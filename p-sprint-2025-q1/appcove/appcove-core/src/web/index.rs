#[approck::http(GET /; AUTH None; return HTML;)]
pub mod page {
    async fn request(app: App, ui: Document) -> Response {
        let auth_fence_system = app.auth_fence_system();

        let google_button_html = match auth_fence_system.provider_data("google") {
            Some(provider_data) => maud::PreEscaped(provider_data.get_button_html(None)),
            None => maud::PreEscaped("".to_string()),
        };

        ui.add_body(maud::html! {
            div.card {
                div.card-body {
                    h1 { "Hello, world!" }
                    p { "Welcome to AppCove!" }
                    p { "Please sign in with one of the following providers:" }
                    div { (google_button_html) }
                }
            }
        });

        Response::HTML(ui.into())
    }
}
