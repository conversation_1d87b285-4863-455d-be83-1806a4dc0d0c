import { SearchSimple } from "@bux/floater/search_simple.mts";

import "./app.mcss";

const $search_btn = document.getElementById("ui-search") as HTMLButtonElement | null;

if ($search_btn !== null) {
    // Singleton state for the search popover
    let search_simple: SearchSimple | null = null;

    $search_btn.addEventListener("click", () => {
        if (search_simple === null) {
            search_simple = new SearchSimple({
                $trigger: $search_btn,
                on_close: () => {
                    search_simple = null;
                },
            });
            search_simple.show();
        }
    });
}
