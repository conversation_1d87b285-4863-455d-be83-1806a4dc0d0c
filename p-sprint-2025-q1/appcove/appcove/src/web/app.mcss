body {
    font-family: 'Montserrat';
    -webkit-font-smoothing: antialiased;
    background-color: lightslategray;
}

#Primary {
    background-color: black;
    box-shadow: inset 0 -6px 9px -7px rgba(0, 0, 0, 0.3);
    border: none;
    border-top: 6px solid rgb(1, 104, 128);
    margin-bottom: 0;
    padding: 0;
}

#Primary .navbar-right > .username > a > img {
    width: 35px;
    height: 35px;
    object-fit: cover;
    border-radius: 50%;
    margin: 0;
}

#Primary .navbar-right > .username > a > span {
    margin: 0px 0px 0px 10px;
    font-size: 12pt;
}

#Primary.navbar .navbar-right .dropdown .dropdown-toggle::after,
#Primary.navbar .navbar-nav .dropdown .dropdown-toggle::after {
    display: none;
}

#Primary .navbar-nav > li > a{
    margin: 0;
    color: #00AAFF;
    text-decoration: none;
}

#Primary .navbar-nav > li > a:focus,
#Primary .navbar-nav > li > a:hover{
    background-color: rgb(1, 104, 128);
    color: white;
}

.navbar .navbar-nav li a:hover {
    background-color: rgb(1, 104, 128);
    color: white;
    box-shadow: none;
}

.navbar-brand img {
    height: auto;
    width: 156px;
    padding: 5px 0;
    display: inline-block;
}

.main-content {
    margin-top: 15px;
}

a {
    color: #3097d1;
    text-decoration: none;
}

a:hover,
a:focus{
    color: rgb(1, 104, 128);
    text-decoration: none;
}

a:focus{
    outline: thin dotted;
    outline: 5px auto -webkit-focus-ring-color;
    outline-offset: -2px;
}

.btn.io-timebutton {
    background-color: #ffaa00;
    color: #000000;
    margin: 8pt 10pt 0 10pt;
    font-size: 14pt;
    padding: 0pt 5pt;
    margin-top: 4pt;
}