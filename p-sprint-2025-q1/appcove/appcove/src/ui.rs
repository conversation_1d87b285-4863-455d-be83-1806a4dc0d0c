use maud::{html, <PERSON><PERSON>, Ren<PERSON>};

// write a struct to represent an html template
pub struct MainDoc {
    title: String,
    head: Vec<Markup>,
    body: Vec<Markup>,
    tail: Vec<Markup>,
    js_list: Vec<String>,
    css_list: Vec<String>,
    status: approck::StatusCode,
    script_list: Vec<String>,
    style_list: Vec<String>,
}

impl Default for MainDoc {
    fn default() -> Self {
        Self {
            title: "AppCove".to_string(),
            head: Vec::new(),
            body: Vec::new(),
            tail: Vec::new(),
            js_list: vec!["/app.js".to_string()],
            css_list: vec!["/app.css".to_string()],
            status: approck::StatusCode::OK,
            script_list: Vec::new(),
            style_list: Vec::new(),
        }
    }
}

impl From<MainDoc> for approck::server::response::HTML {
    fn from(layout: MainDoc) -> approck::server::response::HTML {
        let mut rval = approck::server::response::HTML::new(layout.render().into_string());
        rval.status = layout.status;
        rval
    }
}

// LUKE: talk to us about a better pattern for "inheritance"
impl approck::traits::Document for MainDoc {
    fn add_head(&mut self, chunk: maud::Markup) {
        self.head.push(chunk);
    }

    fn add_body(&mut self, chunk: maud::Markup) {
        self.body.push(chunk);
    }

    fn add_tail(&mut self, chunk: maud::Markup) {
        self.tail.push(chunk);
    }

    fn set_title(&mut self, title: &str) {
        self.title = title.to_string();
    }

    fn add_js(&mut self, module: &str) {
        self.js_list.push(module.to_string());
    }

    fn add_css(&mut self, path: &str) {
        self.css_list.push(path.to_string());
    }

    fn set_status(&mut self, status: approck::StatusCode) {
        self.status = status;
    }

    fn add_script(&mut self, script: &str) {
        self.script_list.push(script.to_string());
    }

    fn add_style(&mut self, style: &str) {
        self.style_list.push(style.to_string());
    }
}

#[rustfmt::skip]
impl maud::Render for MainDoc {
    fn render(&self) -> maud::Markup {
        
        html! {
            (maud::DOCTYPE)
            html {
                head {
                    meta charset="utf-8";
                    meta name="viewport" content="width=device-width, initial-scale=1";
                    // Bootstrap CSS
                    link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous";
                    link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet";
                    link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css" rel="stylesheet";

                    // Import Google Fonts
                    link href="https://fonts.googleapis.com/css?family=Lora:700|Montserrat:400,700" rel="stylesheet";
                    
                    title { (self.title) }

                    @for css in &self.css_list {
                        link href=(css) rel="stylesheet" {}
                    }

                    @for style in &self.style_list {
                        style { (style) }
                    }

                    @for chunk in &self.head {
                        (chunk)
                    }
                }
                body {
                    nav.navbar.navbar-expand-lg.navbar-dark.bg-dark id="Primary" {
                        div.container-fluid {
                            a href="/" class="navbar-brand" {
                                img src="https://appcove.com/assets/appcove_logo_2.png" alt="logo" border="0" {}
                            }                            
                            button.navbar-toggler type="button" data-bs-toggle="collapse" data-bs-target="#Primary_Menu" aria-controls="Primary_Menu" aria-expanded="false" aria-label="Toggle navigation" {
                                span.navbar-toggler-icon {}
                            }
                            div.collapse.navbar-collapse id="Primary_Menu" {
                                ul.navbar-nav.me-auto."mb-2.mb-lg-0" {
                                    li.nav-link {
                                        a.nav-link.active aria-current="page" href="/pm5/" { "Home" }
                                    }
                                    // li.nav-link {
                                    //     a.nav-link.active aria-current="page" href="/pm5/audit/" { "Audit" }
                                    // }
                                    li.nav-link.dropdown {
                                        a.nav-link.dropdown-toggle href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" {
                                            "Support"
                                        }
                                        ul.dropdown-menu aria-labelledby="navbarDropdown" {
                                            li { a.dropdown-item href="#" { "Support" } }
                                            li { a.dropdown-item href="#" { "Another action" } }
                                            li { hr.dropdown-divider {} }
                                            li { a.dropdown-item href="#" { "Something else here" } }
                                        }
                                    }
                                    li.nav-link.dropdown {
                                        a.nav-link.dropdown-toggle href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" {
                                            "Time"
                                        }
                                        ul.dropdown-menu aria-labelledby="navbarDropdown" {
                                            li { a.dropdown-item href="#" { "Time" } }
                                            li { a.dropdown-item href="#" { "Another action" } }
                                            li { hr.dropdown-divider {} }
                                            li { a.dropdown-item href="#" { "Something else here" } }
                                        }
                                    }
                                    li.nav-link.dropdown {
                                        a.nav-link.dropdown-toggle href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" {
                                            "Dashboard"
                                        }
                                        ul.dropdown-menu aria-labelledby="navbarDropdown" {
                                            li { a.dropdown-item href="#" { "Dashboard" } }
                                            li { a.dropdown-item href="#" { "Another action" } }
                                            li { hr.dropdown-divider {} }
                                            li { a.dropdown-item href="#" { "Something else here" } }
                                        }
                                    }
                                    li.nav-link.dropdown {
                                        a.nav-link.dropdown-toggle href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" {
                                            "Workflow"
                                        }
                                        ul.dropdown-menu aria-labelledby="navbarDropdown" {
                                            li { a.dropdown-item href="#" { "Workflow" } }
                                            li { a.dropdown-item href="#" { "Another action" } }
                                            li { hr.dropdown-divider {} }
                                            li { a.dropdown-item href="#" { "Something else here" } }
                                        }
                                    }
                                    li.nav-link.dropdown {
                                        a.nav-link.dropdown-toggle href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" {
                                            "Units"
                                        }
                                        ul.dropdown-menu aria-labelledby="navbarDropdown" {
                                            li { a.dropdown-item href="#" { "Units" } }
                                            li { a.dropdown-item href="#" { "Another action" } }
                                            li { hr.dropdown-divider {} }
                                            li { a.dropdown-item href="#" { "Something else here" } }
                                        }
                                    }
                                    li.nav-link.dropdown {
                                        a.nav-link.dropdown-toggle href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" {
                                            "Meetings"
                                        }
                                        ul.dropdown-menu aria-labelledby="navbarDropdown" {
                                            li { a.dropdown-item href="#" { "Meetings" } }
                                            li { a.dropdown-item href="#" { "Another action" } }
                                            li { hr.dropdown-divider {} }
                                            li { a.dropdown-item href="#" { "Something else here" } }
                                        }
                                    }
                                    li.nav-link.dropdown {
                                        a.nav-link.dropdown-toggle href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" {
                                            "Cog"
                                        }
                                        ul.dropdown-menu aria-labelledby="navbarDropdown" {
                                            li { a.dropdown-item href="#" { "Cog" } }
                                            li { a.dropdown-item href="#" { "Another action" } }
                                            li { hr.dropdown-divider {} }
                                            li { a.dropdown-item href="#" { "Something else here" } }
                                        }
                                    }
                                    li.nav-link {
                                        a.nav-link.active aria-current="page" href="#" { "Docs" }
                                    }
                                    li.nav-link.dropdown {
                                        a.nav-link.dropdown-toggle href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" {
                                            "Settings"
                                        }
                                        ul.dropdown-menu aria-labelledby="navbarDropdown" {
                                            li { a.dropdown-item href="#" { "Settings" } }
                                            li { a.dropdown-item href="#" { "Another action" } }
                                            li { hr.dropdown-divider {} }
                                            li { a.dropdown-item href="#" { "Something else here" } }
                                        }
                                    }
                                    li.nav-link {
                                        button.btn.border-secondary.btn-light id="ui-search" {
                                            i.bi.bi-search {}
                                            " Search"
                                        }
                                    }
                                    li.nav-link {
                                        button.btn.btn-secondary.btn-sm.io-timebutton data-app-autoinit-require="#" {
                                            i.bi.bi-clock {}
                                        }
                                    }
                                }
                                ul.nav.navbar-right.d-flex {
                                    li.dropdown.username {
                                        a.dropdown-bs-toggle aria-label="Edissa Aringo" href="#" data-bs-toggle="dropdown" {
                                            img src="https://lh3.googleusercontent.com/a/ACg8ocIuuGQ0eSO_6n9uYPzDb8Nm-HYAXBpf4c9dwpJ-E9zQ=s96-c";
                                            span { "Edissa Aringo" }
                                        }
                                        ul.dropdown-menu.dropdown-menu-limit-height {
                                            li.dropdown-item {
                                                a href="#" dropdown-item { i.fa.fa-sign-out {} " Logout" }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    div.container-fluid.main-content {  //Made the main content into a container-fluid. If you create a new page, don't forget to wrap it in a div.container
                        @for chunk in &self.body {
                            (chunk)
                        }
                    }
                }

                // Bootstrap JS
                script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous" {}
                
                @for module in &self.js_list {
                    script src=(module) type="module" {}
                }

                @for script in &self.script_list {
                    script { (script) }
                }
            }
        }
    }
}
