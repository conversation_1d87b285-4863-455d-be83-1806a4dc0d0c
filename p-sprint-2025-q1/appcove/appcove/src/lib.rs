#[path = "libλ.rs"]
mod libλ;

pub mod types {

    #[approck::appconfig]
    pub struct AppConfig {
        pub redis: granite_redis::RedisConfig,
        pub postgres: granite_postgres::PostgresConfig,
        pub webserver: approck::server::WebServerConfig,
        pub appcove_pm5: appcove_pm5::AppCovePM5Config,
        pub auth_fence: auth_fence::types::ModConfig,
    }

    pub struct AppSystem {
        pub webserver: approck::server::WebServerSystem,
        pub redis: granite_redis::RedisSystem,
        pub postgres: granite_postgres::PostgresSystem,
        pub appcove_pm5: appcove_pm5::AppCovePM5System,
        pub auth_fence: auth_fence::types::ModSystem,
    }
    #[derive(Debug)]
    pub enum AppIdentity {
        Anonymous,
        User(auth_fence::BasicUserInfo),
    }

    impl crate::AppIdentity for AppIdentity {}
    impl appcove_pm5::AppIdentity for AppIdentity {}
    impl appcove_core::AppIdentity for AppIdentity {}
    impl appcove_play::AppIdentity for AppIdentity {}
    impl auth_fence::AppIdentity for AppIdentity {}
    impl bux::AppIdentity for AppIdentity {
        fn role(&self) -> String {
            match self {
                AppIdentity::User(_) => "user".to_string(),
                AppIdentity::Anonymous => "anonymous".to_string(),
            }
        }
        fn name(&self) -> Option<String> {
            match self {
                AppIdentity::User(user) => Some(format!("{} {}", user.first_name, user.last_name)),
                AppIdentity::Anonymous => None,
            }
        }
        fn email(&self) -> Option<String> {
            match self {
                AppIdentity::User(user) => Some(user.email.to_string()),
                AppIdentity::Anonymous => None,
            }
        }
        fn avatar_uri(&self) -> Option<String> {
            match self {
                AppIdentity::User(user) => Some(user.avatar_uri.to_string()),
                AppIdentity::Anonymous => None,
            }
        }
    }

    impl crate::AppSystem for AppSystem {}
    impl appcove_core::AppSystem for AppSystem {}
    impl appcove_play::AppSystem for AppSystem {}

    impl granite_postgres::PostgresModule for AppSystem {
        async fn postgres_dbcx(&self) -> granite::Result<granite_postgres::DBCX> {
            self.postgres.get_dbcx().await
        }
    }

    impl granite_redis::RedisModule for AppSystem {
        async fn redis_dbcx(&self) -> granite::Result<granite_redis::RedisCX<'_>> {
            self.redis.get_dbcx().await
        }
    }

    impl approck::server::WebServerModule for AppSystem {
        fn webserver_system(&self) -> &approck::server::WebServerSystem {
            &self.webserver
        }

        async fn webserver_route(
            &'static self,
            req: approck::server::Request,
        ) -> granite::Result<approck::server::response::Response> {
            crate::libλ::router(self, req).await
        }
    }

    impl appcove_pm5::AppSystem for AppSystem {
        fn appcove_pm5_system(&self) -> &appcove_pm5::AppCovePM5System {
            &self.appcove_pm5
        }
    }

    impl auth_fence::AppSystem for AppSystem {
        /*
        fn data(&self) -> &auth_fence::ModData { &self.auth_fence }
        fn data_mut(&mut self) -> &mut auth_fence::ModData { &mut self.auth_fence }
        */
        fn logout_url<'a>(&self) -> &'a str {
            "/logout"
        }
        fn auth_fence_system(&self) -> &auth_fence::types::ModSystem {
            &self.auth_fence
        }
        fn after_login_next_url<'a>(&self, next_uri: Option<&'a str>) -> &'a str {
            next_uri.unwrap_or("/pm5/")
        }
    }

    bux::document! {
        pub struct Document {}

        impl Document {
            pub fn new(
                _app: &'static impl crate::AppSystem,
                _identity: &impl crate::AppIdentity,
                _req: &approck::server::Request,
            ) -> Self {
                use bux::document::Base;

                let mut rval = Self {
                    ..Default::default()
                };

                rval.add_js("/app.js");
                rval.add_css("/app.css");

                rval
            }
        }

        impl bux::document::Base for Document {

            fn render_body(&self) -> maud::Markup {
                maud::html!(

                    nav.navbar.navbar-expand-lg.navbar-dark.bg-dark id="Primary" {
                        div.container-fluid {
                            a href="/" class="navbar-brand" {
                                img src="https://appcove.com/assets/appcove_logo_2.png" alt="logo" border="0" {}
                            }
                            button.navbar-toggler type="button" data-bs-toggle="collapse" data-bs-target="#Primary_Menu" aria-controls="Primary_Menu" aria-expanded="false" aria-label="Toggle navigation" {
                                span.navbar-toggler-icon {}
                            }
                            div.collapse.navbar-collapse id="Primary_Menu" {
                                ul.navbar-nav.me-auto."mb-2.mb-lg-0" {
                                    li.nav-link {
                                        a.nav-link.active aria-current="page" href="/pm5/" { "Home" }
                                    }
                                    // li.nav-link {
                                    //     a.nav-link.active aria-current="page" href="/pm5/audit/" { "Audit" }
                                    // }
                                    li.nav-link.dropdown {
                                        a.nav-link.dropdown-toggle href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" {
                                            "Support"
                                        }
                                        ul.dropdown-menu aria-labelledby="navbarDropdown" {
                                            li { a.dropdown-item href="#" { "Support" } }
                                            li { a.dropdown-item href="#" { "Another action" } }
                                            li { hr.dropdown-divider {} }
                                            li { a.dropdown-item href="#" { "Something else here" } }
                                        }
                                    }
                                    li.nav-link.dropdown {
                                        a.nav-link.dropdown-toggle href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" {
                                            "Time"
                                        }
                                        ul.dropdown-menu aria-labelledby="navbarDropdown" {
                                            li { a.dropdown-item href="#" { "Time" } }
                                            li { a.dropdown-item href="#" { "Another action" } }
                                            li { hr.dropdown-divider {} }
                                            li { a.dropdown-item href="#" { "Something else here" } }
                                        }
                                    }
                                    li.nav-link.dropdown {
                                        a.nav-link.dropdown-toggle href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" {
                                            "Dashboard"
                                        }
                                        ul.dropdown-menu aria-labelledby="navbarDropdown" {
                                            li { a.dropdown-item href="#" { "Dashboard" } }
                                            li { a.dropdown-item href="#" { "Another action" } }
                                            li { hr.dropdown-divider {} }
                                            li { a.dropdown-item href="#" { "Something else here" } }
                                        }
                                    }
                                    li.nav-link.dropdown {
                                        a.nav-link.dropdown-toggle href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" {
                                            "Workflow"
                                        }
                                        ul.dropdown-menu aria-labelledby="navbarDropdown" {
                                            li { a.dropdown-item href="#" { "Workflow" } }
                                            li { a.dropdown-item href="#" { "Another action" } }
                                            li { hr.dropdown-divider {} }
                                            li { a.dropdown-item href="#" { "Something else here" } }
                                        }
                                    }
                                    li.nav-link.dropdown {
                                        a.nav-link.dropdown-toggle href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" {
                                            "Units"
                                        }
                                        ul.dropdown-menu aria-labelledby="navbarDropdown" {
                                            li { a.dropdown-item href="#" { "Units" } }
                                            li { a.dropdown-item href="#" { "Another action" } }
                                            li { hr.dropdown-divider {} }
                                            li { a.dropdown-item href="#" { "Something else here" } }
                                        }
                                    }
                                    li.nav-link.dropdown {
                                        a.nav-link.dropdown-toggle href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" {
                                            "Meetings"
                                        }
                                        ul.dropdown-menu aria-labelledby="navbarDropdown" {
                                            li { a.dropdown-item href="#" { "Meetings" } }
                                            li { a.dropdown-item href="#" { "Another action" } }
                                            li { hr.dropdown-divider {} }
                                            li { a.dropdown-item href="#" { "Something else here" } }
                                        }
                                    }
                                    li.nav-link.dropdown {
                                        a.nav-link.dropdown-toggle href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" {
                                            "Cog"
                                        }
                                        ul.dropdown-menu aria-labelledby="navbarDropdown" {
                                            li { a.dropdown-item href="#" { "Cog" } }
                                            li { a.dropdown-item href="#" { "Another action" } }
                                            li { hr.dropdown-divider {} }
                                            li { a.dropdown-item href="#" { "Something else here" } }
                                        }
                                    }
                                    li.nav-link {
                                        a.nav-link.active aria-current="page" href="#" { "Docs" }
                                    }
                                    li.nav-link.dropdown {
                                        a.nav-link.dropdown-toggle href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" {
                                            "Settings"
                                        }
                                        ul.dropdown-menu aria-labelledby="navbarDropdown" {
                                            li { a.dropdown-item href="#" { "Settings" } }
                                            li { a.dropdown-item href="#" { "Another action" } }
                                            li { hr.dropdown-divider {} }
                                            li { a.dropdown-item href="#" { "Something else here" } }
                                        }
                                    }
                                    li.nav-link {
                                        button.btn.border-secondary.btn-light id="ui-search" {
                                            i.bi.bi-search {}
                                            " Search"
                                        }
                                    }
                                    li.nav-link {
                                        button.btn.btn-secondary.btn-sm.io-timebutton data-app-autoinit-require="#" {
                                            i.bi.bi-clock {}
                                        }
                                    }
                                }
                                ul.nav.navbar-right.d-flex {
                                    li.dropdown.username {
                                        a.dropdown-bs-toggle aria-label="Edissa Aringo" href="#" data-bs-toggle="dropdown" {
                                            img src="https://lh3.googleusercontent.com/a/ACg8ocIuuGQ0eSO_6n9uYPzDb8Nm-HYAXBpf4c9dwpJ-E9zQ=s96-c";
                                            span { "Edissa Aringo" }
                                        }
                                        ul.dropdown-menu.dropdown-menu-limit-height {
                                            li.dropdown-item {
                                                a href="#" dropdown-item { i.fa.fa-sign-out {} " Logout" }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    div.container-fluid.main-content {  //Made the main content into a container-fluid. If you create a new page, don't forget to wrap it in a div.container
                        (bux::document::Base::render_body_inner(self))
                    }

                )
            }
        }

        impl appcove_core::Document for Document {}
        impl appcove_play::Document for Document {}
        impl appcove_pm5::Document for Document {}
        impl auth_fence::Document for Document {}
        impl bux::document::PageNav for Document {}
    }
}

pub trait AppSystem {}
pub trait AppIdentity: bux::AppIdentity {}

pub async fn init(_app: &'static crate::types::AppSystem) {
    println!("init appcove!");
}

pub async fn authenticate_request(
    _app: &'static crate::types::AppSystem,
    _req: &approck::server::Request,
) -> granite::Result<crate::types::AppIdentity> {
    Ok(crate::types::AppIdentity::Anonymous)
}
