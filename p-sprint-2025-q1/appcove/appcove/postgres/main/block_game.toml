


[[table]]
table = "block_game"
allow_additional_fields = true
primary_key = ["block_game_mnid"]

[[field]]
table = "block_game"
field = "block_game_mnid"

[[field]]
table = "block_game"
field = "name"
ptype = "string"
fkey = {table="block_game", field="name", on_delete="cascade", on_update="cascade"}

[[field]]
table = "block_game"
field = "block_game_history_mnid"
ptype = "mnid"

[[fkey]]
name = "block_game_history>>foobar"
source_table = "block_game"
source_fields = ["block_game_history_mnid"]
target_table = "another_schema.block_game_history"
target_fields = ["block_game_history_mnid"]

[[field]]

[[tree-sturcture]]
table = "foo"
pkey = "foo_id"
parent = "foo_id_parent"
pathcache = "foo_id_pathcache"
use_gin = true


[[function]]
name = "foobar"
args = ["a", "b", "c"]
rval = "d"

[[index]]
name = "block_game_mnid"
table = "block_game"
itype = "btree"
fields = ["block_game_mnid"]

