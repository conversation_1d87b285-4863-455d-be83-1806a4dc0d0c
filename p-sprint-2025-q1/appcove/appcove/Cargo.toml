[package]
name = "appcove"
version = "0.1.0"
edition = "2021"

[package.metadata.acp]
app.port = 3003

extends = ["bux", "appcove-core", "appcove-pm5", "appcove-play", "auth-fence"]


#app.webserver.port = 3003

#site.main = { domain = "www.appcove.com", aliases = ["appcove.com"] }
#site.play = { domain = "play.appcove.com" }

#extend.appcove-pm5 = { sites = ["main"] }
#extend.appcove-play = { sites = ["play"] }
#extend.email-ray = { sites = ["main"] }


[dependencies]
approck = { workspace = true }
auth-fence = { workspace = true }
bux = { workspace = true }
granite = { workspace = true }
granite-postgres = { workspace = true }
granite-redis = { workspace = true }


appcove-pm5 = { path = "../appcove-pm5" }
appcove-play = { path = "../appcove-play" }
appcove-core = { path = "../appcove-core" }

clap = { workspace = true, features = ["derive"] }
tokio = { workspace = true, features = ["full"] }
maud = { workspace = true }
serde = { workspace = true, features = ["derive"] }
toml = { workspace = true }