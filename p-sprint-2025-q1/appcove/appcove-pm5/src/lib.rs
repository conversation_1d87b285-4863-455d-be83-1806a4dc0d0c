pub mod web;

#[derive(Debug, serde::Deserialize)]
pub struct AppCovePM5Config {
    pub name: String,
}

impl AppCovePM5Config {
    pub async fn into_system(self) -> granite::Result<AppCovePM5System> {
        Ok(AppCovePM5System { config: self })
    }
}

#[allow(dead_code)]
pub struct AppCovePM5System {
    config: AppCovePM5Config,
}

impl AppCovePM5System {
    pub fn name(&self) -> &str {
        &self.config.name
    }
}

pub trait AppSystem: approck::server::WebServerModule + auth_fence::AppSystem {
    fn appcove_pm5_system(&self) -> &AppCovePM5System;
}

pub trait AppIdentity {}

pub trait Document: bux::document::Base {}

pub async fn init<APP: AppSystem>(app: &'static APP) {
    let system = app.appcove_pm5_system();
    println!("init appcove-pm5! {}", system.name());

    // spawn a thread which prints a message every 5 seconds
    tokio::spawn(async move {
        let mut i = 0;

        loop {
            i += 1;
            tokio::time::sleep(tokio::time::Duration::from_secs(1)).await;
            println!("appcove-pm5: still alive for {} seconds", i);
        }
    });
}
