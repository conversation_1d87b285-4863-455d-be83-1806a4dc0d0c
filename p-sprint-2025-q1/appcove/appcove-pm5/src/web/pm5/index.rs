#[approck::http(GET|POST /pm5/; AUTH None; return HTML|Redirect;)]
pub mod page {
    async fn request(req: Request, app: App, ui: Document) -> Result<Response> {
        let foobad = app.appcove_pm5_system().name();

        if req.is_post() {
            return Ok(Response::Redirect(req.path().into()));
        }

        ui.add_body(maud::html! {
            div.card {
                div.card-body {
                    "hello pm5!  foo is " code {(foobad)}

                    form method="post" {
                        input type="submit" value="submit";
                    }
                }
            }
        });

        Ok(Response::HTML(ui.into()))
    }
}
