[package]
name = "appcove-pm5"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html
[package.metadata.acp]
extends = ["appcove-core", "auth-fence"]
module = {}

[dependencies]
approck = { workspace = true }
auth-fence = { workspace = true }
bux = { workspace = true }
granite = { workspace = true }
granite-postgres = { workspace = true }
granite-redis = { workspace = true }

appcove-core = { path = "../appcove-core" }

tokio = { workspace = true, features = ["full"] }
maud = { workspace = true }
serde = { workspace = true, features = ["derive"] }
