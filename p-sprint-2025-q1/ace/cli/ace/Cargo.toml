[package]
name = "ace"
version = "0.1.0"
edition = "2021"


[dependencies]
ace-check = { path = "../../core/ace-check" }
ace-core = { path = "../../core/ace-core" }
ace-db = { path = "../../core/ace-db" }
ace-graph = { path = "../../core/ace-graph" }
ace-proc = { path = "../../core/ace-proc" }
garbage = { path = "../../shared/garbage"}

approck = { workspace = true }
bux = { workspace = true }
granite = { workspace = true }
granite-postgres = { workspace = true }
granite-redis = { workspace = true }
maud = { workspace = true }
anyhow = { workspace = true }
atty = { workspace = true }
chrono = { workspace = true }
clap = { workspace = true, features = ["derive"] }
clap-markdown = { workspace = true }
comfy-table = { workspace = true }
console = { workspace = true }
data-encoding = { workspace = true }
dialoguer = { workspace = true }
dotenv = { workspace = true }
error-stack = { workspace = true }
futures = { workspace = true }
git2 = { workspace = true }
glob = { workspace = true }
hostname = { workspace = true }
indicatif = { workspace = true }
ipnetwork = { workspace = true }
nix = { workspace = true , features = ["process"] }
regex = { workspace = true }
reqwest = { workspace = true }
rustls = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
serde_toml = { workspace = true }
serde_with = { workspace = true }
serde_yaml = { workspace = true }
sha2 = { workspace = true }
shell-quote = { workspace = true }
shell-words = { workspace = true }
tokio = { workspace = true }
toml = { workspace = true }
tracing-subscriber = { workspace = true, features = ["env-filter"] }
tracing.workspace = true
trust-dns-client = { workspace = true }
trust-dns-proto = { workspace = true }
urlencoding = { workspace = true }
zip = { workspace = true }
