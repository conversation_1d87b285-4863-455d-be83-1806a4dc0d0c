use ace_graph::GraphKeyExt;
use comfy_table::{presets, ContentArrangement, Table};

pub async fn list(verbose: bool, ace_db_app: &ace_db::App) {
    let instances_result =
        ace_graph::ins::select_result(&ace_graph::InstanceFilter::All, ace_db_app).await;
    let mut errors = Vec::new();
    let mut instances_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid instance entries
    match instances_result {
        Ok(instances) => {
            for instance in instances {
                match instance {
                    Ok(instance) => instances_sorted.push(instance),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching instances: {:#?}", e);
            return;
        }
    }

    // Sort instances by their graphkey
    instances_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for instance in instances_sorted {
            println!("\n{:#?}", instance);
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec![
            "Name",
            "Graphkey",
            "AMI Graphkey",
            "Instance Type",
            "Volume Size",
            "Subnet",
        ]);
        for instance in instances_sorted {
            let row = vec![
                instance.name.to_string(),
                instance.graphkey.to_string(),
                instance.ami_graphkey.to_string(),
                instance.instance_type.to_string(),
                instance.volume_size.to_string(),
                instance.subnet.to_string(),
            ];
            table.add_row(row);
        }

        println!("{}", table);
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("Error: {:#?}", error);
        }
    }
}
