use ace_graph::GraphKeyExt;
use comfy_table::{presets, ContentArrangement, Table};

pub async fn list(verbose: bool) {
    let iam_role_policies_result =
        ace_graph::iam_role_policy::select_result(ace_graph::IamPolicyFilter::All).await;
    let mut errors = Vec::new();
    let mut iam_role_policies_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid IAM role policy entries
    match iam_role_policies_result {
        Ok(iam_role_policies) => {
            for iam_role_policy in iam_role_policies {
                match iam_role_policy {
                    Ok(iam_role_policy) => iam_role_policies_sorted.push(iam_role_policy),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching IAM role policies: {:#?}", e);
            return;
        }
    }

    // Sort IAM role policies by their graphkey
    iam_role_policies_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for iam_role_policy in iam_role_policies_sorted {
            println!("\n{:#?}", iam_role_policy);
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey"]);
        for iam_role_policy in iam_role_policies_sorted {
            table.add_row(vec![iam_role_policy.graphkey.to_string()]);
        }

        println!("{}", table);
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{:#?}", error);
        }
    }
}
