use ace_check::Check<PERSON>eyTrait;
use comfy_table::{presets, ContentArrangement, Table};
use error_stack::ResultExt;

pub type NsValues = Vec<String>;

#[derive(Debug)]
pub enum NsStatus {
    MissingCanonicalRecords,
    Mismatch {
        missing_records: NsValues,
        extra_records: NsValues,
    },
    Match,
    Uncalculated,
    Error(ErrorStack),
}

#[derive(Debug)]
pub struct NsCompare {
    pub domain: String,
    pub pass: bool,
    pub canonical_entries: Vec<String>,
    pub actual_entries: Vec<String>,
    pub actions_needed: Vec<String>,
    pub status: NsStatus,
}

impl NsCompare {
    pub fn gen_status_msg(&self) -> String {
        match &self.status {
            NsStatus::MissingCanonicalRecords => "No Canonical Records.".to_string(),
            NsStatus::Mismatch {
                missing_records,
                extra_records,
            } => ("Mismatch:\n\nAdd Missing Records:\n".to_string()
                + &missing_records.join("\n")
                + "\n\nRemove Extra Records:\n"
                + &extra_records.join("\n"))
                .to_string(),
            NsStatus::Match => "None: All Records Match!".to_string(),
            NsStatus::Error(err) => format!("Error: {:?}", err),
            NsStatus::Uncalculated => "Uncalculated".to_string(),
        }
    }
}

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    DeserializeDnsResponse,
    GetActualPrivateNS,
    GetActualPublicNS,
    GetConfig,
    GetDataTerraformOutput,
    GetResponseText,
    MakeGetRequest,
    NoActualNsRecordsFound,
}

pub async fn list_checks(ace_db_app: &ace_db::App) {
    let checks = ace_check::list_checks(ace_db_app).await;

    // Print out check keys (using comfy_table)
    let mut table = Table::new();
    table.set_content_arrangement(ContentArrangement::Dynamic);
    if atty::is(atty::Stream::Stdout) {
        table.load_preset(presets::UTF8_FULL_CONDENSED);
    } else {
        table.load_preset(presets::NOTHING);
    }
    table.set_header(vec!["Check Key", "Subject", "Check Type"]);

    for check in checks {
        let check_key = check.to_string();
        let check_object = check.create();
        table.add_row(vec![
            check_key,
            check_object.get_subject_string(ace_db_app).await,
            check_object.get_check_type(),
        ]);
    }

    println!("{table}");
}

pub async fn run(ace_db_app: &ace_db::App) -> error_stack::Result<(), ErrorStack> {
    // Code converted from old ace2-checks.

    // Get Canonical NS records:
    let data_terraform_output = ace_db::data::terraform_output::get(&ace_db_app.data_path)
        .await
        .change_context(ErrorStack::GetDataTerraformOutput)?;
    let canonical_pvt_ns = data_terraform_output.dns_private_domain_ns_list;
    let canonical_pub_ns = data_terraform_output.dns_public_domain_ns_list;

    // Get private/public subdomains:
    let config = ace_graph::config::get(&ace_graph::Config::EtcConfig, ace_db_app)
        .await
        .change_context(ErrorStack::GetConfig)?;
    let private_subdomain_name = config.private_subdomain_name;
    let public_subdomain_name = config.public_subdomain_name;

    // Collect Actual NS records:
    let pvt_ns_values = ace_core::dns::get_ns_values(&private_subdomain_name)
        .await
        .change_context(ErrorStack::GetActualPrivateNS)?;
    let pub_ns_values = ace_core::dns::get_ns_values(&public_subdomain_name)
        .await
        .change_context(ErrorStack::GetActualPublicNS)?;

    // Compare:
    let pvt_ns_compare = compare_ns_values(
        canonical_pvt_ns.unwrap_or_default(),
        pvt_ns_values,
        &private_subdomain_name,
    );
    let pub_ns_compare = compare_ns_values(
        canonical_pub_ns.unwrap_or_default(),
        pub_ns_values,
        &public_subdomain_name,
    );

    // Print results:
    println!("Private NS Check:");
    println!("{:#?}", pvt_ns_compare);
    println!("\n\nPublic NS Check:");
    println!("{:#?}", pub_ns_compare);

    Ok(())
}

fn compare_ns_values(canonical_ns: NsValues, actual_ns: NsValues, domain: &str) -> NsCompare {
    // Check for empty lists:
    if canonical_ns.is_empty() {
        return NsCompare {
            domain: domain.to_string(),
            canonical_entries: canonical_ns,
            actual_entries: actual_ns,
            status: NsStatus::MissingCanonicalRecords,
            actions_needed: vec![],
            pass: false,
        };
    }

    if actual_ns.is_empty() {
        return NsCompare {
            domain: domain.to_string(),
            canonical_entries: canonical_ns,
            actual_entries: actual_ns,
            pass: false,
            status: NsStatus::Error(ErrorStack::NoActualNsRecordsFound),
            actions_needed: vec![],
        };
    }

    // Calculate/collect missing and extra records
    let mut missing_records = NsValues::new();
    let mut extra_records = NsValues::new();

    for record in &canonical_ns {
        if !actual_ns.contains(record) {
            missing_records.push(record.clone());
        }
    }

    for record in &actual_ns {
        if !canonical_ns.contains(record) {
            extra_records.push(record.to_string());
        }
    }

    // Sort before stuffing into the comparison struct
    let mut sorted_canonical = canonical_ns.clone();
    let mut sorted_actual = actual_ns.clone();

    sorted_canonical.sort();
    sorted_actual.sort();

    extra_records.sort();
    missing_records.sort();

    // Do they match?
    let ns_status = match (missing_records.len(), extra_records.len()) {
        (0, 0) => NsStatus::Match,
        _ => NsStatus::Mismatch {
            missing_records,
            extra_records,
        },
    };

    NsCompare {
        domain: domain.to_string(),
        canonical_entries: sorted_canonical,
        actual_entries: sorted_actual,
        pass: true,
        actions_needed: vec![],
        status: ns_status,
    }
}
