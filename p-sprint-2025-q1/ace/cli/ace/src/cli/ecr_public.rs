use ace_graph::GraphKeyExt;
use comfy_table::{presets, ContentArrangement, Table};

pub async fn list(verbose: bool, ace_db_app: &ace_db::App) {
    let pub_ecrs =
        match ace_graph::ecr_public::select_result(&ace_graph::EcrPublicFilter::All, ace_db_app)
            .await
        {
            Ok(ecrs) => ecrs,
            Err(e) => {
                println!("{:#?}", e);
                return;
            }
        };

    let mut pub_ecrs_sorted = Vec::new();
    let mut errors = Vec::new();

    for ecr in pub_ecrs {
        match ecr {
            Ok(ecr) => pub_ecrs_sorted.push(ecr),
            Err(e) => errors.push(e),
        }
    }

    pub_ecrs_sorted.sort_by(|a, b| {
        a.graphkey
            .serialize_dashed()
            .cmp(&b.graphkey.serialize_dashed())
    });

    if verbose {
        for ecr in pub_ecrs_sorted {
            println!("{:#?}", ecr);
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);

        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey", "ARN"]);

        for pub_ecr in pub_ecrs_sorted {
            table.add_row(vec![pub_ecr.graphkey.serialize(), pub_ecr.arn]);
        }

        println!("{}", table);
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{:#?}", error);
        }
    }
}
