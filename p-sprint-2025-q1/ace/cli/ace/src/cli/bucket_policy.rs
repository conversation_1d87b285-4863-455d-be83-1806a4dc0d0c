use ace_graph::GraphKeyExt;
use comfy_table::{presets, ContentArrangement, Table};

pub async fn list(verbose: bool) {
    let bucket_policies_result =
        ace_graph::bucket_policy::select_result(ace_graph::BucketPolicyFilter::All).await;
    let mut errors = Vec::new();
    let mut bucket_policies_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid bucket policies
    match bucket_policies_result {
        Ok(bucket_policies) => {
            for bucket_policy in bucket_policies {
                match bucket_policy {
                    Ok(bucket_policy) => bucket_policies_sorted.push(bucket_policy),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching bucket policies: {:#?}", e);
            return;
        }
    }

    // Sort bucket policies by their graphkey
    bucket_policies_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for bucket_policy in bucket_policies_sorted {
            println!("\n{:#?}", bucket_policy);
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey"]);
        for bucket_policy in bucket_policies_sorted {
            table.add_row(vec![bucket_policy.graphkey.to_string()]);
        }

        println!("{}", table);
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{:#?}", error);
        }
    }
}
