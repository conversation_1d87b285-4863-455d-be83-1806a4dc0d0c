use ace_core::gpg::check_gpg_installation;
use console::Term;
use dialoguer::{theme::ColorfulTheme, Input, Password, Select};
use garbage::{CNSL, STSL};
use indicatif::MultiProgress;
use regex::Regex;
use shell_words::quote;
use std::fs::File;
use std::io::prelude::*;
use std::os::unix::fs::PermissionsExt;
use tokio::try_join;

use error_stack::ResultExt;

pub const MOUNT_INSTRUCTION: &str = "Run 'ace secure mount' to mount this filesystem";

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    AutoCommit,
    CheckSecureMountState,
    CheckGpgInstallation,
    ConvertGetAliasCmdOutputToString,
    CopyAceBinaryToBin,
    CreateAccountTomlFile,
    CreateConfigTomlFile,
    CreateDirectory,
    CreateFile,
    CreatePackerPluginFile,
    DownloadEasyTLSBin,
    DownloadGocyrptfsTarball,
    DownloadPackerBin,
    DownloadTerraformBin,
    ExampleString,
    ExecuteCommand(String),
    ExecuteGetAliasPathCommand,
    ExecuteTouchCommand,
    ExtractGocryptfsTarball,
    ExtractNebulaTarball,
    GenerateAceKeypair,
    GetRunningAceBinaryPath,
    GpgNotInstalled,
    InitGit,
    PrintMPBMessage,
    RemoveFile,
    Rename,
    SecureAlreadyMounted,
    SecureMount,
    SecureUnmount,
    SelectInteract,
    UserCancelled,
    WriteAccountFile,
    WriteAutoUpdateScriptFile,
    WriteAutoUpdateScriptFilePermissions,
    WriteConfigFile,
    WriteFile,
    WriteVersionFile,
}

fn version_2_0_0_deprecations(env: &ace_core::Application) -> error_stack::Result<(), ErrorStack> {
    move_and_remove(
        env,
        &[
            ("config.yaml", Some("etc/config.yaml")),
            ("etc/config.yaml", Some("etc/config.toml")),
            ("GIT_COMMIT", Some("etc/version.txt")),
            ("GIT_HASH", None),
        ],
    )?;

    Ok(())
}

/// Fix for the packer plugin config file (v1.10+ no longer automatically install plugins)
async fn create_packer_plugin_config_file(
    packer_config_path: &std::path::Path,
) -> error_stack::Result<(), ErrorStack> {
    let contents = CNSL!(
        r#"
        packer {
            required_plugins {
                amazon = {
                    version = ">= 1.3.3"
                    source  = "github.com/hashicorp/amazon"
                }
            }
        }
    "#
    );

    tokio::fs::write(packer_config_path, contents)
        .await
        .change_context(ErrorStack::CreatePackerPluginFile)?;

    Ok(())
}

fn move_and_remove(
    env: &ace_core::Application,
    moves: &[(&str, Option<&str>)],
) -> error_stack::Result<(), ErrorStack> {
    for (p1, p2) in moves {
        match p2 {
            Some(p2) => {
                if env.path.join(p1).exists() && !env.path.join(p2).exists() {
                    println!("Moving {} to {}", p1, p2);
                    std::fs::rename(env.path.join(p1), env.path.join(p2))
                        .change_context(ErrorStack::Rename)?;
                }
            }
            None => {
                if env.path.join(p1).exists() {
                    println!("removing {}", p1);
                    std::fs::remove_file(env.path.join(p1))
                        .change_context(ErrorStack::RemoveFile)?;
                }
            }
        }
    }

    Ok(())
}

fn create_dir_if_not_exists(path: &std::path::PathBuf) -> error_stack::Result<(), ErrorStack> {
    if !path.exists() {
        std::fs::create_dir_all(path).change_context(ErrorStack::CreateDirectory)?;
    }
    Ok(())
}

fn create_empty_file_if_not_exists(
    path: &std::path::PathBuf,
) -> error_stack::Result<(), ErrorStack> {
    if !path.exists() {
        std::fs::File::create(path).change_context(ErrorStack::CreateFile)?;
    }
    Ok(())
}

pub async fn run(app: &ace_core::Application) -> error_stack::Result<(), ErrorStack> {
    let mpb: MultiProgress = MultiProgress::new();

    println!("Initializing environment in {}", app.path.display());

    // Check if GPG is installed
    check_gpg_installation(app).change_context(ErrorStack::CheckGpgInstallation)?;

    // must not be mounted to continue with init
    if app
        .is_secure_mounted()
        .change_context(ErrorStack::CheckSecureMountState)?
    {
        println!("Note: secure filesystem is mounted.  Unmounting...");
        ace_core::gocryptfs::secure_unmount(app).change_context(ErrorStack::SecureUnmount)?;
    }

    // Double check that it is not mounted
    if app
        .is_secure_mounted()
        .change_context(ErrorStack::CheckSecureMountState)?
    {
        println!("Error: you cannot continue until the secure filesystem is unmounted");
        return Err(ErrorStack::SecureAlreadyMounted.into());
    }

    println!("Placing .gitignore");
    {
        // Write out gitignore
        let mut file =
            File::create(app.path.join(".gitignore")).change_context(ErrorStack::CreateFile)?;
        #[rustfmt::skip]
        write!(file, "{}", STSL!(r#"
            /terraform/terraform.tfstate.backup
            /terraform/terraform.tfstate.*.backup
            /tmp/
            /secure/
            *.swp
            *.swo
            /.agit
        "#)).change_context(ErrorStack::WriteFile)?;
    }

    // Set git ident
    {
        let mut cmd = std::process::Command::new("git");
        cmd.arg("config");
        cmd.arg("user.email");
        cmd.arg(format!(
            "{}@{}",
            &app.path_accountkey_region.region, &app.path_accountkey_region.account_key
        ));
        cmd.output()
            .change_context(ErrorStack::ExecuteCommand("git config".to_string()))?;

        let mut cmd = std::process::Command::new("git");
        cmd.arg("config");
        cmd.arg("user.name");
        cmd.arg("ace");
        cmd.output()
            .change_context(ErrorStack::ExecuteCommand("git config".to_string()))?;
    }

    create_dir_if_not_exists(&app.ace_db_app.asset_path)?;
    create_dir_if_not_exists(&app.bin_path)?;
    create_dir_if_not_exists(&app.ace_db_app.etc_path)?;
    create_dir_if_not_exists(&app.temp_path)?;
    create_dir_if_not_exists(&app.ace_db_app.data_path)?;
    //NOTE: data_ca_path is created by `ace vpn init`
    create_dir_if_not_exists(&app.ace_db_app.vpn_path)?;
    create_dir_if_not_exists(&app.ace_db_app.docker_path)?;
    create_dir_if_not_exists(&app.packer_path)?;
    create_dir_if_not_exists(&app.terraform_path)?;
    create_dir_if_not_exists(&app.secure_mountpoint)?;
    create_dir_if_not_exists(&app.secure_encrypted)?;
    create_packer_plugin_config_file(&app.packer_config_path).await?;

    // important this comes AFTER creating the mountpoint directory
    create_empty_file_if_not_exists(&app.secure_mountpoint.join(MOUNT_INSTRUCTION))?;

    // Run command to copy ace binary to /bin
    {
        if !app.bin_executable_path.exists() {
            let running_ace_path =
                std::env::current_exe().change_context(ErrorStack::GetRunningAceBinaryPath)?;

            // copy the ace binary to /bin
            std::fs::copy(running_ace_path, &app.bin_executable_path)
                .change_context(ErrorStack::CopyAceBinaryToBin)?;
        }
    }

    println!("Placing agit");
    {
        let agit_path = app.path.join("bin/agit");
        let agit_ignore_path = app.path.join(".agitignore");

        // Write out gitignore
        {
            let mut file = File::create(&agit_path).change_context(ErrorStack::CreateFile)?;
            #[rustfmt::skip]
            write!(file, "{}", STSL!(r#"
                #!/bin/bash
                ## THIS IS PLACED BY ace init ##

                # fail on errors
                set -e

                # find root git dir
                gitdir=$(git rev-parse --show-toplevel)

                # switch to it
                cd $gitdir

                # run git command with args
                exec git --git-dir=./.agit --work-tree=. -c core.excludesFile=./.agitignore $@

            "#)).change_context(ErrorStack::WriteFile)?;

            // make it executable with rust builtin chmod
            std::fs::set_permissions(&agit_path, std::fs::Permissions::from_mode(0o755))
                .change_context(ErrorStack::CreateFile)?;
        }

        // place .agitignore
        {
            let mut file =
                File::create(&agit_ignore_path).change_context(ErrorStack::CreateFile)?;
            #[rustfmt::skip]
            write!(file, "{}", STSL!(r#"
                ## THIS IS PLACED BY ace init ##

                # Ignore everything
                *
            "#)).change_context(ErrorStack::WriteFile)?;
        }

        // init agit
        {
            // run agit init
            let mut cmd = std::process::Command::new(&agit_path);
            cmd.arg("init");
            cmd.output()
                .change_context(ErrorStack::ExecuteCommand("agit".to_string()))?;
        }

        // Set agit ident
        {
            let mut cmd = std::process::Command::new(&agit_path);
            cmd.arg("config");
            cmd.arg("user.email");
            cmd.arg(format!(
                "{}@{}",
                &app.path_accountkey_region.region, &app.path_accountkey_region.account_key
            ));
            cmd.output()
                .change_context(ErrorStack::ExecuteCommand("set agit ident".to_string()))?;

            let mut cmd = std::process::Command::new(&agit_path);
            cmd.arg("config");
            cmd.arg("user.name");
            cmd.arg("ace");
            cmd.output()
                .change_context(ErrorStack::ExecuteCommand("set agit ident".to_string()))?;
        }

        // generate agit-upgrade script
        {
            let agit_upgrade_path = app.bin_path.join("agit-upgrade");

            #[rustfmt::skip]
            let agit_upgrade_text = garbage::CNSL!(r#"
                #!/bin/bash
                ## THIS IS PLACED BY ace init ##

                # Fail on errors
                set -e

                # make sure we run in the root dir
                cd $(git rev-parse --show-toplevel) || exit 1

                # If git status reports not clean, then print a message and exit
                if [[ "$(git status --porcelain)" != "" ]]; then
                    echo "Error: the git repo is not clean."
                    echo 'Please run `ace commit` to fix'
                    exit 1
                fi

                # If the bin/agit status reports not clean, then print a message and exit
                if [[ "$(bin/agit status --porcelain)" != "" ]]; then
                    echo "bin/agit status"
                    bin/agit status
                    echo
                    echo "Error: the .agit repo is not clean."
                    echo 'This script runs `bin/agit reset --hard FETCH_HEAD` to update the .agit repo.'
                    echo 'Therefore, please use `bin/agit` to either commit and push, or reset your changes first.'
                    exit 1
                fi

                # Validate there is an origin remote
                remote_url=$(bin/agit remote get-url origin || "")
                if [[ "$remote_url" == "" ]]; then
                    echo "Error: the origin remote is not set."
                    echo 'Please run `bin/agit remote add origin <url>` to set the origin remote.'
                    exit 1
                fi

                # Print the origin remote and ask user to confirm
                echo "The origin remote is set to:"
                echo "  $remote_url"
                read -p "Is this correct? [y/N] " -n 1 -r
                echo
                if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                    echo "Aborting..."
                    exit 1
                fi                    

                # Pull the agit repo
                GIT_SSH_COMMAND="ssh -i data/ace2_key" bin/agit fetch origin --depth=1
                bin/agit reset --hard FETCH_HEAD

                # Do not fail on errors because git returns error codes for some empty ops
                set +e

                # Commit everything
                git add -A && git commit -m "auto commit after update operation"

                # Fail on errors
                set -e

                # Fix anything that needs
                bin/ace upgrade --fixup

                # Commit everything
                bin/ace commit

                # Refresh the system after upgrade
                bin/ace init
                bin/ace system-update

                # Print a message
                echo "DONE!  SUCCESS!"
            "#);

            // Write out the script and make it executable
            tokio::fs::write(&agit_upgrade_path, agit_upgrade_text)
                .await
                .change_context(ErrorStack::WriteAutoUpdateScriptFile)?;

            // make it executable with rust builtin chmod
            std::fs::set_permissions(&agit_upgrade_path, std::fs::Permissions::from_mode(0o755))
                .change_context(ErrorStack::WriteAutoUpdateScriptFilePermissions)?;
        }
    }

    println!("Placing pathcache");
    {
        let mut file =
            File::create(&app.data_pathcache_path).change_context(ErrorStack::CreateFile)?;
        write!(file, "{}", &app.path.display().to_string())
            .change_context(ErrorStack::WriteFile)?;
    }

    version_2_0_0_deprecations(app)?;

    let j1 = check_easytls_binary(app, &mpb);
    let j2 = check_gocryptfs_binary(app, &mpb);
    let j3 = check_nebula_binaries(app, &mpb);
    let j4 = check_packer_binary(app, &mpb);
    let j5 = check_terraform_binary(app, &mpb);

    try_join!(j1, j2, j3, j4, j5)?;

    ace_core::git::write_version_file(app).change_context(ErrorStack::WriteVersionFile)?;

    // If etc/account.toml does not exist, create it
    if app.ace_db_app.etc_account_file_path.exists() {
        println!(
            "account file found at {}",
            app.ace_db_app.etc_account_file_path.display()
        );
    } else {
        let mut file = File::create(&app.ace_db_app.etc_account_file_path)
            .change_context(ErrorStack::CreateAccountTomlFile)?;
        #[rustfmt::skip]
        write!(file, "{}", ace_db::etc::account::example_string().change_context(ErrorStack::ExampleString)?).change_context(ErrorStack::WriteAccountFile)?;
    }

    // If etc/config.toml does not exist, create it
    if app.ace_db_app.etc_config_file_path.exists() {
        println!(
            "config file found at {}",
            app.ace_db_app.etc_config_file_path.display()
        );
    } else {
        let mut file = File::create(&app.ace_db_app.etc_config_file_path)
            .change_context(ErrorStack::CreateConfigTomlFile)?;
        #[rustfmt::skip]
        write!(file, "{}", ace_db::etc::config::example_string().change_context(ErrorStack::ExampleString)?).change_context(ErrorStack::WriteConfigFile)?;
    }

    // TODO: Mount HERE (or prior here)
    if !app
        .is_secure_mounted()
        .change_context(ErrorStack::CheckSecureMountState)?
    {
        ace_core::gocryptfs::secure_mount(app).change_context(ErrorStack::SecureMount)?;
    }

    if !app
        .is_secure_mounted()
        .change_context(ErrorStack::CheckSecureMountState)?
    {
        println!("Error: you cannot continue until the secure filesystem is mounted");
    }

    // If local_file_path does not exist, offer to create it
    if app.local_file_path.exists() {
        println!("local file found at {}", app.local_file_path.display());
    } else {
        println!("no local file found at {}", app.local_file_path.display());
        let account_key_pattern = Regex::new(r"^[a-z][a-z0-9]{3,7}$").unwrap();

        let account_key: String = Input::with_theme(&ColorfulTheme::default())
            .default(app.path_accountkey_region.account_key.to_string())
            .with_prompt("Enter Account Key")
            .validate_with(|input: &String| -> Result<(), &str> {
                if account_key_pattern.is_match(input) {
                    Ok(())
                } else {
                    Err("Account Key must start with a letter followed by letters and numbers for a total of 4-8 characters.")
                }
            })
            .interact_text().change_context(ErrorStack::SelectInteract)?;

        let default_index = ace_core::REGION_SET
            .iter()
            .position(|&r| r == app.path_accountkey_region.region)
            .unwrap_or(0);

        let selection = Select::with_theme(&ColorfulTheme::default())
            .with_prompt("Pick the region you want to use")
            .default(default_index)
            .items(&ace_core::REGION_SET[..])
            .interact()
            .change_context(ErrorStack::SelectInteract)?;

        let region_name = ace_core::REGION_SET[selection];

        // Define username to be "{account_key}-{region}-ace-root"
        let username = format!("{}-{}-ace-root", account_key, region_name);

        // Use Term to write a line that explains you need to run the following in the cloud shell
        let term = Term::stdout();

        term.write_line("Run the following in the cloud shell:\n")
            .unwrap();

        let command = format!("aws iam create-user --user-name {} && aws iam attach-user-policy --user-name {} --policy-arn arn:aws:iam::aws:policy/AdministratorAccess && aws iam create-access-key --user-name {}", quote(&username), quote(&username), quote(&username));
        println!("{}\n", command);

        let response = dialoguer::Select::new()
            .with_prompt("Have you run the above command in the cloud shell?")
            .items(&["yes", "no, I want to cancel"])
            .default(0)
            .interact()
            .change_context(ErrorStack::SelectInteract)?;

        // Cancel response
        if response == 1 {
            error_stack::bail!(ErrorStack::UserCancelled)
        }

        // Prompt for aws access key and secret key
        let access_key_id: String = Input::with_theme(&ColorfulTheme::default())
            .with_prompt("Paste AWS Access Key")
            .interact_text()
            .unwrap();

        let secret_access_key: String = Password::with_theme(&ColorfulTheme::default())
            .with_prompt("Paste AWS Secret Key")
            .interact()
            .unwrap();

        let mut file = File::create(&app.local_file_path).expect("Could not create file");
        writeln!(file, "ACE_ACCOUNT_KEY={}", account_key).expect("Could not write to file");
        writeln!(file, "AWS_REGION={}", region_name).expect("Could not write to file");
        writeln!(file, "AWS_ACCESS_KEY_ID={}", access_key_id).expect("Could not write to file");
        writeln!(file, "AWS_SECRET_ACCESS_KEY={}", secret_access_key)
            .expect("Could not write to file");

        // Set up symbolic link to the local file in the secure mountpoint
        let local_file_symlink = app.path.join("LOCAL.env");
        match std::os::unix::fs::symlink("secure/LOCAL.env", &local_file_symlink) {
            Ok(_) => {}
            Err(e) => {
                println!("Error creating symlink: {}", e);
            }
        }
    }

    // Note that the PUBLIC ace key is stored in the data directory
    let ace2_private_key_path = app
        .ace_db_app
        .secure_mountpoint
        .join("data")
        .join("ace2_key");
    let ace2_public_key_path = app.ace_db_app.data_path.join("ace2_key.pub");

    // If the ace2 keypair does not exist, generate it
    if ace2_private_key_path.exists() && ace2_public_key_path.exists() {
        println!("ace2 keypair found, not generating.");
    } else {
        println!("ace2 keypair not found, generating...");

        // Create the corresponding secure data directory
        create_dir_if_not_exists(&app.secure_mountpoint.join("data"))?;

        let mut cmd = std::process::Command::new("ssh-keygen");
        cmd.args(["-t", "ed25519"]); // Use ED25519 elliptic curve
        cmd.arg("-f"); // Specify the file name
        cmd.arg(&ace2_private_key_path);
        cmd.arg("-C"); // Comment
        cmd.arg(format!(
            "ace@{}@{}",
            &app.path_accountkey_region.account_key, &app.path_accountkey_region.region
        ));
        cmd.args(["-N", ""]); // No passphrase
        cmd.output()
            .change_context(ErrorStack::GenerateAceKeypair)?;

        // Copy the public key out of the secure mountpoint to the data directory and remove the original
        // (ssh-keygen automatically puts it in the same directory as the private key)
        match std::fs::copy(
            ace2_private_key_path.with_extension("pub"),
            &ace2_public_key_path,
        ) {
            Ok(_) => {
                println!("Copying public key to data directory");
                match std::fs::remove_file(ace2_private_key_path.with_extension("pub")) {
                    Ok(_) => println!("Removing public key from secure mountpoint"),
                    Err(e) => {
                        println!("Error removing public key: {}", e);
                    }
                }
            }
            Err(e) => {
                println!("Error copying public key: {}", e);
            }
        }

        // Set up symbolic link to the private key in secure
        let ace2_key_symlink = app.ace_db_app.data_path.join("ace2_key");
        if !ace2_key_symlink.exists() {
            match std::os::unix::fs::symlink(&ace2_private_key_path, &ace2_key_symlink) {
                Ok(_) => {}
                Err(e) => {
                    println!("Error creating symlink: {}", e);
                }
            }
        }
    }

    // Create corresponding secure vpn directory while still mounted
    create_dir_if_not_exists(&app.secure_mountpoint.join("vpn"))?;

    ace_core::git::autocommit(app, &"auto commit after init operation".to_string())
        .await
        .change_context(ErrorStack::AutoCommit)?;

    println!(
        "\n\nDone!\n\nPlease review:\n  {}\n  {}\n",
        app.ace_db_app.etc_account_file_path.display(),
        app.ace_db_app.etc_config_file_path.display()
    );

    mpb.clear().unwrap();

    Ok(())
}

async fn check_packer_binary(
    app: &ace_core::Application,
    multi_progress_bar: &MultiProgress,
) -> error_stack::Result<(), ErrorStack> {
    let packer_bin_sha256 = ace_core::compute_sha256_file_path(&app.packer_bin_path);

    let packer_hash_matches = match &packer_bin_sha256 {
        Some(packer_path_sha256) => packer_path_sha256 == ace_core::PACKER_ZIP_BIN_SHA256,
        None => false,
    };

    if packer_hash_matches {
        multi_progress_bar
            .println(format!(
                "packer {} hash matches, no need to download.",
                &app.packer_version
            ))
            .change_context(ErrorStack::PrintMPBMessage)?;
    } else {
        ace_core::download::download_bin_in_zip(
            &app.temp_path,
            &app.packer_zip_url,
            &app.packer_bin_path,
            "packer",
            ace_core::PACKER_ZIP_SHA256,
            ace_core::PACKER_ZIP_BIN_SHA256,
            Some(multi_progress_bar),
        )
        .await
        .change_context(ErrorStack::DownloadPackerBin)?;
    }

    Ok(())
}

async fn check_terraform_binary(
    app: &ace_core::Application,
    multi_progress_bar: &MultiProgress,
) -> error_stack::Result<(), ErrorStack> {
    let terraform_bin_sha256 = ace_core::compute_sha256_file_path(&app.terraform_bin_path);

    let terraform_hash_matches = match &terraform_bin_sha256 {
        Some(terraform_path_sha256) => terraform_path_sha256 == ace_core::TERRAFORM_ZIP_BIN_SHA256,
        None => false,
    };

    if terraform_hash_matches {
        multi_progress_bar
            .println(format!(
                "terraform {} hash matches, no need to download.",
                &app.terraform_version
            ))
            .change_context(ErrorStack::PrintMPBMessage)?;
    } else {
        ace_core::download::download_bin_in_zip(
            &app.temp_path,
            &app.terraform_zip_url,
            &app.terraform_bin_path,
            "terraform",
            ace_core::TERRAFORM_ZIP_SHA256,
            ace_core::TERRAFORM_ZIP_BIN_SHA256,
            Some(multi_progress_bar),
        )
        .await
        .change_context(ErrorStack::DownloadTerraformBin)?;
    }

    Ok(())
}

async fn check_easytls_binary(
    app: &ace_core::Application,
    multi_progress_bar: &MultiProgress,
) -> error_stack::Result<(), ErrorStack> {
    let easytls_bin_sha256 = ace_core::compute_sha256_file_path(&app.easytls_bin_path);

    let easytls_hash_matches = match &easytls_bin_sha256 {
        Some(easytls_path_sha256) => easytls_path_sha256 == ace_core::EASYTLS_BIN_SHA256,
        None => false,
    };

    if easytls_hash_matches {
        multi_progress_bar
            .println(format!(
                "easytls {} hash matches, no need to download.",
                &app.easytls_version
            ))
            .change_context(ErrorStack::PrintMPBMessage)?;
    } else {
        ace_core::download::download_bin(
            &app.easytls_bin_url,
            &app.easytls_bin_path,
            ace_core::EASYTLS_BIN_SHA256,
            Some(multi_progress_bar),
        )
        .await
        .change_context(ErrorStack::DownloadEasyTLSBin)?;
    }

    Ok(())
}

async fn check_gocryptfs_binary(
    app: &ace_core::Application,
    multi_progress_bar: &MultiProgress,
) -> error_stack::Result<(), ErrorStack> {
    if app.gocryptfs_bin_path.exists() {
        multi_progress_bar
            .println("gocryptfs binary exists, no need to download/extract.")
            .change_context(ErrorStack::PrintMPBMessage)?;
    } else {
        let tarball_path = ace_core::download::download_tarball(
            &app.gocryptfs_tar_url,
            &app.temp_path,
            Some(multi_progress_bar),
        )
        .await
        .change_context(ErrorStack::DownloadGocyrptfsTarball)?;

        ace_core::download::untar_and_move_gocryptfs(&app.temp_path, &tarball_path, &app.bin_path)
            .change_context(ErrorStack::ExtractGocryptfsTarball)?;
    }

    Ok(())
}

async fn check_nebula_binaries(
    app: &ace_core::Application,
    multi_progress_bar: &MultiProgress,
) -> error_stack::Result<(), ErrorStack> {
    let nebula_bin_sha256 = ace_core::compute_sha256_file_path(&app.nebula_path);
    let nebula_cert_bin_sha256 = ace_core::compute_sha256_file_path(&app.nebula_cert_path);

    let nebula_hash_matches = match &nebula_bin_sha256 {
        Some(nebula_path_sha256) => nebula_path_sha256 == ace_core::NEBULA_BIN_SHA256,
        None => false,
    };

    let nebula_cert_hash_matches = match &nebula_cert_bin_sha256 {
        Some(nebula_cert_path_sha256) => {
            nebula_cert_path_sha256 == ace_core::NEBULA_CERT_BIN_SHA256
        }
        None => false,
    };

    if nebula_cert_hash_matches && nebula_hash_matches {
        multi_progress_bar
            .println("nebula and nebula-cert hashes match, no need to download/extract.")
            .change_context(ErrorStack::PrintMPBMessage)?;
    } else {
        let tarball_path = ace_core::download::download_tarball(
            &app.nebula_tar_url,
            &app.temp_path,
            Some(multi_progress_bar),
        )
        .await
        .change_context(ErrorStack::DownloadGocyrptfsTarball)?;

        ace_core::download::untar_and_move_nebula(&app.temp_path, &tarball_path, &app.bin_path)
            .change_context(ErrorStack::ExtractNebulaTarball)?;
    }

    Ok(())
}
