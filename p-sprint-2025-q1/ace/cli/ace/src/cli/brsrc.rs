use ace_graph::GraphKeyExt;
use comfy_table::{presets, ContentArrangement, Table};

pub async fn list(verbose: bool, ace_db_app: &ace_db::App) {
    let brsrcs_result =
        ace_graph::brsrc::select_result(&ace_graph::BrsrcFilter::All, ace_db_app).await;
    let mut errors = Vec::new();
    let mut brsrcs_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid brsrcs
    match brsrcs_result {
        Ok(brsrcs) => {
            for brsrc in brsrcs {
                match brsrc {
                    Ok(brsrc) => brsrcs_sorted.push(brsrc),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching brsrcs: {:#?}", e);
            return;
        }
    }

    // Sort brsrcs by their graphkey
    brsrcs_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for brsrc in brsrcs_sorted {
            println!("\n{:#?}", brsrc);
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec![
            "Graphkey",
            "Source Bucket ARN",
            "Target AccountKey@Region",
            "Target Bucket ARN",
        ]);

        for brsrc in brsrcs_sorted {
            let target_account_key_region =
                format!("{}@{}", brsrc.target.account_key, brsrc.target.region);
            table.add_row(vec![
                brsrc.graphkey.to_string(),
                brsrc.source.bucket_arn.to_string(),
                target_account_key_region,
                brsrc.target.bucket_arn.to_string(),
            ]);
        }

        println!("{}", table);
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{:#?}", error);
        }
    }
}
