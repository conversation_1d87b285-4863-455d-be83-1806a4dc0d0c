use ace_graph::GraphKeyExt;
use comfy_table::{presets, ContentArrangement, Table};
use error_stack::ResultExt;
use std::io::Write;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    CommandFailed(std::process::ExitStatus, String, String),
    CopyFileFromTo(String, String),
    CreateTempDir,
    CreateTempDockerFile(String),
    ExecuteCommand(String),
    GetMysqldevimg(String),
    GetMysqldevimgList,
    GetSqlFileMetaData(String),
    GetSqlFilenameFromPath,
    WriteLineToDockerfile,
}

pub async fn list(verbose: bool, ace_db_app: &ace_db::App) {
    let mysqldevimg_result =
        ace_graph::mysqldevimg::select_result(&ace_graph::MysqlDevImgFilter::All, ace_db_app).await;
    let mut errors = Vec::new();
    let mut mysqldevimg_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid mysqldevimg entries
    match mysqldevimg_result {
        Ok(mysqldevimgs) => {
            for mysqldevimg in mysqldevimgs {
                match mysqldevimg {
                    Ok(mysqldevimg) => mysqldevimg_sorted.push(mysqldevimg),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching mysqldevimgs: {:#?}", e);
            return;
        }
    }

    // Sort mysqldevimgs by their graphkey
    mysqldevimg_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for mysqldevimg in mysqldevimg_sorted {
            println!("\n{:#?}", mysqldevimg);
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);

        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey", "Tag Name", "Sql Path"]);
        for mysqldevimg in mysqldevimg_sorted {
            let graphkey = mysqldevimg.graphkey.to_string();
            let tag_name = mysqldevimg.tag_name.to_string();
            let sql_path = mysqldevimg.sql_path.to_string_lossy().into_owned();
            table.add_row(vec![graphkey, tag_name, sql_path]);
        }

        println!("{}", table);
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{:#?}", error);
        }
    }
}
// Other functions (run, build, push, etc.) remain unchanged.

pub async fn run(ace_db_app: &ace_db::App) -> error_stack::Result<(), ErrorStack> {
    let mysqldevimg_list =
        ace_graph::mysqldevimg::select(&ace_graph::MysqlDevImgFilter::All, ace_db_app)
            .await
            .change_context(ErrorStack::GetMysqldevimgList)?;

    for mysqldevimg in mysqldevimg_list {
        println!();
        println!("ace mysqldevimg build {}", mysqldevimg.name);
        println!("ace mysqldevimg push {}", mysqldevimg.name);
    }

    Ok(())
}

pub async fn build(
    app: &ace_core::Application,
    identifier: &str,
) -> error_stack::Result<(), ErrorStack> {
    let mysqldevimg = ace_graph::mysqldevimg::get(
        &ace_graph::MysqlDevImg::Db(identifier.to_string()),
        &app.ace_db_app,
    )
    .await
    .change_context(ErrorStack::GetMysqldevimg(identifier.to_string()))?;

    let tag_name = &mysqldevimg.tag_name;

    let sql_filename = match mysqldevimg.sql_path.file_name() {
        Some(sql_filename) => sql_filename,
        None => {
            error_stack::bail!(ErrorStack::GetSqlFilenameFromPath);
        }
    };

    let temp_dir = app
        .create_temp_dir(&format!("mysqldevimg-{}", mysqldevimg.name))
        .change_context(ErrorStack::CreateTempDir)?;

    // Copy file into temp dir
    std::fs::copy(&mysqldevimg.sql_path, temp_dir.join(sql_filename)).change_context(
        ErrorStack::CopyFileFromTo(
            mysqldevimg.sql_path.display().to_string(),
            temp_dir.display().to_string(),
        ),
    )?;

    // Open Dockerfile in temp dir
    let dockerfile_path = temp_dir.join("Dockerfile");
    let mut dockerfile = std::fs::File::create(&dockerfile_path).change_context(
        ErrorStack::CreateTempDockerFile(dockerfile_path.display().to_string()),
    )?;

    // Write lines to dockerfile
    writeln!(dockerfile, "FROM mariadb:11.0.2-jammy")
        .change_context(ErrorStack::WriteLineToDockerfile)?;
    writeln!(
        dockerfile,
        "COPY {} /docker-entrypoint-initdb.d/{}",
        sql_filename.to_string_lossy(),
        sql_filename.to_string_lossy()
    )
    .change_context(ErrorStack::WriteLineToDockerfile)?;
    writeln!(dockerfile, "ENV MARIADB_DATABASE=app")
        .change_context(ErrorStack::WriteLineToDockerfile)?;
    writeln!(dockerfile, "ENV MARIADB_USER=app")
        .change_context(ErrorStack::WriteLineToDockerfile)?;

    // Build the image
    let mut cmd = std::process::Command::new("docker");
    cmd.stderr(std::process::Stdio::inherit());
    cmd.stdout(std::process::Stdio::inherit());
    cmd.current_dir(&temp_dir);
    cmd.arg("build");
    cmd.arg("--tag").arg(tag_name);
    cmd.arg(".");

    let output = cmd
        .output()
        .change_context(ErrorStack::ExecuteCommand("docker".to_string()))?;

    // fail if command failed
    if !output.status.success() {
        error_stack::bail!(ErrorStack::CommandFailed(
            output.status,
            "docker".to_string(),
            String::from_utf8_lossy(&output.stderr).to_string()
        ));
    }

    Ok(())
}

pub async fn push(
    identifier: &str,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<(), ErrorStack> {
    let mysqldevimg = ace_graph::mysqldevimg::get(
        &ace_graph::MysqlDevImg::Db(identifier.to_string()),
        ace_db_app,
    )
    .await
    .change_context(ErrorStack::GetMysqldevimg(identifier.to_string()))?;
    let tag_name = &mysqldevimg.tag_name;

    // Call env.terraform_path executable with all of the remaining command line arguments
    let mut cmd = std::process::Command::new("docker");
    cmd.envs(std::env::vars());
    cmd.stdout(std::process::Stdio::inherit());
    cmd.stderr(std::process::Stdio::inherit());
    cmd.arg("push");
    cmd.arg(tag_name);

    let output = cmd
        .output()
        .change_context(ErrorStack::ExecuteCommand("docker".to_string()))?;

    // fail if command failed
    if !output.status.success() {
        error_stack::bail!(ErrorStack::CommandFailed(
            output.status,
            "docker".to_string(),
            String::from_utf8_lossy(&output.stderr).to_string()
        ));
    }

    Ok(())
}

/*
// Get the mysqldevimg

let sql_path = &mysqldevimg.sql_path;
let sql_filename = sql_path.file_name().with_context(|| {
    format!(
        "While getting the file name from the path: {}",
        sql_path.display()
    )
})?;

// Call env.terraform_path executable with all of the remaining command line arguments
let mut cmd = std::process::Command::new("docker");
cmd.stderr(std::process::Stdio::inherit());

cmd.arg("run");
cmd.arg("--name").arg("mysqldevimg");
cmd.arg("-d");
cmd.arg("--env").arg("MARIADB_ROOT_PASSWORD=dbpass");
cmd.arg("--env").arg("MARIADB_DATABASE=app");
cmd.arg("--env").arg("MARIADB_USER=app");
cmd.arg("--env").arg("MARIADB_PASSWORD=pass");
cmd.arg("-v").arg(format!(
    "{}:/docker-entrypoint-initdb.d/{}",
    sql_path.to_string_lossy(),
    sql_filename.to_string_lossy()
));
cmd.arg("mariadb:11.0.2-jammy");

let output = cmd
    .output()
    .with_context(|| format!("While running the command:\n{:?}", cmd))?;

// fail if command failed
if !output.status.success() {
    anyhow::bail!(
        "Command failed with status: {}\n{:?}\n{}",
        output.status,
        cmd,
        String::from_utf8_lossy(&output.stderr)
    );
}

let container_id = String::from_utf8_lossy(&output.stdout).trim().to_string();

eprintln!("Launched image_id: {}", container_id);

// Loop to 10, sleeping each second
for _ in 0..200 {
    // run this command: docker exec mysqldevimg /usr/local/bin/healthcheck.sh
    let mut cmd = std::process::Command::new("docker");
    cmd.arg("exec");
    cmd.arg(&container_id);
    cmd.arg("/usr/local/bin/healthcheck.sh");
    cmd.arg("--connect");

    let foo = cmd
        .output()
        .with_context(|| format!("While running the command:\n{:?}", cmd))?;

    println!("Server Status Status (0 is ready): {}", foo.status);

    if foo.status.success() {
        break;
    }

    tokio::time::sleep(std::time::Duration::from_secs(1)).await;
}

// Tell docker to stop the container
let mut cmd = std::process::Command::new("docker");
cmd.stderr(std::process::Stdio::inherit());
cmd.arg("stop");
cmd.arg(&container_id);

let output = cmd
    .output()
    .with_context(|| format!("While running the command:\n{:?}", cmd))?;

// fail if command failed
if !output.status.success() {
    anyhow::bail!(
        "Command failed with status: {}\n{:?}\n{}",
        output.status,
        cmd,
        String::from_utf8_lossy(&output.stderr)
    );
}

// Tell docker to take an image of the container
let mut cmd = std::process::Command::new("docker");
cmd.stderr(std::process::Stdio::inherit());
cmd.arg("commit");
cmd.arg(&container_id);
cmd.arg(format!("mysqldevimg-{}", identifier));

let output = cmd
    .output()
    .with_context(|| format!("While running the command:\n{:?}", cmd))?;

// fail if command failed
if !output.status.success() {
    anyhow::bail!(
        "Command failed with status: {}\n{:?}\n{}",
        output.status,
        cmd,
        String::from_utf8_lossy(&output.stderr)
    );
}

let image_id = String::from_utf8_lossy(&output.stdout).trim().to_string();

eprintln!("Created image_id: {}", image_id);

// Tell docker to remove the container
let mut cmd = std::process::Command::new("docker");
cmd.stderr(std::process::Stdio::inherit());
cmd.arg("rm");
cmd.arg(&container_id);

let output = cmd
    .output()
    .with_context(|| format!("While running the command:\n{:?}", cmd))?;

// fail if command failed
if !output.status.success() {
    anyhow::bail!(
        "Command failed with status: {}\n{:?}\n{}",
        output.status,
        cmd,
        String::from_utf8_lossy(&output.stderr)
    );
}

Ok(())

 */
