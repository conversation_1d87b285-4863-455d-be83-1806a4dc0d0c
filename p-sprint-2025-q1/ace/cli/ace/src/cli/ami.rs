use ace_graph::GraphKeyExt;
use comfy_table::{presets, ContentArrangement, Table};
use granite::util::time_ago_en_us;

pub async fn list(verbose: bool, ace_db_app: &ace_db::App) {
    let amis_result = ace_graph::ami::select_result(ace_graph::AmiFilter::All, ace_db_app).await;
    let mut errors = Vec::new();
    let mut amis_sorted = Vec::new();

    // Separate valid AMIs and errors
    match amis_result {
        Ok(amis) => {
            for ami in amis {
                match ami {
                    Ok(ami) => amis_sorted.push(ami),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching AMIs: {:#?}", e);
            return;
        }
    }

    // Sort valid AMIs by their graphkey
    amis_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for ami in amis_sorted {
            println!("{:#?}", ami);
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);

        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey", "AMI ID", "Created On"]);

        for ami in amis_sorted {
            let build_time = match ami.build_timestamp {
                Some(timestamp) => time_ago_en_us(timestamp),
                None => "unknown".to_string(),
            };

            table.add_row(vec![
                ami.graphkey.to_string(),
                ami.ami_id.to_string(),
                build_time,
            ]);
        }

        println!("{}", table);
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{:#?}", error);
        }
    }
}
