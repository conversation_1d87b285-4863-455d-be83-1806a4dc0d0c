use error_stack::ResultExt;
use tokio::join;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    AutoCommit,
    BuildFailed,
    GenerateAceServerToml,
    GenerateAutoUpdate,
    GeneratePacker,
    GenerateRelocateBashScript,
    GenerateTerraform,
    GetConfig,
    ReadFromTlsCertFile(std::path::PathBuf),
    ReadFromTlsKeyFile(std::path::PathBuf),
    ParseTlsCertAndKey,
    SerializeWebServerConfig,
    SetAutoUpdateScriptFilePermissions,
    SetBashScriptFilePermissions,
    SetTransformScriptPermissions,
    TlsCertFileNotFound(std::path::PathBuf),
    TlsKeyFileNotFound(std::path::PathBuf),
    WriteAgitConfigFile,
    WriteAutoUpdateScriptFile,
    WriteBashScriptFile,
    WriteToAceServerToml,
    WriteTransformScriptFile,
    WriteVersionFile,
}

#[derive(Debug)]
enum AceServerToml {
    Error(error_stack::Report<ErrorStack>),
    Ok,
    Skip,
}

pub async fn run(app: &ace_core::Application) -> error_stack::Result<(), ErrorStack> {
    let config = ace_graph::config::get(&ace_graph::Config::EtcConfig, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GetConfig)?;

    let future_generate_packer = generate_packer(app);
    let future_generate_terraform = generate_terraform(app, &config);
    let future_generate_ace_server_toml = generate_ace_server_toml(app);

    let (result_packer, result_terraform, result_server_toml) = join!(
        future_generate_packer,
        future_generate_terraform,
        future_generate_ace_server_toml
    );

    let mut statuses: Vec<(&str, &str)> = Vec::new();
    let error_status = "\u{2715}";
    let success_status = "\u{2714}";

    match result_packer {
        Ok(_) => statuses.push((success_status, "Packer")),
        Err(_e) => statuses.push((error_status, "Packer")),
    }
    match result_terraform {
        Ok(_) => statuses.push((success_status, "Terraform")),
        Err(_e) => statuses.push((error_status, "Terraform")),
    }
    match result_server_toml {
        AceServerToml::Ok => statuses.push((success_status, "Create Ace Server Toml")),
        AceServerToml::Error(_e) => {
            statuses.push((error_status, "Create Ace Server Toml"));
        }
        AceServerToml::Skip => (),
    }

    ace_core::git::write_version_file(app).change_context(ErrorStack::WriteVersionFile)?;

    ace_core::git::autocommit(app, &"auto commit after build operation".to_string())
        .await
        .change_context(ErrorStack::AutoCommit)?;

    println!("Status:");
    for (status, name) in statuses {
        println!(" {} {}", status, name);
    }

    Ok(())
}

async fn generate_packer(app: &ace_core::Application) -> error_stack::Result<(), ErrorStack> {
    match ace_core::packer::build(app)
        .await
        .change_context(ErrorStack::GeneratePacker)
    {
        Ok(_) => Ok(()),
        Err(e) => {
            println!("{:#?}", e);
            Err(e)
        }
    }
}

async fn generate_terraform(
    app: &ace_core::Application,
    config: &ace_graph::config::Config,
) -> error_stack::Result<(), ErrorStack> {
    match ace_core::terraform::build(app, config)
        .await
        .change_context(ErrorStack::GenerateTerraform)
    {
        Ok(_) => Ok(()),
        Err(e) => {
            println!("{:#?}", e);
            Err(e)
        }
    }
}

async fn generate_ace_server_toml(app: &ace_core::Application) -> AceServerToml {
    // ONLY CREATE THIS FILE IF THE BINARY IS IN THE BIN PATH
    if app.ace_server_bin_path.exists() {
        match create_ace_server_toml(app).await {
            Ok(_) => return AceServerToml::Ok,
            Err(e) => {
                println!("{:#?}", e);
                return AceServerToml::Error(e);
            }
        }
    }
    println!("Skipping creation of ace-server.toml, ace-server binary not found...\n");

    AceServerToml::Skip
}

/// This function will create a basic ace-server.toml file if it does not already exist.
/// The tls configuration will be set to static, port 443 and host "0.0.0.0".
///
/// Errors:
/// This function bails if the tls cert or key files do not exist or there is a problem reading them.
pub async fn create_ace_server_toml(
    app: &ace_core::Application,
) -> error_stack::Result<(), ErrorStack> {
    let ace_server_toml_path = app.bin_path.join("ace-server.toml");
    let contents = r#"[webserver]
host = "0.0.0.0"
port = 443
"#;

    std::fs::write(ace_server_toml_path, contents)
        .change_context(ErrorStack::WriteToAceServerToml)?;

    Ok(())
}
