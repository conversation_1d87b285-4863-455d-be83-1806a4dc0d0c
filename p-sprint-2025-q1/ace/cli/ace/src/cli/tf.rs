use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    AutoCommit,
    CheckTerraformState,
    ExecuteCommand(std::path::PathBuf),
    TerraformStateIsUncommitted,
}

pub async fn run(
    app: &ace_core::Application,
    args: &[String],
) -> error_stack::Result<(), ErrorStack> {
    let mut autocommit = false;
    if let Some(first_arg) = args.first() {
        match first_arg.as_str() {
            "apply" | "destroy" | "import" | "init" | "input" | "taint" | "untaint" | "refresh"
            | "state" => {
                match app.is_dirty("terraform/terraform.tfstate").await {
                    Ok(true) => {
                        error_stack::bail!(ErrorStack::TerraformStateIsUncommitted);
                    }
                    Ok(false) => {}
                    Err(e) => {
                        return Err(e.change_context(ErrorStack::CheckTerraformState));
                    }
                }
                autocommit = true;
            }
            _ => {}
        }
    }

    // Call env.terraform_path executable with all of the remaining command line arguments
    let mut cmd = std::process::Command::new(&app.terraform_bin_path);
    cmd.args(args.iter());
    cmd.current_dir(&app.terraform_path);
    cmd.envs(std::env::vars());
    cmd.stdout(std::process::Stdio::inherit());
    cmd.stderr(std::process::Stdio::inherit());
    cmd.stdin(std::process::Stdio::inherit());
    cmd.output()
        .change_context(ErrorStack::ExecuteCommand(app.terraform_bin_path.clone()))?;

    if autocommit {
        ace_core::git::autocommit(
            app,
            &format!(
                "auto commit after terraform {:?}",
                args.first().unwrap_or(&"".to_string())
            )
            .to_string(),
        )
        .await
        .change_context(ErrorStack::AutoCommit)?;
    }

    Ok(())
}
