use ace_graph::GraphKeyExt;
use comfy_table::{presets, ContentArrangement, Table};

pub async fn list(verbose: bool) {
    let iam_role_policy_attach_result =
        ace_graph::iam_role_policy_attach::select_result(ace_graph::IamRolePolicyAttachFilter::All)
            .await;
    let mut errors = Vec::new();
    let mut iam_role_policy_attaches_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid IAM role policy attachment entries
    match iam_role_policy_attach_result {
        Ok(iam_role_policy_attaches) => {
            for iam_role_policy_attach in iam_role_policy_attaches {
                match iam_role_policy_attach {
                    Ok(iam_role_policy_attach) => {
                        iam_role_policy_attaches_sorted.push(iam_role_policy_attach)
                    }
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            println!("Error fetching IAM role policy attachments: {:#?}", e);
            return;
        }
    }

    // Sort IAM role policy attachments by their graphkey
    iam_role_policy_attaches_sorted
        .sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for iam_role_policy_attach in iam_role_policy_attaches_sorted {
            println!("\n{:#?}", iam_role_policy_attach);
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey"]);
        for iam_role_policy_attach in iam_role_policy_attaches_sorted {
            table.add_row(vec![iam_role_policy_attach.graphkey.to_string()]);
        }

        println!("{}", table);
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{:#?}", error);
        }
    }
}
