use ace_graph::GraphKeyExt;
use comfy_table::{presets, ContentArrangement, Table};

pub async fn list(verbose: bool) {
    let subnets_result = match ace_graph::sn::select_result(ace_graph::SubnetFilter::All).await {
        Ok(subnets) => subnets,
        Err(e) => {
            eprintln!("Error: {:#?}", e);
            return;
        }
    };

    let mut errors = Vec::new();
    let mut subnets_sorted = Vec::new();

    for subnet in subnets_result {
        match subnet {
            Ok(subnet) => subnets_sorted.push(subnet),
            Err(e) => errors.push(e),
        }
    }

    // Sort subnets by their graphkey
    subnets_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for subnet in subnets_sorted {
            println!("\n{:#?}", subnet);
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);

        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey"]);

        for subnet in subnets_sorted {
            table.add_row(vec![subnet.graphkey.to_string()]);
        }

        println!("{}", table);
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{:#?}", error);
        }
    }
}
