use ace_graph::GraphKeyExt;
use comfy_table::{presets, ContentArrangement, Table};
pub async fn list(verbose: bool) {
    let iam_roles_result = ace_graph::iam_role::select_result(ace_graph::IamRoleFilter::All).await;
    let mut errors = Vec::new();
    let mut iam_roles_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid IAM role entries
    match iam_roles_result {
        Ok(iam_roles) => {
            for iam_role in iam_roles {
                match iam_role {
                    Ok(iam_role) => iam_roles_sorted.push(iam_role),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            println!("Error fetching IAM roles: {:#?}", e);
            return;
        }
    }

    // Sort IAM roles by their graphkey
    iam_roles_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for iam_role in iam_roles_sorted {
            println!("\n{:#?}", iam_role);
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey"]);
        for iam_role in iam_roles_sorted {
            table.add_row(vec![iam_role.graphkey.to_string()]);
        }

        println!("{}", table);
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{:#?}", error);
        }
    }
}
