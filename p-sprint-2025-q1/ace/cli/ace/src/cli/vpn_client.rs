use std::path::Path;

use ace_graph::GraphKeyExt;
use comfy_table::{presets, ContentArrangement, Table};
use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    CreateClient,
    AutoCommit,
    RevokeClient,
}

pub async fn list(verbose: bool, vpn_path: &Path) {
    let vpn_clients_result =
        ace_graph::vpn_client::select_result(&ace_graph::VpnClientFilter::All, vpn_path).await;
    let mut errors = Vec::new();
    let mut vpn_clients_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid vpn client entries
    match vpn_clients_result {
        Ok(vpn_clients) => {
            for vpn_client in vpn_clients {
                match vpn_client {
                    Ok(vpn_client) => vpn_clients_sorted.push(vpn_client),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching VPN clients: {:#?}", e);
            return;
        }
    }

    // Sort vpn clients by their graphkey
    vpn_clients_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for vpn_client in vpn_clients_sorted {
            println!("\n{:#?}", vpn_client);
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey", "Common Name", "Not Before", "Not After"]);
        for vpn_client in vpn_clients_sorted {
            table.add_row(vec![
                vpn_client.graphkey.to_string(),
                vpn_client.common_name,
                vpn_client.not_before.format("%Y-%m-%d").to_string(),
                vpn_client.not_after.format("%Y-%m-%d").to_string(),
            ]);
        }

        println!("{}", table);
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("Error: {:#?}", error);
        }
    }
}
pub async fn create_client(
    app: &ace_core::Application,
    username: &str,
) -> error_stack::Result<(), ErrorStack> {
    let client_config_path = ace_core::vpn::client::create(app, username)
        .await
        .change_context(ErrorStack::CreateClient)?;

    ace_core::git::autocommit(app, &"auto commit after vpn create-client".to_string())
        .await
        .change_context(ErrorStack::AutoCommit)?;

    println!("Wrote client config to: {}", client_config_path.display());

    Ok(())
}

pub async fn revoke_client(
    app: &ace_core::Application,
    username: &str,
) -> error_stack::Result<(), ErrorStack> {
    ace_core::vpn::client::revoke(app, username)
        .await
        .change_context(ErrorStack::RevokeClient)?;

    ace_core::git::autocommit(
        app,
        &format!("auto commit after vpn revoke-client {}", username),
    )
    .await
    .change_context(ErrorStack::AutoCommit)?;

    println!("Revoked client config for: {}", username);

    Ok(())
}
