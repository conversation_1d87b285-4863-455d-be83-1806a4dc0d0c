use ace_graph::Graph<PERSON>eyExt;
use comfy_table::{presets, ContentArrangement, Table};
use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    GenCert(String),
    Get<PERSON><PERSON>(String),
    GetTlsCert(ace_graph::TlsCert),
    InvalidGraphKey(String, String),
    NewTlsManager,
}

pub async fn ls(verbose: bool, ace_db_app: &ace_db::App) -> error_stack::Result<(), ErrorStack> {
    let manager = ace_core::tls::TlsManager::new(ace_db_app)
        .await
        .change_context(ErrorStack::NewTlsManager)?;

    let mut table: Table = Table::new();
    table.set_content_arrangement(ContentArrangement::Dynamic);

    if atty::is(atty::Stream::Stdout) {
        table.load_preset(presets::UTF8_FULL_CONDENSED);
    } else {
        table.load_preset(presets::NOTHING);
    }

    table.set_header(vec![
        "Graphkey",
        "Common Name",
        "Expiration time",
        "Subject Alternative Names",
    ]);

    let mut certs_sorted = manager.get_list().into_iter().collect::<Vec<_>>();

    certs_sorted.sort_by(|a, b| a.graphkey.to_string().cmp(&b.graphkey.to_string()));

    for cert in certs_sorted {
        if verbose {
            println!("\n{:#?}", cert);
        } else {
            table.add_row(vec![
                cert.graphkey.to_string(),
                cert.common_name.clone(),
                cert.expiration_time.to_string(),
                cert.subject_alternative_names.join(", "),
            ]);
        }
    }

    if !verbose {
        println!("{}", table);
    }

    Ok(())
}

pub async fn ls2(gk: &Option<String>, verbose: bool, ace_db_app: &ace_db::App) {
    let tlscert_filter = match gk {
        Some(gk) => match ace_graph::TlsCert::deserialize(gk) {
            Ok(gk) => ace_graph::TlsCertFilter::One(gk),
            Err(e) => {
                println!("Invalid graph key: {}", e);
                return;
            }
        },
        None => ace_graph::TlsCertFilter::All,
    };

    let tlscerts = match ace_graph::tlscert::select_result(&tlscert_filter, ace_db_app).await {
        Ok(tlscerts) => tlscerts,
        Err(e) => {
            println!("{:?}", e);
            return;
        }
    };

    let mut sorted_tlscerts: Vec<_> = tlscerts.into_iter().collect();

    sorted_tlscerts.sort_by(|a, b| {
        a.as_ref()
            .map(|x| x.graphkey.serialize())
            .unwrap_or_default()
            .cmp(
                &b.as_ref()
                    .map(|x| x.graphkey.serialize())
                    .unwrap_or_default(),
            )
    });

    if verbose {
        for tlscert in sorted_tlscerts {
            match tlscert {
                Ok(tlscert) => println!("\n{:#?}", tlscert),
                Err(e) => println!("\n{:#?}", e),
            }
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);

        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec![
            "Graphkey",
            "Common Name",
            "Expiration time",
            "Subject Alternative Names",
        ]);

        let mut errors = Vec::new();

        for tlscert in sorted_tlscerts {
            match tlscert {
                Ok(tlscert) => {
                    let expire = match tlscert.expiration_time {
                        Some(expire) => expire.to_string(),
                        None => "NA/Does Not Exist Yet".to_string(),
                    };

                    table.add_row(vec![
                        tlscert.graphkey.to_string(),
                        tlscert.common_name.clone(),
                        expire,
                        tlscert.subject_alternative_names.join(", "),
                    ]);
                }
                Err(e) => errors.push(e),
            }
        }

        println!("{}", table);

        if !errors.is_empty() {
            println!("\nErrors:");
            for error in errors {
                println!("{:#?}", error);
            }
        }
    }
}
pub async fn info(gk_tlscert: &str, ace_db_app: &ace_db::App) {
    match info_inner(gk_tlscert, ace_db_app).await {
        Ok(()) => {}
        Err(e) => println!("{:?}", e),
    }
}

pub async fn info_inner(
    gk_tlscert: &str,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<(), ErrorStack> {
    let gk_tlscert = match ace_graph::TlsCert::deserialize(gk_tlscert) {
        Ok(gk_developer) => gk_developer,
        Err(e) => error_stack::bail!(ErrorStack::InvalidGraphKey(gk_tlscert.to_owned(), e)),
    };

    let tlscert = ace_graph::tlscert::get(&gk_tlscert, ace_db_app)
        .await
        .change_context(ErrorStack::GetTlsCert(gk_tlscert))?;

    let expire = match tlscert.expiration_time {
        Some(expire) => expire.to_string(),
        None => "NA/Does Not Exist Yet".to_string(),
    };

    println!("tls cert summary:");
    println!("Graph Key:              {}", tlscert.graphkey.serialize());
    println!("Common Name:            {}", tlscert.common_name);
    println!("Expiration Time:        {}", expire);
    println!(
        "Subject Alternative Names:   {}",
        tlscert.subject_alternative_names.join(", ")
    );

    Ok(())
}

pub async fn gen_cert(
    app: &ace_core::Application,
    key: &str,
) -> error_stack::Result<(), ErrorStack> {
    let tlscert_gk: ace_graph::TlsCert = match ace_graph::GraphKeyExt::deserialize(key) {
        Ok(gk) => gk,
        Err(e) => error_stack::bail!(ErrorStack::InvalidGraphKey(key.to_owned(), e)),
    };

    let manager = ace_core::tls::TlsManager::new(&app.ace_db_app)
        .await
        .change_context(ErrorStack::NewTlsManager)?;
    let certificate = manager
        .get_cert(&tlscert_gk)
        .change_context(ErrorStack::GetCert(key.to_owned()))?;
    certificate
        .generate(app)
        .await
        .change_context(ErrorStack::GenCert(key.to_owned()))?;
    Ok(())
}
