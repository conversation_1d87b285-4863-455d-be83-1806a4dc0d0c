use error_stack::ResultExt;
use garbage::CNSL;
use tokio::io::AsyncWriteExt;
use tokio::process::Command;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    AutoCommit,
    ConfigServer,
    CreateCaPublicKey,
    FailedToOpenStdin,
    FailedToWaitForSshCommandToComplete,
    GetConfig,
    InitializeVpnCa,
    LoadConfigText,
    NextEntry,
    NoVpnConfigFound,
    ReadDir,
    RemoveDirectory,
    RemoveFile,
    SpawnSshCommand,
    SshCommandFailed(std::process::ExitStatus),
    WriteToStdinSshCommand,
}

pub async fn init(app: &ace_core::Application) -> error_stack::Result<(), ErrorStack> {
    ace_core::ca::init(app)
        .await
        .change_context(ErrorStack::InitializeVpnCa)?;
    ace_core::ca::create_ca_public_key(app)
        .await
        .change_context(ErrorStack::CreateCaPublicKey)?;
    ace_core::git::autocommit(app, &"auto commit after vpn init".to_string())
        .await
        .change_context(ErrorStack::AutoCommit)?;

    Ok(())
}

pub async fn create_ca_public_key(
    app: &ace_core::Application,
) -> error_stack::Result<(), ErrorStack> {
    ace_core::ca::create_ca_public_key(app)
        .await
        .change_context(ErrorStack::CreateCaPublicKey)?;

    ace_core::git::autocommit(
        app,
        &"auto commit after vpn create-ca-public-key".to_string(),
    )
    .await
    .change_context(ErrorStack::AutoCommit)?;

    Ok(())
}

pub async fn config_server(app: &ace_core::Application) -> error_stack::Result<(), ErrorStack> {
    let server_config_path = ace_core::vpn::server::create(app)
        .await
        .change_context(ErrorStack::ConfigServer)?;

    ace_core::git::autocommit(app, &"auto commit after vpn create-server".to_string())
        .await
        .change_context(ErrorStack::AutoCommit)?;

    println!("Wrote server config to: {}", server_config_path.display());

    Ok(())
}

pub async fn deploy_server(
    app: &ace_core::Application,
    use_public_ip: &bool,
    dump_script: &bool,
) -> error_stack::Result<(), ErrorStack> {
    let (vpn, vpn_client_subnet, subnet, key_path) = {
        let config = ace_graph::config::get(&ace_graph::Config::EtcConfig, &app.ace_db_app)
            .await
            .change_context(ErrorStack::GetConfig)?;

        (
            config.vpn,
            config.vpn_client_cidr_block,
            config.cidr_block,
            config.ace2_private_key_file_path,
        )
    };

    let vpn_config = match vpn {
        Some(vpn_config) => vpn_config,
        None => {
            error_stack::bail!(ErrorStack::NoVpnConfigFound);
        }
    };

    let ssh_user_at_host = match use_public_ip {
        true => format!("app@{}", &vpn_config.public_server_name),
        false => format!("app@{}", &vpn_config.private_server_name),
    };

    let config_text = match ace_core::vpn::server::load_config_text(app).await {
        Ok(config_text) => config_text,
        Err(e) => {
            return Err(e
                .change_context(ErrorStack::LoadConfigText)
                .attach_printable(
                    "Perhaps you need to generate the server config first?\n".to_string(),
                ));
        }
    };

    let encoded_config = data_encoding::BASE64.encode(config_text.as_bytes());

    // VPN users are in the 10.252.X.0/24 address space, therefore the MASQUERADE needs to be
    // from that space
    let mut iptables_lines = vec![format!(
        "sudo iptables -t nat -A POSTROUTING -s {} -d {} -j MASQUERADE",
        vpn_client_subnet, subnet
    )];

    // Add the route for any additional subnets
    for additional_subnet in vpn_config.additional_routed_subnets.iter() {
        iptables_lines.push(format!(
            "sudo iptables -t nat -A POSTROUTING -s {} -d {} -j MASQUERADE",
            vpn_client_subnet, additional_subnet
        ));
    }

    #[rustfmt::skip]
    let bash_script = CNSL!(r#"
        echo "Installing openvpn configuration..."
        echo "#, encoded_config, r#" | base64 --decode | sudo dd of=/etc/openvpn/server.conf

        echo "Installing iptables configuration..."
        sudo systemctl enable nftables
        sudo systemctl start nftables
        sudo iptables -F -t nat
        "#, iptables_lines.join("\n"), r#"
        sudo nft list ruleset | sudo dd of=/etc/nftables.conf
        
        echo "Enabling openvpn service..."
        sudo systemctl enable openvpn@server

        echo "Starting openvpn service..."
        sudo systemctl restart openvpn@server

        echo "Done installing openvpn configuration!"
    "#);

    if *dump_script {
        println!("{}", bash_script);
        return Ok(());
    }

    let mut child = Command::new("ssh")
        .arg("-T") // no pseudo-tty
        .arg("-i") // identity
        .arg(key_path)
        .arg(ssh_user_at_host)
        .arg("--")
        .arg("/bin/bash")
        .stdin(std::process::Stdio::piped())
        .spawn()
        .change_context(ErrorStack::SpawnSshCommand)?;

    {
        let mut stdin = match child.stdin.take() {
            Some(stdin) => stdin,
            None => error_stack::bail!(ErrorStack::FailedToOpenStdin),
        };

        stdin
            .write_all(bash_script.as_bytes())
            .await
            .change_context(ErrorStack::WriteToStdinSshCommand)?;
    }

    let status = child
        .wait()
        .await
        .change_context(ErrorStack::FailedToWaitForSshCommandToComplete)?;

    if !status.success() {
        error_stack::bail!(ErrorStack::SshCommandFailed(status));
    }

    println!("SSH Command was successful");

    Ok(())
}
