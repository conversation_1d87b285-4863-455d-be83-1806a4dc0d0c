use ace_graph::GraphKeyExt;
use comfy_table::{presets, ContentArrangement, Table};

pub async fn list(verbose: bool, ace_db_app: &ace_db::App) {
    let buckets_result =
        ace_graph::bucket::select_result(ace_graph::BucketFilter::All, ace_db_app).await;
    let mut errors = Vec::new();
    let mut buckets_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid buckets
    match buckets_result {
        Ok(buckets) => {
            for bucket in buckets {
                match bucket {
                    Ok(bucket) => buckets_sorted.push(bucket),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching buckets: {:#?}", e);
            return;
        }
    }

    // Sort buckets by their graphkey
    buckets_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for bucket in buckets_sorted {
            println!("{:#?}", bucket);
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey"]);
        for bucket in buckets_sorted {
            table.add_row(vec![bucket.graphkey.to_string()]);
        }

        println!("{}", table);
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{:#?}", error);
        }
    }
}
