use ace_graph::GraphKeyExt;
use comfy_table::{presets, ContentArrangement, Table};
pub async fn list(verbose: bool) {
    let local_files_result = ace_graph::lf::select_result(ace_graph::LocalFileFilter::All).await;
    let mut errors = Vec::new();
    let mut local_files_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid local file entries
    match local_files_result {
        Ok(local_files) => {
            for local_file in local_files {
                match local_file {
                    Ok(local_file) => local_files_sorted.push(local_file),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching local files: {:#?}", e);
            return;
        }
    }

    // Sort local files by their graphkey
    local_files_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for local_file in local_files_sorted {
            println!("\n{:#?}", local_file);
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey"]);
        for local_file in local_files_sorted {
            table.add_row(vec![local_file.graphkey.to_string()]);
        }

        println!("{}", table);
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("Error: {:#?}", error);
        }
    }
}
