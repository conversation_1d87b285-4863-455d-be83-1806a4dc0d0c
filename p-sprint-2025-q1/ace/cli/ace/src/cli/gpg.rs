use ace_core::gpg::{check_gpg_installation, gpg_info};
use ace_core::Application;
use ace_core::ErrorStack;
use error_stack::ResultExt;

pub async fn info(app: &Application, _verbose: bool) -> error_stack::Result<(), ErrorStack> {
    check_gpg_installation(app).change_context(ErrorStack::GpgNotInstalled)?;
    let keys = gpg_info().change_context(ErrorStack::GpgNotInstalled)?;
    print!("{}", keys);
    Ok(())
}
