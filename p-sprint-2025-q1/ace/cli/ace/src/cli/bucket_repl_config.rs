use ace_graph::GraphKeyExt;
use comfy_table::{presets, ContentArrangement, Table};

pub async fn list(verbose: bool) {
    let bucket_repl_configs_result =
        ace_graph::bucket_repl_config::select_result(ace_graph::BucketReplicationConfigFilter::All)
            .await;

    let mut errors = Vec::new();
    let mut bucket_repl_configs_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid items
    match bucket_repl_configs_result {
        Ok(bucket_repl_configs) => {
            for bucket_repl_config in bucket_repl_configs {
                match bucket_repl_config {
                    Ok(bucket_repl_config) => bucket_repl_configs_sorted.push(bucket_repl_config),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching bucket replication configurations: {:#?}", e);
            return;
        }
    }

    // Sort bucket replication configurations by their graphkey
    bucket_repl_configs_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for bucket_repl_config in bucket_repl_configs_sorted {
            println!("\n{:#?}", bucket_repl_config);
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey"]);
        for bucket_repl_config in bucket_repl_configs_sorted {
            table.add_row(vec![bucket_repl_config.graphkey.to_string()]);
        }

        println!("{}", table);
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{:#?}", error);
        }
    }
}
