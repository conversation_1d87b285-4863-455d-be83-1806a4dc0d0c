use ace_graph::GraphKeyExt;
use comfy_table::{presets, ContentArrangement, Table};

pub async fn list(verbose: bool, ace_db_app: &ace_db::App) {
    let vpc_result = ace_graph::vpc::get(&ace_graph::Vpc::Ace, ace_db_app).await;
    let mut errors = Vec::new();
    let mut vpcs = Vec::new();

    // Handle overall fetching errors or proceed to collect valid VPC entries
    match vpc_result {
        Ok(vpc) => vpcs.push(vpc),
        Err(e) => errors.push(e),
    }

    // Sort VPCs by their graphkey
    vpcs.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for vpc in vpcs {
            println!("\n{:#?}", vpc);
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey", "CIDR Block"]);
        for vpc in vpcs {
            table.add_row(vec![vpc.graphkey.serialize(), vpc.cidr_block.to_string()]);
        }

        println!("{}", table);
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{:#?}", error);
        }
    }
}
