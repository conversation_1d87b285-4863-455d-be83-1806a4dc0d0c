use chrono::TimeZone;
use comfy_table::{presets, Cell, Color, ContentArrangement, Table};
use std::path::PathBuf;

pub const LEVELONE: &str = "101";
pub const LEVELTWO: &str = "102";
pub const LEVELTHREE: &str = "103";

pub trait TableValues {
    fn get_values(&self) -> (&str, bool, String);
}

pub struct Conditions {
    pub init_paths: Vec<InitPathConditions>,
    pub init_flags: Vec<InitFlagConditions>,
    pub mature_paths: Vec<MaturePathConditions>,
    pub mature_flags: Vec<MatureFlagConditions>,
}

pub fn render_table_rows<T>(conditions: &Vec<T>, table: &mut Table, level: &str) -> bool
where
    T: TableValues,
{
    let mut rval = true;

    for condition in conditions {
        let (name, result, data) = condition.get_values();
        match result {
            true => {
                table.add_row(vec![level, name, "Yes", &data]);
            }
            false => {
                table.add_row(vec![
                    Cell::new(level).fg(Color::Red),
                    Cell::new(name).fg(Color::Red),
                    Cell::new("No").fg(Color::Red),
                    Cell::new(&data).fg(Color::Red),
                ]);
                rval = false;
            }
        }
    }

    rval
}

pub struct PathCondition {
    pub path: PathBuf,
    pub exists: bool,
}

impl PathCondition {
    pub fn new(path: PathBuf, exists: bool) -> Self {
        Self { path, exists }
    }
}

pub enum MaturePathConditions {
    AceBinExecutablePathExists(PathCondition),
    AceServerBinPathExists(PathCondition),
    AceServerTomlPathExists(PathCondition),
    CaPathExists(PathCondition),
    DataTerraformOutputPathExists(PathCondition),
    DataVersionFilePathExists(PathCondition),
    SshBinPathExists(PathCondition),
    TerraformStateFilePathExists(PathCondition),
}

impl TableValues for MaturePathConditions {
    fn get_values(&self) -> (&str, bool, String) {
        match self {
            MaturePathConditions::AceBinExecutablePathExists(value) => (
                "Ace Bin Executable Path Exists",
                value.exists,
                value.path.display().to_string(),
            ),
            MaturePathConditions::AceServerBinPathExists(value) => (
                "Ace Server Bin Path Exists",
                value.exists,
                value.path.display().to_string(),
            ),
            MaturePathConditions::AceServerTomlPathExists(value) => (
                "Ace Server Toml Path Exists",
                value.exists,
                value.path.display().to_string(),
            ),
            MaturePathConditions::CaPathExists(value) => (
                "CA Path Exists",
                value.exists,
                value.path.display().to_string(),
            ),
            MaturePathConditions::DataTerraformOutputPathExists(value) => (
                "Data Terraform Output Path Exists",
                value.exists,
                value.path.display().to_string(),
            ),
            MaturePathConditions::SshBinPathExists(value) => (
                "SSH Bin Path Exists",
                value.exists,
                value.path.display().to_string(),
            ),
            MaturePathConditions::TerraformStateFilePathExists(value) => (
                "Terraform State File Path Exists",
                value.exists,
                value.path.display().to_string(),
            ),
            MaturePathConditions::DataVersionFilePathExists(value) => (
                "Data Version File Path Exists",
                value.exists,
                value.path.display().to_string(),
            ),
        }
    }
}

pub enum MatureFlagConditions {
    IsAceServer { is_ace_server: bool, path: PathBuf },
    AceAmiExists { exists: bool, age_in_days: String },
    DevBoxAmiExists { exists: bool, age: String },
    DockerAmiExists { exists: bool, age: String },
    OpenVpnAmiExists { exists: bool, age: String },
    PostgresAmiExists { exists: bool, age: String },
    VideoprocAmiExists { exists: bool, age: String },
}

impl TableValues for MatureFlagConditions {
    fn get_values(&self) -> (&str, bool, String) {
        match self {
            MatureFlagConditions::IsAceServer {
                is_ace_server,
                path,
            } => ("Is Ace Server", *is_ace_server, path.display().to_string()),
            MatureFlagConditions::AceAmiExists {
                exists,
                age_in_days: age,
            } => ("Ace AMI Exists", *exists, format!("Age: {}", age)),
            MatureFlagConditions::DevBoxAmiExists { exists, age } => {
                ("DevBox AMI Exists", *exists, format!("Age: {}", age))
            }
            MatureFlagConditions::DockerAmiExists { exists, age } => {
                ("Docker AMI Exists", *exists, format!("Age: {}", age))
            }
            MatureFlagConditions::OpenVpnAmiExists { exists, age } => {
                ("OpenVPN AMI Exists", *exists, format!("Age: {}", age))
            }
            MatureFlagConditions::PostgresAmiExists { exists, age } => {
                ("Postgres AMI Exists", *exists, format!("Age: {}", age))
            }
            MatureFlagConditions::VideoprocAmiExists { exists, age } => {
                ("Videoproc AMI Exists", *exists, format!("Age: {}", age))
            }
        }
    }
}

pub enum EssentialSuggestedFixes {
    RunAceInit,
    AddVpnSectionToConfigToml,
}

impl std::fmt::Display for EssentialSuggestedFixes {
    fn fmt(&self, f: &mut std::fmt::Formatter) -> std::fmt::Result {
        write!(f, "Run `ace init`")
    }
}

pub enum InitPathConditions {
    Ace2PrivateKeyExists(PathCondition),
    Ace2PublicKeyExists(PathCondition),
    BinPathExists(PathCondition),
    DataPathExists(PathCondition),
    DockerPathExists(PathCondition),
    EasyTlsBinPathExists(PathCondition),
    EtcAccountFileExists(PathCondition),
    EtcConfigFileExists(PathCondition),
    EtcPathExists(PathCondition),
    GocryptfsBinPathExists(PathCondition),
    LocalFilePathExists(PathCondition),
    NebulaBinPathExists(PathCondition),
    NebulaCertBinPathExists(PathCondition),
    PackerBinPathExists(PathCondition),
    PackerPathExists(PathCondition),
    PackerPluginPathExists(PathCondition),
    SecureMountpointExists(PathCondition),
    SecureEncryptedExists(PathCondition),
    TempPathExists(PathCondition),
    TerraformBinPathExists(PathCondition),
    TerraformPathExists(PathCondition),
    VpnPathExists(PathCondition),
}

impl TableValues for InitPathConditions {
    fn get_values(&self) -> (&str, bool, String) {
        match self {
            InitPathConditions::Ace2PrivateKeyExists(value) => (
                "Ace2 Private Key Exists",
                value.exists,
                value.path.display().to_string(),
            ),
            InitPathConditions::Ace2PublicKeyExists(value) => (
                "Ace2 Public Key Exists",
                value.exists,
                value.path.display().to_string(),
            ),
            InitPathConditions::BinPathExists(value) => (
                "Bin Path Exists",
                value.exists,
                value.path.display().to_string(),
            ),
            InitPathConditions::DataPathExists(value) => (
                "Data Path Exists",
                value.exists,
                value.path.display().to_string(),
            ),
            InitPathConditions::DockerPathExists(value) => (
                "Docker Path Exists",
                value.exists,
                value.path.display().to_string(),
            ),
            InitPathConditions::EasyTlsBinPathExists(value) => (
                "EasyTLS Bin Path Exists",
                value.exists,
                value.path.display().to_string(),
            ),
            InitPathConditions::EtcAccountFileExists(value) => (
                "Etc Account File Exists",
                value.exists,
                value.path.display().to_string(),
            ),
            InitPathConditions::EtcConfigFileExists(value) => (
                "Etc Config File Exists",
                value.exists,
                value.path.display().to_string(),
            ),
            InitPathConditions::EtcPathExists(value) => (
                "Etc Path Exists",
                value.exists,
                value.path.display().to_string(),
            ),
            InitPathConditions::GocryptfsBinPathExists(value) => (
                "Gocryptfs Bin Path Exists",
                value.exists,
                value.path.display().to_string(),
            ),
            InitPathConditions::LocalFilePathExists(value) => (
                "Local File Path Exists",
                value.exists,
                value.path.display().to_string(),
            ),
            InitPathConditions::NebulaBinPathExists(value) => (
                "Nebula Bin Path Exists",
                value.exists,
                value.path.display().to_string(),
            ),
            InitPathConditions::NebulaCertBinPathExists(value) => (
                "Nebula Cert Bin Path Exists",
                value.exists,
                value.path.display().to_string(),
            ),
            InitPathConditions::PackerBinPathExists(value) => (
                "Packer Bin Path Exists",
                value.exists,
                value.path.display().to_string(),
            ),
            InitPathConditions::PackerPathExists(value) => (
                "Packer Path Exists",
                value.exists,
                value.path.display().to_string(),
            ),
            InitPathConditions::PackerPluginPathExists(value) => (
                "Packer Plugin Path Exists",
                value.exists,
                value.path.display().to_string(),
            ),
            InitPathConditions::SecureMountpointExists(value) => (
                "Secure Mountpoint Exists",
                value.exists,
                value.path.display().to_string(),
            ),
            InitPathConditions::SecureEncryptedExists(value) => (
                "Secure Encrypted Exists",
                value.exists,
                value.path.display().to_string(),
            ),
            InitPathConditions::TempPathExists(value) => (
                "Temp Path Exists",
                value.exists,
                value.path.display().to_string(),
            ),
            InitPathConditions::TerraformBinPathExists(value) => (
                "Terraform Bin Path Exists",
                value.exists,
                value.path.display().to_string(),
            ),
            InitPathConditions::TerraformPathExists(value) => (
                "Terraform Path Exists",
                value.exists,
                value.path.display().to_string(),
            ),
            InitPathConditions::VpnPathExists(value) => (
                "VPN Path Exists",
                value.exists,
                value.path.display().to_string(),
            ),
        }
    }
}

pub enum InitFlagConditions {
    DataPathCacheMatches { matches: bool, path: PathBuf },
}

impl TableValues for InitFlagConditions {
    fn get_values(&self) -> (&str, bool, String) {
        match self {
            InitFlagConditions::DataPathCacheMatches { matches, path } => (
                "Data Path Cache Matches",
                *matches,
                path.display().to_string(),
            ),
        }
    }
}

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    CouldNotDeterminePathOfCurrentExecutable,
    CouldNotFindGitRepo,
    CouldNotLoadPathFromRepo,
}

/// 'ace status' needs to work even if APP is not initialized?
pub async fn run(app: &ace_core::Application) {
    let mut table = create_new_table();
    table.set_header(vec!["Level", "Item", "Status", "Data"]);

    let mut suggested_fixes = vec![];

    // Check essential paths
    // Note: Any errors returned here are likely never going to be displayed to the user, since app is initialized before the cli ever runs.
    let conditions = match check_env(app).await {
        Ok(conditions) => conditions,
        Err(e) => {
            println!("Cannot Proceed - Error: {:#?}", e);
            return;
        }
    };

    // Check current executable:
    match std::env::current_exe() {
        Ok(current_exe) => {
            table.add_row(vec![
                LEVELONE,
                "Current Executable",
                "Yes",
                &current_exe.display().to_string(),
            ]);
        }
        Err(_) => {
            table.add_row(vec!["Current Executable", "??", "Could not determine path"]);
        }
    }

    let essential_path_conditions_met =
        render_table_rows(&conditions.init_flags, &mut table, LEVELONE);
    let essential_flag_conditions_met =
        render_table_rows(&conditions.init_paths, &mut table, LEVELONE);

    if !essential_path_conditions_met | !essential_flag_conditions_met {
        suggested_fixes.push(EssentialSuggestedFixes::RunAceInit);
    }

    // TODO: Add suggested fixes for mature conditions?
    let _mature_path_conditions_met =
        render_table_rows(&conditions.mature_flags, &mut table, LEVELTWO);
    let _mature_flag_conditions_met =
        render_table_rows(&conditions.mature_paths, &mut table, LEVELTWO);

    // Print out the results:
    println!("{}", table);

    // Print out suggested fixes:
    if !suggested_fixes.is_empty() {
        println!("Suggested Fixes:");
        for fix in suggested_fixes {
            println!("  {}", fix);
        }
    }
}

fn create_new_table() -> Table {
    let mut table = Table::new();
    table.set_content_arrangement(ContentArrangement::Dynamic);
    if atty::is(atty::Stream::Stdout) {
        table.load_preset(presets::UTF8_FULL_CONDENSED);
    } else {
        table.load_preset(presets::NOTHING);
    }

    table
}

pub async fn check_env(app: &ace_core::Application) -> Result<Conditions, ErrorStack> {
    // Specific binary paths:
    let nebula_bin_path = app.bin_path.join("nebula");
    let nebula_cert_bin_path = app.bin_path.join("nebula-cert");

    // Specific file paths:
    // (Note: Not all of these are ESSENTIAL for ace init)
    let ace2_private_key_path = app.data_path.join("ace2_key");
    let ace2_public_key_path = app.data_path.join("ace2_key.pub");
    let ace_server_toml_path = app.bin_path.join("ace-server.toml");
    let packer_plugin_config_path = app.packer_path.join("packer.pkr.hcl");
    let terraform_state_path = app.terraform_path.join("terraform.tfstate");

    // Other conditions:
    let ace_server_indicator_path = PathBuf::from("/etc/ace");
    let is_ace_server = ace_server_indicator_path.exists();

    // built-in Ami conditions:
    let ace_ami = ace_graph::ami::get(
        ace_graph::Ami::Packer(ace_graph::Packer::Ubuntu2204Ace2),
        &app.ace_db_app,
    )
    .await;
    let devbox_ami = ace_graph::ami::get(
        ace_graph::Ami::Packer(ace_graph::Packer::Ubuntu2204Devbox),
        &app.ace_db_app,
    )
    .await;
    let docker_ami = ace_graph::ami::get(
        ace_graph::Ami::Packer(ace_graph::Packer::Ubuntu2204Docker),
        &app.ace_db_app,
    )
    .await;
    let openvpn_ami = ace_graph::ami::get(
        ace_graph::Ami::Packer(ace_graph::Packer::Ubuntu2204Openvpn),
        &app.ace_db_app,
    )
    .await;
    let postgres_ami = ace_graph::ami::get(
        ace_graph::Ami::Packer(ace_graph::Packer::Ubuntu2204Postgresql),
        &app.ace_db_app,
    )
    .await;
    let videoproc_ami = ace_graph::ami::get(
        ace_graph::Ami::Packer(ace_graph::Packer::Ubuntu2204Videoproc),
        &app.ace_db_app,
    )
    .await;

    let essential_path_conditions = vec![
        InitPathConditions::Ace2PrivateKeyExists(PathCondition {
            exists: ace2_private_key_path.exists(),
            path: ace2_private_key_path,
        }),
        InitPathConditions::Ace2PublicKeyExists(PathCondition {
            exists: ace2_public_key_path.exists(),
            path: ace2_public_key_path,
        }),
        InitPathConditions::BinPathExists(PathCondition {
            exists: app.bin_path.exists(),
            path: app.bin_path.clone(),
        }),
        InitPathConditions::DataPathExists(PathCondition {
            exists: app.data_path.exists(),
            path: app.data_path.clone(),
        }),
        InitPathConditions::DockerPathExists(PathCondition {
            exists: app.docker_path.exists(),
            path: app.docker_path.clone(),
        }),
        InitPathConditions::EasyTlsBinPathExists(PathCondition {
            exists: app.easytls_bin_path.exists(),
            path: app.easytls_bin_path.clone(),
        }),
        InitPathConditions::EtcAccountFileExists(PathCondition {
            exists: app.etc_account_file_path.exists(),
            path: app.etc_account_file_path.clone(),
        }),
        InitPathConditions::EtcConfigFileExists(PathCondition {
            exists: app.etc_config_file_path.exists(),
            path: app.etc_config_file_path.clone(),
        }),
        InitPathConditions::EtcPathExists(PathCondition {
            exists: app.etc_path.exists(),
            path: app.etc_path.clone(),
        }),
        InitPathConditions::GocryptfsBinPathExists(PathCondition {
            exists: app.gocryptfs_bin_path.exists(),
            path: app.gocryptfs_bin_path.clone(),
        }),
        InitPathConditions::LocalFilePathExists(PathCondition {
            exists: app.local_file_path.exists(),
            path: app.local_file_path.clone(),
        }),
        InitPathConditions::NebulaBinPathExists(PathCondition {
            exists: nebula_bin_path.exists(),
            path: nebula_bin_path,
        }),
        InitPathConditions::NebulaCertBinPathExists(PathCondition {
            exists: nebula_cert_bin_path.exists(),
            path: nebula_cert_bin_path,
        }),
        InitPathConditions::PackerBinPathExists(PathCondition {
            exists: app.packer_bin_path.exists(),
            path: app.packer_bin_path.clone(),
        }),
        InitPathConditions::PackerPathExists(PathCondition {
            exists: app.packer_path.exists(),
            path: app.packer_path.clone(),
        }),
        InitPathConditions::PackerPluginPathExists(PathCondition {
            exists: packer_plugin_config_path.exists(),
            path: packer_plugin_config_path,
        }),
        InitPathConditions::SecureMountpointExists(PathCondition {
            exists: app.secure_mountpoint.exists(),
            path: app.secure_mountpoint.clone(),
        }),
        InitPathConditions::SecureEncryptedExists(PathCondition {
            exists: app.secure_encrypted.exists(),
            path: app.secure_encrypted.clone(),
        }),
        InitPathConditions::TempPathExists(PathCondition {
            exists: app.temp_path.exists(),
            path: app.temp_path.clone(),
        }),
        InitPathConditions::TerraformBinPathExists(PathCondition {
            exists: app.terraform_bin_path.exists(),
            path: app.terraform_bin_path.clone(),
        }),
        InitPathConditions::TerraformPathExists(PathCondition {
            exists: app.terraform_path.exists(),
            path: app.terraform_path.clone(),
        }),
        InitPathConditions::VpnPathExists(PathCondition {
            exists: app.vpn_path.exists(),
            path: app.vpn_path.clone(),
        }),
    ];

    let essential_flags = vec![InitFlagConditions::DataPathCacheMatches {
        matches: app.data_pathcache_matches,
        path: app.data_pathcache_path.clone(),
    }];

    let mature_path_conditions = vec![
        MaturePathConditions::AceBinExecutablePathExists(PathCondition {
            exists: app.bin_executable_path.exists(),
            path: app.bin_executable_path.clone(),
        }),
        MaturePathConditions::AceServerBinPathExists(PathCondition {
            exists: app.ace_server_bin_path.exists(),
            path: app.ace_server_bin_path.clone(),
        }),
        MaturePathConditions::AceServerTomlPathExists(PathCondition {
            exists: ace_server_toml_path.exists(),
            path: ace_server_toml_path,
        }),
        MaturePathConditions::CaPathExists(PathCondition {
            exists: app.ca_path.exists(),
            path: app.ca_path.clone(),
        }),
        MaturePathConditions::DataTerraformOutputPathExists(PathCondition {
            exists: app.data_terraform_output_path.exists(),
            path: app.data_terraform_output_path.clone(),
        }),
        MaturePathConditions::DataVersionFilePathExists(PathCondition {
            exists: app.data_version_file_path.exists(),
            path: app.data_version_file_path.clone(),
        }),
        MaturePathConditions::SshBinPathExists(PathCondition {
            exists: app.ssh_bin_path.exists(),
            path: app.ssh_bin_path.clone(),
        }),
        MaturePathConditions::TerraformStateFilePathExists(PathCondition {
            exists: terraform_state_path.exists(),
            path: terraform_state_path,
        }),
    ];

    let mature_flags = vec![
        MatureFlagConditions::IsAceServer {
            is_ace_server,
            path: ace_server_indicator_path,
        },
        MatureFlagConditions::AceAmiExists {
            exists: ace_ami.is_ok(),
            age_in_days: {
                match ace_ami {
                    Ok(ami) => calculate_days_ago(ami.build_timestamp.unwrap_or_default()),
                    Err(_) => "N/A".to_string(),
                }
            },
        },
        MatureFlagConditions::DevBoxAmiExists {
            exists: devbox_ami.is_ok(),
            age: {
                match devbox_ami {
                    Ok(ami) => calculate_days_ago(ami.build_timestamp.unwrap_or_default()),
                    Err(_) => "N/A".to_string(),
                }
            },
        },
        MatureFlagConditions::DockerAmiExists {
            exists: docker_ami.is_ok(),
            age: {
                match docker_ami {
                    Ok(ami) => calculate_days_ago(ami.build_timestamp.unwrap_or_default()),
                    Err(_) => "N/A".to_string(),
                }
            },
        },
        MatureFlagConditions::OpenVpnAmiExists {
            exists: openvpn_ami.is_ok(),
            age: {
                match openvpn_ami {
                    Ok(ami) => calculate_days_ago(ami.build_timestamp.unwrap_or_default()),
                    Err(_) => "N/A".to_string(),
                }
            },
        },
        MatureFlagConditions::PostgresAmiExists {
            exists: postgres_ami.is_ok(),
            age: {
                match postgres_ami {
                    Ok(ami) => calculate_days_ago(ami.build_timestamp.unwrap_or_default()),
                    Err(_) => "N/A".to_string(),
                }
            },
        },
        MatureFlagConditions::VideoprocAmiExists {
            exists: videoproc_ami.is_ok(),
            age: {
                match videoproc_ami {
                    Ok(ami) => calculate_days_ago(ami.build_timestamp.unwrap_or_default()),
                    Err(_) => "N/A".to_string(),
                }
            },
        },
    ];

    Ok(Conditions {
        init_paths: essential_path_conditions,
        init_flags: essential_flags,
        mature_paths: mature_path_conditions,
        mature_flags,
    })
}

fn calculate_days_ago(timestamp: u64) -> String {
    let now = chrono::Utc::now();
    let creation_time = match i64::try_from(timestamp) {
        Ok(timestamp) => timestamp,
        Err(_) => return "—".to_string(),
    };

    let creation_utc = match chrono::Utc.timestamp_opt(creation_time, 0).single() {
        Some(time) => time,
        None => return "—".to_string(),
    };

    if creation_utc > now {
        return "—Err—".to_string();
    }

    let age = now.signed_duration_since(creation_utc);
    let days_old = age.num_days();

    format!("{} days ago", days_old)
}
