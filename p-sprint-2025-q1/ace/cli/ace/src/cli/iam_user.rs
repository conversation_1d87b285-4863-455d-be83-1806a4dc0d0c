use ace_graph::GraphKeyExt;
use comfy_table::{presets, ContentArrangement, Table};

pub async fn list(verbose: bool) {
    let iam_users_result = ace_graph::iam_user::select_result(ace_graph::IamUserFilter::All).await;
    let mut errors = Vec::new();
    let mut iam_users_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid IAM user entries
    match iam_users_result {
        Ok(iam_users) => {
            for iam_user in iam_users {
                match iam_user {
                    Ok(iam_user) => iam_users_sorted.push(iam_user),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            println!("Error fetching IAM users: {:#?}", e);
            return;
        }
    }

    // Sort IAM users by their graphkey
    iam_users_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for iam_user in iam_users_sorted {
            println!("\n{:#?}", iam_user);
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey"]);
        for iam_user in iam_users_sorted {
            table.add_row(vec![iam_user.graphkey.to_string()]);
        }

        println!("{}", table);
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{:#?}", error);
        }
    }
}
