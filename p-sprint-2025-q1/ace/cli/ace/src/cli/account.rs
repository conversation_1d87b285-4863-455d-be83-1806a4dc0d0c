use ace_graph::GraphKeyExt;
use comfy_table::{presets, ContentArrangement, Table};

pub async fn list(verbose: bool, ace_db_app: &ace_db::App) {
    let accounts_result =
        ace_graph::account::select_result(&ace_graph::AccountFilter::All, ace_db_app).await;
    let mut errors = Vec::new();
    let mut accounts_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid accounts
    match accounts_result {
        Ok(accounts) => {
            for account in accounts {
                match account {
                    Ok(account) => accounts_sorted.push(account),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching accounts: {:#?}", e);
            return;
        }
    }

    // Sort accounts by graphkey
    accounts_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for account in accounts_sorted {
            println!("\n{:#?}", account);
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec![
            "Graphkey",
            "Account Key",
            "Public Domain",
            "Private Domain",
        ]);

        for account in accounts_sorted {
            table.add_row(vec![
                account.graphkey.to_string(),
                account.account_key.to_string(),
                account.public_domain,
                account.private_domain,
            ]);
        }

        println!("{}", table);
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{:#?}", error);
        }
    }
}
