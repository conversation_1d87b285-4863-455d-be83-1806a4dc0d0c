use clap::{Parser, Subcommand};
use error_stack::ResultExt;
use garbage::STSL;
use std::path::PathBuf;
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

/// A fictional versioning CLI
#[derive(Debug, Parser)] // requires `derive` feature
#[command(name = "ace")]
#[command(about = "AppCove Config Engine", long_about = None)]
struct Cli {
    #[clap(
        short = 'C',
        long,
        help = "Run in this directory instead of the current directory",
        default_value = "."
    )]
    current_directory: String,

    #[command(subcommand)]
    command: Commands,
}

#[derive(Debug, Subcommand)]
enum Commands {
    #[clap(about = "Account commands")]
    #[command(arg_required_else_help = true)]
    Account(AccountSubCommand),

    #[clap(about = "ACE Agent Sub Command")]
    #[command(arg_required_else_help = true)]
    Agent(AgentCommandOpts),

    #[clap(about = "Ami commands")]
    #[command(arg_required_else_help = true)]
    Ami(AmiSubCommand),

    #[clap(about = "App commands")]
    #[command(arg_required_else_help = true)]
    App(AppSubCommand),

    #[clap(about = "Prints information about this application environment")]
    AppEnv,

    #[clap(
        about = "Print AWS CLI call status with credentials",
        long_about = "Equivalent to `git add -A; git commit -m 'checkpoint'."
    )]
    Aws(AllTheArgs),

    #[clap(about = "Asset commands")]
    #[command(arg_required_else_help = true)]
    Asset(AssetSubCommand),

    #[clap(about = "Brdst commands")]
    #[command(arg_required_else_help = true)]
    Brdst(BrdstSubCommand),

    #[clap(about = "Brsrc commands")]
    #[command(arg_required_else_help = true)]
    Brsrc(BrsrcSubCommand),

    #[clap(about = "Creates terraform and packer files.")]
    Build,

    #[clap(about = "Bucket commands")]
    Bucket(BucketCommandOpts),

    #[clap(about = "Bucket Policy commands")]
    #[command(arg_required_else_help = true)]
    BucketPolicy(BucketPolicySubCommand),

    #[clap(about = "Bucket Policy Pub Access Blk commands")]
    #[command(arg_required_else_help = true)]
    BucketPubAccessBlk(BucketPubAccessBlkSubCommand),

    #[clap(about = "Bucket Replication Config commands")]
    #[command(arg_required_else_help = true)]
    BucketReplConfig(BucketReplConfigSubCommand),

    #[clap(about = "Bucket Versioning commands")]
    #[command(arg_required_else_help = true)]
    BucketVersion(BucketVersionSubCommand),

    #[clap(about = "certificate authority commands")]
    #[command(arg_required_else_help = true)]
    Ca(CaSubCommand),

    #[clap(about = "Prints finalized configuration information after processing")]
    Config,

    #[clap(about = "Perform various checks")]
    #[command(arg_required_else_help = true)]
    Check(CheckSubCommand),

    #[clap(about = "Creates a commit with an (optionally) provided message")]
    Commit(Commit),

    #[clap(about = "Developer commands")]
    #[command(arg_required_else_help = true)]
    Developer(DeveloperSubCommand),

    #[clap(about = "DeveloperApp commands")]
    #[command(arg_required_else_help = true)]
    DevApp(DevAppSubCommand),

    #[clap(about = "Print DNS Lookup results for a given domain")]
    Dns(Domain),

    #[clap(about = "DnsRecord commands")]
    #[command(arg_required_else_help = true)]
    DnsRec(DnsRecCommandOpts),

    #[clap(about = "DnsZone commands")]
    #[command(arg_required_else_help = true)]
    DnsZone(DnsZoneCommandOpts),

    #[clap(about = "Docker commands")]
    #[command(arg_required_else_help = true)]
    Docker(DockerSubCommand),

    #[clap(about = "Domain commands")]
    #[command(arg_required_else_help = true)]
    Domain(DomainSubCommand),

    #[clap(about = "Ecr commands")]
    #[command(arg_required_else_help = true)]
    Ecr(EcrCommandOpts),

    #[clap(about = "EcrPublic commands")]
    #[command(arg_required_else_help = true)]
    EcrPublic(EcrPubCommandOpts),

    #[clap(about = "Elastic IP commands")]
    #[command(arg_required_else_help = true)]
    Eip(EipCommandOpts),

    #[clap(about = "Gpg commands")]
    #[command(arg_required_else_help = true)]
    Gpg(GpgCommandOpts),

    #[clap(about = "Host related commands")]
    #[command(arg_required_else_help = true)]
    Host(HostCommandOpts),

    #[clap(about = "Elastic IP Association commands")]
    #[command(arg_required_else_help = true)]
    EipAssoc(EipAssocCommandOpts),

    #[clap(about = "IamAccessKey commands")]
    #[command(arg_required_else_help = true)]
    IamAccessKey(IamAccessKeyCommandOpts),

    #[clap(about = "IamRolePolicyAttach commands")]
    #[command(arg_required_else_help = true)]
    IamRolePolicyAttach(IamRolePolicyAttachCommandOpts),

    #[clap(about = "IamPolicy commands")]
    #[command(arg_required_else_help = true)]
    IamRolePolicy(IamRolePolicyCommandOpts),

    #[clap(about = "IamUserPolicyAttach commands")]
    #[command(arg_required_else_help = true)]
    IamUserPolicyAttach(IamUserPolicyAttachCommandOpts),

    #[clap(about = "IamRole commands")]
    #[command(arg_required_else_help = true)]
    IamRole(IamRoleCommandOpts),

    #[clap(about = "IamUser commands")]
    #[command(arg_required_else_help = true)]
    IamUser(IamUserCommandOpts),

    #[clap(about = "Internet Gateway commands")]
    #[command(arg_required_else_help = true)]
    Igw(IgwCommandOpts),

    #[clap(about = "Begin initialization sequence of a new local environment")]
    Init,

    #[clap(about = "Instance Profile commands")]
    #[command(arg_required_else_help = true)]
    InsProfile(InsProfileSubCommand),

    #[clap(about = "Instance commands")]
    #[command(arg_required_else_help = true)]
    Instance(InstanceSubCommand),

    #[clap(about = "Keypair commands")]
    #[command(arg_required_else_help = true)]
    Keypair(KeypairOpts),

    #[clap(about = "Local File commands")]
    #[command(arg_required_else_help = true)]
    Lf(LocalFileSubCommand),

    #[clap(about = "Prints the help in markdown format to stdout")]
    MarkdownHelp,

    #[clap(about = "Mediaproctor commands")]
    MP(MediaproctorOpts),

    // #[clap(about = "Print ACE directory transfer status to the server")]
    #[clap(
        about = "Move local environment to remote ace instance, install keys, and start ace-server"
    )]
    MoveToAce,

    #[clap(about = "Print MySQL sanitized image build status")]
    Mysqldevimg(MysqldevimgOpts),

    #[clap(about = "Nat Gateway commands")]
    #[command(arg_required_else_help = true)]
    NatGateway(NatGatewaySubCommand),

    #[clap(about = "Print packer run status")]
    #[command(arg_required_else_help = true)]
    Pkr(PackerSubCommand),

    #[clap(about = "Peering Connection commands")]
    #[command(arg_required_else_help = true)]
    Peercon(PeerconSubCommand),

    #[clap(
        about = "Print packer run status",
        long_about = "This command will run packer with the provided arguments.\n\nIt sets up the packer environment from LOCAL.env\nand runs it in the packer sub directory."
    )]
    Packer(AllTheArgs),

    #[clap(about = "Print route table association status")]
    #[command(arg_required_else_help = true)]
    RtAssoc(RouteTableAssocSubCommand),

    #[clap(about = "Print route table status")]
    #[command(arg_required_else_help = true)]
    Rt(RouteTableSubCommand),

    #[clap(about = "Print route table route status")]
    #[command(arg_required_else_help = true)]
    Rtr(RoutTableRouteSubCommand),

    #[clap(about = "./secure filesystem operations")]
    #[command(arg_required_else_help = true)]
    Secure(SecureSubCommand),

    #[clap(about = "Security Groups")]
    #[command(arg_required_else_help = true)]
    Sg(SgSubCommand),

    #[clap(about = "Security Group Rules")]
    #[command(arg_required_else_help = true)]
    Sgr(SgrSubCommand),

    #[clap(
        about = "Print SSH connection status to the server",
        long_about = STSL!(r#"
            This command will provide ssh with the proper credentials to ssh to anything that 
            ace has access to.

            It can be called in these ways:
            $ ace ssh ace-public 
            $ ace ssh ace-private
            $ ace ssh ace
            $ ace ssh vpn
            $ ace ssh graylog
            $ ace ssh some.server.example.com

            If the server name is not fully qualified, it will be assumed to be at .{region}.{domain}.

            TODO: more here
        "#)
    )]
    Ssh(SshOpts),

    Status,

    #[clap(about = "Subnet commands")]
    #[command(arg_required_else_help = true)]
    Subnet(SubnetCommandOpts),

    #[clap(about = "Print server update status")]
    SystemUpdate,

    #[clap(
        about = "Print terraform run status",
        long_about = STSL!(r#"
            This command will run terraform with the provided arguments.

            It sets up the terraform environment from LOCAL.env and runs it in 
            the terraform sub directory.
        "#)
    )]
    Tf(AllTheArgs),

    #[clap(about = "Tls commands")]
    #[command(arg_required_else_help = true)]
    Tlscert(TlscertCommandOpts),

    #[clap(about = "Print TLS private key status")]
    #[command(arg_required_else_help = true)]
    TlsPrivkey(TlsPrivateKeySubCommand),

    #[clap(about = "Print ACE installation upgrade status")]
    Upgrade(UpgradeOptions),

    #[clap(about = "User commands")]
    #[command(arg_required_else_help = true)]
    User(UserSubCommand),

    #[clap(about = "Print the ACE version")]
    Version,

    #[clap(about = "Vpc commands")]
    #[command(arg_required_else_help = true)]
    Vpc(VpcCommandOpts),

    #[clap(about = "VPN commands")]
    #[command(arg_required_else_help = true)]
    Vpn(VpnCommandOpts),

    #[clap(about = "VPN client commands")]
    VpnClient(VpnClientCommand),
}

#[derive(Debug, Parser)]
struct AccountSubCommand {
    #[clap(subcommand)]
    pub command: AccountSubCommands,
}

#[derive(Debug, Parser)]
enum AccountSubCommands {
    #[clap(about = "List accounts - optionally filtered by account key")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct AgentCommandOpts {
    #[clap(subcommand)]
    pub command: AgentCommandEnum,
}

#[derive(Debug, Subcommand)]
enum AgentCommandEnum {
    #[clap(about = "Prints command to install agent on a linux system")]
    PrintInstallCommand,
}

#[derive(Debug, Parser)]
struct AllTheArgs {
    args: Vec<String>,
}

#[derive(Debug, Parser)]
struct AmiSubCommand {
    #[clap(subcommand)]
    pub command: AmiSubCommands,
}

#[derive(Debug, Parser)]
enum AmiSubCommands {
    #[clap(about = "List amis")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct AppSubCommand {
    #[clap(subcommand)]
    pub command: AppSubCommands,
}

#[derive(Debug, Parser)]
enum AppSubCommands {
    #[clap(about = "Print a summary of information about a specific app")]
    Info { gk_app: String },
    #[clap(about = "List apps - optionally filtered")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct AssetSubCommand {
    #[clap(subcommand)]
    pub command: AssetSubCommands,
}

#[derive(Debug, Parser)]
enum AssetSubCommands {
    #[clap(about = "List all assets")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct BrdstSubCommand {
    #[clap(subcommand)]
    pub command: BrdstSubCommands,
}

#[derive(Debug, Parser)]
enum BrdstSubCommands {
    #[clap(about = "List brdsts - optionally filtered")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct BrsrcSubCommand {
    #[clap(subcommand)]
    pub command: BrsrcSubCommands,
}

#[derive(Debug, Parser)]
enum BrsrcSubCommands {
    #[clap(about = "List brsrcs - optionally filtered")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct BucketCommandOpts {
    #[clap(subcommand)]
    pub command: BucketCommandEnum,
}

#[derive(Debug, Subcommand)]
enum BucketCommandEnum {
    #[clap(about = "List buckets")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct BucketPolicySubCommand {
    #[clap(subcommand)]
    pub command: BucketPolicySubCommands,
}

#[derive(Debug, Parser)]
enum BucketPolicySubCommands {
    #[clap(about = "List bucket policies")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct BucketPubAccessBlkSubCommand {
    #[clap(subcommand)]
    pub command: BucketPubAccessBlkSubCommands,
}

#[derive(Debug, Parser)]
enum BucketPubAccessBlkSubCommands {
    #[clap(about = "List bucket public access blocks")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct BucketReplConfigSubCommand {
    #[clap(subcommand)]
    pub command: BucketReplConfigSubCommands,
}

#[derive(Debug, Parser)]
enum BucketReplConfigSubCommands {
    #[clap(about = "List bucket replication configs")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct BucketVersionSubCommand {
    #[clap(subcommand)]
    pub command: BucketVersionSubCommands,
}

#[derive(Debug, Parser)]
enum BucketVersionSubCommands {
    #[clap(about = "List bucket versions")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct CaSubCommand {
    #[clap(subcommand)]
    pub command: CaSubCommands,
}

#[derive(Debug, Parser)]
enum CaSubCommands {
    #[clap(about = "Initialize the certificate authority")]
    Init,
}

#[derive(Debug, Parser)]
struct CheckSubCommand {
    #[clap(subcommand)]
    pub command: CheckSubCommands,
}

#[derive(Debug, Parser)]
enum CheckSubCommands {
    #[clap(about = "Check ns records for the current environment.")]
    Ns,
    #[clap(about = "List all possible checks to run.")]
    Ls,
}

#[derive(Debug, Parser)]
struct Commit {
    #[clap(short)]
    message: Option<String>,
}

#[derive(Debug, Parser)]
struct DeveloperSubCommand {
    #[clap(subcommand)]
    pub command: DeveloperSubCommands,
}

#[derive(Debug, Parser)]
enum DeveloperSubCommands {
    #[clap(about = "Print a summary of information about a specific developer")]
    Info { gk_developer: String },

    #[clap(about = "List developers")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct DevAppSubCommand {
    #[clap(subcommand)]
    pub command: DevAppSubCommandEnum,
}

#[derive(Debug, Parser)]
enum DevAppSubCommandEnum {
    #[clap(about = "List devapps")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct DnsRecCommandOpts {
    #[clap(subcommand)]
    pub command: DnsRecSubCommand,
}

#[derive(Debug, Parser)]
enum DnsRecSubCommand {
    #[clap(about = "List dns records")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct DnsZoneCommandOpts {
    #[clap(subcommand)]
    pub command: DnsZoneSubCommand,
}

#[derive(Debug, Parser)]
enum DnsZoneSubCommand {
    #[clap(about = "List dns zones")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct DockerSubCommand {
    #[clap(subcommand)]
    pub command: DockerSubCommands,
}

#[derive(Debug, Parser)]
enum DockerSubCommands {
    #[clap(about = "Build docker")]
    Build { gk_docker: String },
    #[clap(about = "Get docker info")]
    Info { gk_docker: String },
    #[clap(about = "List dockers")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
    #[clap(about = "Push docker")]
    Push { gk_docker: String },
}

#[derive(Debug, Parser)]
struct Domain {
    name: String,
}

#[derive(Debug, Parser)]
struct DomainSubCommand {
    #[clap(subcommand)]
    pub command: DomainSubCommands,
}

#[derive(Debug, Parser)]
enum DomainSubCommands {
    #[clap(about = "List domains - optionally filtered by name")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct EcrCommandOpts {
    #[clap(subcommand)]
    pub command: EcrSubCommand,
}

#[derive(Debug, Parser)]
enum EcrSubCommand {
    #[clap(about = "List ECR Repositories")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct EcrPubCommandOpts {
    #[clap(subcommand)]
    pub command: EcrPubSubCommand,
}

#[derive(Debug, Parser)]
enum EcrPubSubCommand {
    #[clap(about = "List ECR Public Repositories")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct EipCommandOpts {
    #[clap(subcommand)]
    pub command: EipSubCommand,
}

#[derive(Debug, Parser)]
enum EipSubCommand {
    #[clap(about = "List elastic ips")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct GpgCommandOpts {
    #[clap(subcommand)]
    pub command: GpgSubCommand,
}

#[derive(Debug, Parser)]
enum GpgSubCommand {
    #[clap(about = "Info about GPG keys")]
    Info {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct HostCommandOpts {
    #[clap(subcommand)]
    pub command: HostSubCommand,
}

#[derive(Debug, Parser)]
enum HostSubCommand {
    #[clap(about = "List hosts")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct EipAssocCommandOpts {
    #[clap(subcommand)]
    pub command: EipAssocSubCommand,
}

#[derive(Debug, Parser)]
enum EipAssocSubCommand {
    #[clap(about = "List elastic ip associations")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct IamAccessKeyCommandOpts {
    #[clap(subcommand)]
    pub command: IamAccessKeySubCommand,
}

#[derive(Debug, Parser)]
enum IamAccessKeySubCommand {
    #[clap(about = "List Iam Access Keys")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct IamRolePolicyAttachCommandOpts {
    #[clap(subcommand)]
    pub command: IamRolePolicyAttachSubCommand,
}

#[derive(Debug, Parser)]
enum IamRolePolicyAttachSubCommand {
    #[clap(about = "List Iam Role Policy Attachments")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct IamRolePolicyCommandOpts {
    #[clap(subcommand)]
    pub command: IamRolePolicySubCommand,
}

#[derive(Debug, Parser)]
enum IamRolePolicySubCommand {
    #[clap(about = "List Iam Policies")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct IamRoleCommandOpts {
    #[clap(subcommand)]
    pub command: IamRoleSubCommand,
}

#[derive(Debug, Parser)]
enum IamRoleSubCommand {
    #[clap(about = "List Iam Roles")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct IamUserPolicyAttachCommandOpts {
    #[clap(subcommand)]
    pub command: IamUserPolicyAttachSubCommand,
}

#[derive(Debug, Parser)]
enum IamUserPolicyAttachSubCommand {
    #[clap(about = "List Iam User Policy Attachments")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct IamUserCommandOpts {
    #[clap(subcommand)]
    pub command: IamUserSubCommand,
}

#[derive(Debug, Parser)]
enum IamUserSubCommand {
    #[clap(about = "List Iam Users")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct IgwCommandOpts {
    #[clap(subcommand)]
    pub command: IgwSubCommand,
}

#[derive(Debug, Parser)]
enum IgwSubCommand {
    #[clap(about = "List Internet Gateways")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct InsProfileSubCommand {
    #[clap(subcommand)]
    pub command: InsProfileSubCommands,
}

#[derive(Debug, Parser)]
enum InsProfileSubCommands {
    #[clap(about = "List instance profiles")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct InstanceSubCommand {
    #[clap(subcommand)]
    pub command: InstanceSubCommands,
}

#[derive(Debug, Parser)]
enum InstanceSubCommands {
    #[clap(about = "List instances - optionally filtered by name")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct KeypairOpts {
    #[clap(subcommand)]
    pub command: KeypairCommand,
}

#[derive(Debug, Parser)]
enum KeypairCommand {
    #[clap(about = "Create a new keypair")]
    Add,

    #[clap(about = "List keypairs - optionally filtered")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct LocalFileSubCommand {
    #[clap(subcommand)]
    pub command: LocalFileSubCommands,
}

#[derive(Debug, Parser)]
enum LocalFileSubCommands {
    #[clap(about = "List local files - optionally filtered by id")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct MediaproctorOpts {
    #[clap(subcommand)]
    pub command: MediaproctorEnum,
}

#[derive(Debug, Subcommand)]
enum MediaproctorEnum {
    #[clap(about = "Launch processing job on server")]
    MpProcess { mp_name: String, job_path: PathBuf },

    #[clap(about = "Launch streaming job on server")]
    MpStream { mp_name: String, job_url: String },

    #[clap(about = "Get Status of instances")]
    InstanceStatus {
        mp_name: String,
        instance_ids: Vec<String>,
    },

    #[clap(about = "List instances - optionally filtered by id")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct MysqldevimgOpts {
    #[clap(subcommand)]
    pub command: Option<MysqldevimgCommand>,
}

#[derive(Debug, Subcommand)]
enum MysqldevimgCommand {
    Build {
        /// The argument for the build command
        #[clap(help = "The identifer (*) in the following: mysqldevimg.*.toml")]
        identifier: String,
    },
    #[clap(about = "Push images")]
    Push {
        /// The argument for the build command
        #[clap(help = "The identifer (*) in the following: mysqldevimg.*.toml")]
        identifier: String,
    },
    #[clap(about = "List images")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct NatGatewaySubCommand {
    #[clap(subcommand)]
    pub command: NatGatewaySubCommands,
}

#[derive(Debug, Parser)]
enum NatGatewaySubCommands {
    #[clap(about = "List nat gateways")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct PackerSubCommand {
    #[clap(subcommand)]
    pub command: PackerSubCommands,
}

#[derive(Debug, Parser)]
enum PackerSubCommands {
    #[clap(about = "List packers")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct PeerconSubCommand {
    #[clap(subcommand)]
    pub command: PeerconSubCommands,
}

#[derive(Debug, Parser)]
enum PeerconSubCommands {
    #[clap(about = "List peercons - optionally filtered by id")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct RouteTableAssocSubCommand {
    #[clap(subcommand)]
    pub command: RouteTableAssocSubCommands,
}

#[derive(Debug, Parser)]
enum RouteTableAssocSubCommands {
    #[clap(about = "List route table associations")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct RouteTableSubCommand {
    #[clap(subcommand)]
    pub command: RouteTableSubCommands,
}

#[derive(Debug, Parser)]
enum RouteTableSubCommands {
    #[clap(about = "List route tables - optionally filtered by id")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct RoutTableRouteSubCommand {
    #[clap(subcommand)]
    pub command: RoutTableRouteSubCommands,
}

#[derive(Debug, Parser)]
enum RoutTableRouteSubCommands {
    #[clap(about = "List route table routes")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct SecureSubCommand {
    #[clap(subcommand)]
    pub command: SecureSubCommands,
}

#[derive(Debug, Parser)]
enum SecureSubCommands {
    #[clap(about = "Mount filesystem")]
    Mount {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },

    #[clap(about = "Unmount filesystem")]
    Unmount {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },

    #[clap(about = "Information about filesystem")]
    Info {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct SgSubCommand {
    #[clap(subcommand)]
    pub command: SgSubCommands,
}

#[derive(Debug, Parser)]
enum SgSubCommands {
    #[clap(about = "List security groups")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct SgrSubCommand {
    #[clap(subcommand)]
    pub command: SgrSubCommands,
}

#[derive(Debug, Parser)]
enum SgrSubCommands {
    #[clap(about = "List security group rules")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct SshOpts {
    server: String,
    args: Vec<String>,
}

#[derive(Debug, Parser)]
struct SubnetCommandOpts {
    #[clap(subcommand)]
    pub command: SubnetSubCommand,
}

#[derive(Debug, Parser)]
enum SubnetSubCommand {
    #[clap(about = "List subnets")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct TlscertCommandOpts {
    #[clap(subcommand)]
    pub command: TlscertCommandEnum,
}

#[derive(Debug, Subcommand)]
enum TlscertCommandEnum {
    #[clap(about = "List Certificates via TlsManager")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },

    #[clap(about = "List Certificates via ace-graph")]
    Ls2 {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
        gk: Option<String>,
    },

    #[clap(about = "Get Certificate Info")]
    Info { gk_tlscert: String },

    #[clap(about = "Generate Certificate")]
    Gen { key: String },
}

#[derive(Debug, Parser)]
struct TlsPrivateKeySubCommand {
    #[clap(subcommand)]
    pub command: TlsPrivateKeySubCommands,
}

#[derive(Debug, Parser)]
enum TlsPrivateKeySubCommands {
    #[clap(about = "List tls private keys")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct UpgradeOptions {
    #[clap(long, help = "The version to upgrade to")]
    fixup: bool,
}

#[derive(Debug, Parser)]
struct UserSubCommand {
    #[clap(subcommand)]
    pub command: UserSubCommands,
}

#[derive(Debug, Parser)]
enum UserSubCommands {
    #[clap(about = "Print a summary of information about a specific user")]
    Info { gk_user: String },
    #[clap(about = "List users")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct VpnCommandOpts {
    #[clap(subcommand)]
    pub command: VpnServerCommandEnum,
}

#[derive(Debug, Subcommand)]
enum VpnServerCommandEnum {
    #[clap(about = "Initialize the data/ca directory with easyrsa")]
    Init,

    #[clap(about = "Create the server configuration")]
    Config,

    #[clap(
        about = "Extract the ca public key in OpenSSH format.  Auto runs during `ace vpn init`."
    )]
    CreateCaPublicKey,

    // deploy server
    #[clap(about = "Deploy the server configuration")]
    DeployServer {
        #[clap(long, help = "Use the public ip instead of the private ip")]
        use_public_ip: bool,

        // dump-script
        #[clap(long, help = "Dump the script to stdout instead of running it")]
        dump_script: bool,
    },
}

#[derive(Debug, Parser)]
struct VpcCommandOpts {
    #[clap(subcommand)]
    pub command: VpcCommandEnum,
}

#[derive(Debug, Subcommand)]
enum VpcCommandEnum {
    #[clap(about = "List vpcs - optionally filtered by name")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },
}

#[derive(Debug, Parser)]
struct VpnClientCommand {
    #[clap(subcommand)]
    pub command: VpnClientCommandEnum,
}

#[derive(Debug, Parser)]
enum VpnClientCommandEnum {
    #[clap(about = "Create the client configuration")]
    Create {
        #[clap(help = "The username to create")]
        username: String,
    },

    #[clap(about = "List vpn clients")]
    Ls {
        #[clap(help = "Print extra information", short, long, action = clap::ArgAction::SetTrue)]
        verbose: bool,
    },

    #[clap(about = "Revoke the client configuration")]
    Revoke {
        #[clap(help = "The username to revoke")]
        username: String,
    },
}

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    AwsRun,
    Build,
    CaInit,
    CheckNS,
    CheckSecureMountState,
    Commit,
    DeveloperGetInfo,
    DirectoryMoveDetected,
    DockerBuildImage,
    DockerPushImage,
    GetConfig,
    GetDnsResponse,
    GetStaticApp,
    GetVersionInfo,
    GpgInfo,
    Init,
    InstallCryptoProvider,
    MediaproctorMpProcess,
    MediaproctorMpStream,
    MediaproctorStatus,
    MoveToAce,
    MysqldevimgBuild,
    MysqldevimgLs,
    MysqldevimgPush,
    MysqldevimgRun,
    NotProperlyInitialized,
    PackerRun,
    PkrRun,
    RunNsCheck,
    SecureMount,
    SecureUnmount,
    SecureInfo,
    ServerRun(anyhow::Error),
    SetCurrentDir,
    Ssh,
    SystemUpdate,
    TfRun,
    Tlscert,
    TlsGenCert,
    TlsLs,
    UpgradeFixup,
    VpnConfig,
    VpnCreateClient,
    VpnDeployServer,
    VpnGenCrl,
    VpnInit,
    VpnLs,
    VpnNukeItAll,
    VpnRevokeClient,
}

#[tokio::main]
async fn main() -> error_stack::Result<(), ErrorStack> {
    let args = Cli::parse();

    tracing_subscriber::Registry::default()
        .with(
            tracing_subscriber::EnvFilter::builder()
                .with_default_directive(tracing_subscriber::filter::LevelFilter::INFO.into())
                .from_env_lossy(),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();

    // Set the cwd before the ace_core::APP is initialized
    std::env::set_current_dir(&args.current_directory).change_context(ErrorStack::SetCurrentDir)?;

    // If asking for version, don't load entire app/env.
    if matches!(args.command, Commands::Version) {
        println!("{}", ace_core::git::version_info());

        return Ok(());
    }

    // Install default CryptoProvder
    match rustls::crypto::aws_lc_rs::default_provider().install_default() {
        Ok(_) => {}
        Err(_e) => error_stack::bail!(ErrorStack::InstallCryptoProvider),
    };

    let app = ace_core::get_static_app().change_context(ErrorStack::GetStaticApp)?;

    // If the local file exists, read it with dotenv
    if app.local_file_path.exists() {
        dotenv::from_path(&app.local_file_path).change_context(ErrorStack::GetConfig)?;
    }

    match (args.command, &app.init_needed, &app.data_pathcache_matches) {
        // Status
        (Commands::Status, _, _) => {
            ace::cli::status::run(app).await;
        }

        (Commands::MarkdownHelp, _, _) => {
            clap_markdown::print_help_markdown::<Cli>();
        }

        // Command still has to be here to keep Rust happy
        (Commands::Version, _, _) => {}

        // Env runs in all cases
        (Commands::AppEnv, _, _) => {
            println!("{:#?}", &app);
        }
        // Init runs in all cases
        (Commands::Init, _, _) => {
            ace::cli::init::run(app)
                .await
                .change_context(ErrorStack::Init)?;
        }
        // Commit runs in all cases
        (Commands::Commit(commit_args), _, _) => {
            ace::cli::commit::run(app, &commit_args.message)
                .await
                .change_context(ErrorStack::Commit)?;
        }
        (Commands::Dns(domain), _, _) => {
            let response = ace_core::dns::get_dns_response(&domain.name)
                .await
                .change_context(ErrorStack::GetDnsResponse)?;
            println!("{:#?}", &response);
        }

        // Secure
        (Commands::Secure(subcmd), _, _) => match &subcmd.command {
            SecureSubCommands::Mount { verbose: _ } => {
                ace::cli::secure::mount(app).change_context(ErrorStack::SecureMount)?;
            }
            SecureSubCommands::Unmount { verbose: _ } => {
                ace::cli::secure::unmount(app).change_context(ErrorStack::SecureUnmount)?;
            }
            SecureSubCommands::Info { verbose: _ } => {
                ace::cli::secure::info(app).change_context(ErrorStack::SecureInfo)?;
            }
        },

        // SystemUpdate - runs with/without secure mountpoint
        (Commands::SystemUpdate, _, true) => {
            ace::cli::system_update::run(app)
                .await
                .change_context(ErrorStack::SystemUpdate)?;
        }

        // Move to ace server
        (Commands::MoveToAce, _, _) => {
            ace::cli::move_to_ace::run(app)
                .await
                .change_context(ErrorStack::MoveToAce)?;
        }

        // Upgrade
        (Commands::Upgrade(opts), _, _) => {
            if opts.fixup {
                ace::cli::upgrade::fixup(app)
                    .await
                    .change_context(ErrorStack::UpgradeFixup)?;
            } else {
                ace::cli::upgrade::run(app).await;
            }
        }

        // Catch the case of needing initialization but not having it
        (_, true, _) => {
            if !app
                .is_secure_mounted()
                .change_context(ErrorStack::CheckSecureMountState)?
            {
                println!();
                println!("Error: you cannot continue until the secure filesystem is mounted");
                println!("Run `ace secure mount` to fix this.");
                println!();
                std::process::exit(1);
            }

            println!();
            println!("Error: not properly initialized");
            println!("Run `ace init` to fix this.");
            println!();
            std::process::exit(1);
        }

        (Commands::Ca(subcmd), _, _) => match &subcmd.command {
            CaSubCommands::Init => {
                ace::cli::ca::init(app)
                    .await
                    .change_context(ErrorStack::CaInit)?;
            }
        },

        (Commands::Config, false, _) => {
            // Print the config (will initialize if needed)
            {
                let config = ace_graph::config::get(&ace_graph::Config::EtcConfig, &app.ace_db_app)
                    .await
                    .change_context(ErrorStack::GetConfig)?;
                println!("{:#?}", &config);
            }
        }
        // Build runs in all cases except if init is needed
        (Commands::Build, false, _) => {
            ace::cli::build::run(app)
                .await
                .change_context(ErrorStack::Build)?;
        }

        // Run ssh in the right environment
        (Commands::Ssh(ssh_args), false, _) => {
            ace::cli::ssh::run(app, &ssh_args.server, &ssh_args.args)
                .await
                .change_context(ErrorStack::Ssh)?;
        }

        // Run packer in the right environment
        (Commands::Packer(pkr_args), false, true) => {
            ace::cli::pkr::run(app, &pkr_args.args)
                .await
                .change_context(ErrorStack::PackerRun)?;
        }

        // Run terraform in the right environment
        (Commands::Tf(tf_args), false, true) => {
            ace::cli::tf::run(app, &tf_args.args)
                .await
                .change_context(ErrorStack::TfRun)?;
        }

        // Run aws in the right environment
        (Commands::Aws(aws_args), false, true) => {
            ace::cli::aws::run(&aws_args.args)
                .await
                .change_context(ErrorStack::AwsRun)?;
        }

        // Check
        (Commands::Check(subcmd), false, true) => match &subcmd.command {
            CheckSubCommands::Ns => ace::cli::check::run(&app.ace_db_app)
                .await
                .change_context(ErrorStack::RunNsCheck)?,
            CheckSubCommands::Ls => ace::cli::check::list_checks(&app.ace_db_app).await,
        },

        // MysqlDevImg
        (Commands::Mysqldevimg(subcmd), false, true) => match &subcmd.command {
            None => {
                ace::cli::mysqldevimg::run(&app.ace_db_app)
                    .await
                    .change_context(ErrorStack::MysqldevimgRun)?;
            }
            Some(MysqldevimgCommand::Build { identifier }) => {
                ace::cli::mysqldevimg::build(app, identifier)
                    .await
                    .change_context(ErrorStack::MysqldevimgBuild)?;
            }
            Some(MysqldevimgCommand::Ls { verbose }) => {
                ace::cli::mysqldevimg::list(*verbose, &app.ace_db_app).await;
            }
            Some(MysqldevimgCommand::Push { identifier }) => {
                ace::cli::mysqldevimg::push(identifier, &app.ace_db_app)
                    .await
                    .change_context(ErrorStack::MysqldevimgPush)?;
            }
        },

        // Agent
        (Commands::Agent(subcmd), false, true) => match &subcmd.command {
            AgentCommandEnum::PrintInstallCommand => {
                // ace_server::print_agent_install_command().await;
                println!("No longer implemented...");
            }
        },

        // Tls
        (Commands::Tlscert(subcmd), false, true) => match &subcmd.command {
            TlscertCommandEnum::Ls { verbose } => {
                ace::cli::tlscert::ls(*verbose, &app.ace_db_app)
                    .await
                    .change_context(ErrorStack::TlsLs)?;
            }
            TlscertCommandEnum::Ls2 { gk, verbose } => {
                ace::cli::tlscert::ls2(gk, *verbose, &app.ace_db_app).await;
            }
            TlscertCommandEnum::Info { gk_tlscert } => {
                ace::cli::tlscert::info(gk_tlscert, &app.ace_db_app).await;
            }
            TlscertCommandEnum::Gen { key } => {
                ace::cli::tlscert::gen_cert(app, key)
                    .await
                    .change_context(ErrorStack::TlsGenCert)?;
            }
        },

        // Vpn
        (Commands::Vpn(subcmd), false, true) => match &subcmd.command {
            VpnServerCommandEnum::Config => {
                ace::cli::vpn::config_server(app)
                    .await
                    .change_context(ErrorStack::VpnConfig)?;
            }
            VpnServerCommandEnum::CreateCaPublicKey => {
                ace::cli::vpn::create_ca_public_key(app)
                    .await
                    .change_context(ErrorStack::VpnGenCrl)?;
            }
            VpnServerCommandEnum::DeployServer {
                use_public_ip,
                dump_script,
            } => {
                ace::cli::vpn::deploy_server(app, use_public_ip, dump_script)
                    .await
                    .change_context(ErrorStack::VpnDeployServer)?;
            }
            VpnServerCommandEnum::Init => {
                ace::cli::vpn::init(app)
                    .await
                    .change_context(ErrorStack::VpnInit)?;
            }
        },

        // VpnClient
        (Commands::VpnClient(subcmd), false, true) => match &subcmd.command {
            VpnClientCommandEnum::Create { username } => {
                ace::cli::vpn_client::create_client(app, username)
                    .await
                    .change_context(ErrorStack::VpnCreateClient)?;
            }
            VpnClientCommandEnum::Ls { verbose } => {
                ace::cli::vpn_client::list(*verbose, &app.ace_db_app.vpn_path).await
            }

            VpnClientCommandEnum::Revoke { username } => {
                ace::cli::vpn_client::revoke_client(app, username)
                    .await
                    .change_context(ErrorStack::VpnRevokeClient)?;
            }
        },

        // Mediaproctor
        (Commands::MP(subcmd), false, true) => match &subcmd.command {
            MediaproctorEnum::MpProcess { mp_name, job_path } => {
                ace::cli::mediaproctor::mp_process(mp_name, job_path, &app.ace_db_app)
                    .await
                    .change_context(ErrorStack::MediaproctorMpProcess)?;
            }
            MediaproctorEnum::MpStream { mp_name, job_url } => {
                ace::cli::mediaproctor::mp_stream(mp_name, job_url, &app.ace_db_app)
                    .await
                    .change_context(ErrorStack::MediaproctorMpStream)?;
            }
            MediaproctorEnum::InstanceStatus {
                mp_name,
                instance_ids,
            } => {
                ace::cli::mediaproctor::status(mp_name, instance_ids, &app.ace_db_app)
                    .await
                    .change_context(ErrorStack::MediaproctorStatus)?;
            }
            MediaproctorEnum::Ls { verbose } => {
                ace::cli::mediaproctor::list(*verbose, &app.ace_db_app).await;
            }
        },

        // Keypair
        (Commands::Keypair(subcmd), false, true) => match &subcmd.command {
            KeypairCommand::Ls { verbose } => {
                ace::cli::keypair::list(*verbose, &app.ace_db_app).await;
            }
            KeypairCommand::Add => {
                ace::cli::keypair::add().await;
            }
        },

        // Account
        (Commands::Account(subcmd), false, true) => match &subcmd.command {
            AccountSubCommands::Ls { verbose } => {
                ace::cli::account::list(*verbose, &app.ace_db_app).await;
            }
        },

        // Ami
        (Commands::Ami(subcmd), false, true) => match &subcmd.command {
            AmiSubCommands::Ls { verbose } => {
                ace::cli::ami::list(*verbose, &app.ace_db_app).await;
            }
        },

        // App
        (Commands::App(subcmd), false, true) => match &subcmd.command {
            AppSubCommands::Info { gk_app } => {
                ace::cli::app::info(gk_app, &app.ace_db_app)
                    .await
                    .change_context(ErrorStack::GetVersionInfo)?;
            }
            AppSubCommands::Ls { verbose } => {
                ace::cli::app::list(*verbose, &app.ace_db_app).await;
            }
        },

        // Asset
        (Commands::Asset(subcmd), false, true) => match &subcmd.command {
            AssetSubCommands::Ls { verbose } => {
                ace::cli::asset::list(*verbose, &app.ace_db_app.asset_path).await;
            }
        },

        // BrDst
        (Commands::Brdst(subcmd), false, true) => match &subcmd.command {
            BrdstSubCommands::Ls { verbose } => {
                ace::cli::brdst::list(*verbose, &app.ace_db_app).await;
            }
        },

        // BrSrc
        (Commands::Brsrc(subcmd), false, true) => match &subcmd.command {
            BrsrcSubCommands::Ls { verbose } => {
                ace::cli::brsrc::list(*verbose, &app.ace_db_app).await;
            }
        },

        // Developer
        (Commands::Developer(subcmd), false, true) => match &subcmd.command {
            DeveloperSubCommands::Info { gk_developer } => {
                ace::cli::developer::info(gk_developer, &app.ace_db_app)
                    .await
                    .change_context(ErrorStack::DeveloperGetInfo)?;
            }
            DeveloperSubCommands::Ls { verbose } => {
                ace::cli::developer::ls(*verbose, &app.ace_db_app).await;
            }
        },

        // DevApp
        (Commands::DevApp(subcmd), false, true) => match &subcmd.command {
            DevAppSubCommandEnum::Ls { verbose } => {
                ace::cli::devapp::list(*verbose, &app.ace_db_app).await;
            }
        },

        // Docker
        (Commands::Docker(subcmd), false, true) => match &subcmd.command {
            DockerSubCommands::Build { gk_docker } => {
                ace::cli::docker::build_image(gk_docker, &app.ace_db_app)
                    .await
                    .change_context(ErrorStack::DockerBuildImage)?;
            }
            DockerSubCommands::Info { gk_docker } => {
                ace::cli::docker::info(gk_docker, &app.ace_db_app).await;
            }
            DockerSubCommands::Ls { verbose } => {
                ace::cli::docker::list(*verbose, &app.ace_db_app).await;
            }
            DockerSubCommands::Push { gk_docker } => {
                ace::cli::docker::push_image(gk_docker, &app.ace_db_app)
                    .await
                    .change_context(ErrorStack::DockerPushImage)?;
            }
        },

        // Domain
        (Commands::Domain(subcmd), false, true) => match &subcmd.command {
            DomainSubCommands::Ls { verbose } => {
                ace::cli::domain::list(*verbose, &app.ace_db_app.etc_path).await;
            }
        },

        // Ecr
        (Commands::Ecr(subcmd), false, true) => match &subcmd.command {
            EcrSubCommand::Ls { verbose } => {
                ace::cli::ecr::list(*verbose, &app.ace_db_app).await;
            }
        },

        // EcrPub
        (Commands::EcrPublic(subcmd), false, true) => match &subcmd.command {
            EcrPubSubCommand::Ls { verbose } => {
                ace::cli::ecr_public::list(*verbose, &app.ace_db_app).await;
            }
        },

        // Gpg
        (Commands::Gpg(subcmd), false, true) => match &subcmd.command {
            GpgSubCommand::Info { verbose } => {
                ace::cli::gpg::info(app, *verbose)
                    .await
                    .change_context(ErrorStack::GpgInfo)?;
            }
        },

        // Host
        (Commands::Host(subcmd), false, true) => match &subcmd.command {
            HostSubCommand::Ls { verbose } => {
                ace::cli::host::list(*verbose, &app.ace_db_app).await;
            }
        },

        // Instance
        (Commands::Instance(subcmd), false, true) => match &subcmd.command {
            InstanceSubCommands::Ls { verbose } => {
                ace::cli::instance::list(*verbose, &app.ace_db_app).await;
            }
        },

        // Instance Profile
        (Commands::InsProfile(subcmd), false, true) => match &subcmd.command {
            InsProfileSubCommands::Ls { verbose } => {
                ace::cli::ins_profile::list(*verbose, &app.ace_db_app).await;
            }
        },

        // LocalFile
        (Commands::Lf(subcmd), false, true) => match &subcmd.command {
            LocalFileSubCommands::Ls { verbose } => {
                ace::cli::lf::list(*verbose).await;
            }
        },

        (Commands::Pkr(subcmd), false, true) => match &subcmd.command {
            PackerSubCommands::Ls { verbose } => {
                ace::cli::pkr::list(*verbose, &app.ace_db_app).await;
            }
        },

        // Peercon
        (Commands::Peercon(subcmd), false, true) => match &subcmd.command {
            PeerconSubCommands::Ls { verbose } => {
                ace::cli::peercon::list(*verbose, &app.ace_db_app).await;
            }
        },

        // RouteTableAssoc
        (Commands::RtAssoc(subcmd), false, true) => match &subcmd.command {
            RouteTableAssocSubCommands::Ls { verbose } => {
                ace::cli::rt_assoc::list(*verbose).await;
            }
        },

        // RouteTable
        (Commands::Rt(subcmd), false, true) => match &subcmd.command {
            RouteTableSubCommands::Ls { verbose } => {
                ace::cli::rt::list(*verbose).await;
            }
        },

        // RouteTableRoute
        (Commands::Rtr(subcmd), false, true) => match &subcmd.command {
            RoutTableRouteSubCommands::Ls { verbose } => {
                ace::cli::rtr::list(*verbose).await;
            }
        },

        // Securitygroup
        (Commands::Sg(subcmd), false, true) => match &subcmd.command {
            SgSubCommands::Ls { verbose } => {
                ace::cli::sg::list(*verbose, &app.ace_db_app).await;
            }
        },

        // Securitygrouprule
        (Commands::Sgr(subcmd), false, true) => match &subcmd.command {
            SgrSubCommands::Ls { verbose } => {
                ace::cli::sgr::list(*verbose).await;
            }
        },

        // TlsPrivateKey
        (Commands::TlsPrivkey(subcmd), false, true) => match &subcmd.command {
            TlsPrivateKeySubCommands::Ls { verbose } => {
                ace::cli::tls_privkey::list(*verbose).await;
            }
        },

        // User
        (Commands::User(subcmd), false, true) => match &subcmd.command {
            UserSubCommands::Info { gk_user } => {
                ace::cli::user::info(gk_user, &app.ace_db_app)
                    .await
                    .change_context(ErrorStack::DeveloperGetInfo)?;
            }
            UserSubCommands::Ls { verbose } => {
                ace::cli::user::list(*verbose, &app.ace_db_app).await;
            }
        },

        // VPC
        (Commands::Vpc(subcmd), false, true) => match &subcmd.command {
            VpcCommandEnum::Ls { verbose } => {
                ace::cli::vpc::list(*verbose, &app.ace_db_app).await;
            }
        },

        // Bucket
        (Commands::Bucket(subcmd), false, true) => match &subcmd.command {
            BucketCommandEnum::Ls { verbose } => {
                ace::cli::bucket::list(*verbose, &app.ace_db_app).await;
            }
        },

        // BucketPolicy
        (Commands::BucketPolicy(subcmd), false, true) => match &subcmd.command {
            BucketPolicySubCommands::Ls { verbose } => {
                ace::cli::bucket_policy::list(*verbose).await;
            }
        },

        // BucketPubAccessBlk
        (Commands::BucketPubAccessBlk(subcmd), false, true) => match &subcmd.command {
            BucketPubAccessBlkSubCommands::Ls { verbose } => {
                ace::cli::bucket_pub_access_blk::list(*verbose).await;
            }
        },

        // BucketReplConfig
        (Commands::BucketReplConfig(subcmd), false, true) => match &subcmd.command {
            BucketReplConfigSubCommands::Ls { verbose } => {
                ace::cli::bucket_repl_config::list(*verbose).await;
            }
        },

        // BucketVersion
        (Commands::BucketVersion(subcmd), false, true) => match &subcmd.command {
            BucketVersionSubCommands::Ls { verbose } => {
                ace::cli::bucket_vers::list(*verbose).await;
            }
        },

        // Eip
        (Commands::Eip(subcmd), false, true) => match &subcmd.command {
            EipSubCommand::Ls { verbose } => {
                ace::cli::eip::list(*verbose).await;
            }
        },

        // EipAssoc
        (Commands::EipAssoc(subcmd), false, true) => match &subcmd.command {
            EipAssocSubCommand::Ls { verbose } => {
                ace::cli::eip_assoc::list(*verbose).await;
            }
        },

        // Igw
        (Commands::Igw(subcmd), false, true) => match &subcmd.command {
            IgwSubCommand::Ls { verbose } => {
                ace::cli::igw::list(*verbose).await;
            }
        },

        // IamAccessKey
        (Commands::IamAccessKey(subcmd), false, true) => match &subcmd.command {
            IamAccessKeySubCommand::Ls { verbose } => {
                ace::cli::iam_access_key::list(*verbose).await;
            }
        },

        // IamRolePolicyAttach
        (Commands::IamRolePolicyAttach(subcmd), false, true) => match &subcmd.command {
            IamRolePolicyAttachSubCommand::Ls { verbose } => {
                ace::cli::iam_role_policy_attach::list(*verbose).await;
            }
        },

        // IamRolePolicy
        (Commands::IamRolePolicy(subcmd), false, true) => match &subcmd.command {
            IamRolePolicySubCommand::Ls { verbose } => {
                ace::cli::iam_role_policy::list(*verbose).await;
            }
        },

        // IamRole
        (Commands::IamRole(subcmd), false, true) => match &subcmd.command {
            IamRoleSubCommand::Ls { verbose } => {
                ace::cli::iam_role::list(*verbose).await;
            }
        },

        // IamUserPolicyAttach
        (Commands::IamUserPolicyAttach(subcmd), false, true) => match &subcmd.command {
            IamUserPolicyAttachSubCommand::Ls { verbose } => {
                ace::cli::iam_user_policy_attach::list(*verbose).await;
            }
        },

        // IamUser
        (Commands::IamUser(subcmd), false, true) => match &subcmd.command {
            IamUserSubCommand::Ls { verbose } => {
                ace::cli::iam_user::list(*verbose).await;
            }
        },

        // NatGateway
        (Commands::NatGateway(subcmd), false, true) => match &subcmd.command {
            NatGatewaySubCommands::Ls { verbose } => {
                ace::cli::nat_gateway::list(*verbose).await;
            }
        },

        // DnsRec
        (Commands::DnsRec(subcmd), false, true) => match &subcmd.command {
            DnsRecSubCommand::Ls { verbose } => {
                ace::cli::dns_rec::list(*verbose).await;
            }
        },

        // DnsZone
        (Commands::DnsZone(subcmd), false, true) => match &subcmd.command {
            DnsZoneSubCommand::Ls { verbose } => {
                ace::cli::dns_zone::list(*verbose, &app.ace_db_app).await;
            }
        },

        // Sn
        (Commands::Subnet(subcmd), false, true) => match &subcmd.command {
            SubnetSubCommand::Ls { verbose } => {
                ace::cli::subnet::list(*verbose).await;
            }
        },

        (_, _, false) => {
            let report = error_stack::Report::new(ErrorStack::DirectoryMoveDetected).attach_printable(
                "\nDirectory Move Detected! Please examine and then run this script to fix: \n  bin/post-relocate-to-new-directory"
            );

            return Err(report);
        }
    }

    Ok(())
}
