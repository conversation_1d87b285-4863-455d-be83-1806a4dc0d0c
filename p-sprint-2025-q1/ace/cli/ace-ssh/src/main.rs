use clap::{ArgA<PERSON>, Parser};
use reqwest::blocking::Client;
use std::io::Write;
use std::os::unix::fs::PermissionsExt;
use std::path::PathBuf;
use std::process::{Command, Stdio};
use std::vec;
use url::form_urlencoded;

#[derive(Debug, Parser)]
#[command(name = "ace-ssh")]
#[command(about = "ace-ssh is a wrapper around the ssh command that understands several options relating to obtaining ssh certificates, and passes everything else to the ssh binary to do the work.", long_about = None)]
#[command(
    after_help = "Environment variables:\n  ACE_DOMAIN - the domain of the ace server.\n    This is used for api communication.\n    Either ACE_DOMAIN or --ace-domain must be specified.\n  ACE_IDENTITY - the path to the identity file to use.\n    "
)]
#[clap(trailing_var_arg = true)]
struct Cli {
    #[clap(
        short = 'C',
        long,
        help = "Run in this directory instead of the current directory"
    )]
    current_directory: Option<PathBuf>,

    // add verbose level so that it starts at 0 and each -v adds 1
    #[clap(short = 'v', long, action = ArgAction::Count, help = "Increase verbosity")]
    verbose: u8,

    #[clap(long, help = "specify ace domain for API communication")]
    ace_domain: Option<String>,

    #[clap(
        short = 'i',
        long,
        help = "Path to the private key to use. Defaults (in order) to the following: ~/.ssh/id_ed25519, id_rsa, id_ecdsa, or id_dsa"
    )]
    identity_path: Option<PathBuf>,

    #[clap(
        short = 'p',
        long,
        help = "Specify principals (can be used multiple times)"
    )]
    principal: Vec<String>,

    #[clap(short = 'e', long, help = "Specify lifetime of certificate in seconds")]
    expire: Option<u32>,

    #[clap(short = 'o', long, help = "Specify to output the ssh key to stdout")]
    output_cert: bool,

    #[clap(short = 'k', long, help = "Keep temporary certificate file")]
    keep_cert: bool,

    #[clap(action = ArgAction::Append)]
    ssh_args: Vec<String>, // Capture all remaining arguments
}

/// Verifies that the ACE_DOMAIN is formatted properly.
///
/// # Exits
/// - If the ACE_DOMAIN contains a colon, slash, or starts with http
fn verify_ace_domain_or_die(ace_domain: &str) {
    if ace_domain.contains([':', '/']) | ace_domain.starts_with("http") {
        eprintln!(
            "[err] ACE_DOMAIN is not formatted properly: {ace_domain}. May not contain ':', '/' or have an http prefix"
        );
        std::process::exit(1);
    }
}

fn main() {
    let args = Cli::parse();

    // Specific options are not compatible with output_cert
    if args.output_cert {
        if !args.ssh_args.is_empty() {
            eprintln!("[err] cannot use (-o | --output-cert) and also pass additional arguments for the `ssh` command");
            std::process::exit(1);
        }

        if args.keep_cert {
            eprintln!("[err] cannot use (-o | --output-cert) and also use (-k | --keep-cert)");
            std::process::exit(1);
        }
    }

    let ace_domain = match args.ace_domain {
        Some(domain) => domain,
        None => match std::env::var("ACE_DOMAIN") {
            Ok(domain) => domain,
            Err(_) => {
                eprintln!("[err] Either `--ace-domain` argument or `ACE_DOMAIN` environment variable is required");
                std::process::exit(1);
            }
        },
    };

    verify_ace_domain_or_die(&ace_domain);

    // get the private key path either from cli option, env var, or defaults
    let private_key_path = get_path_or_die(args.identity_path);

    let identity = Identity::load_or_die(private_key_path);

    // read the user@hostname from the ssh args
    let user_at_hostname = {
        let re = regex::Regex::new(r"^[a-zA-Z0-9_-]+@[a-zA-Z0-9_.-]+$").unwrap(); // static regex is okay to unwrap

        let mut user_at_hostname = None;

        for arg in args.ssh_args.iter() {
            if re.is_match(arg) {
                user_at_hostname = Some(arg.to_string());
                break;
            }
        }

        user_at_hostname
    };

    // If there are no principals, see if we can find the user@hostname in rest_args
    // If we can't find it, then we need to error out.
    let principals = match (args.principal.is_empty(), &user_at_hostname) {
        (false, _) => args.principal,
        (true, Some(user_at_hostname)) => {
            if args.verbose > 0 {
                eprintln!("[wrn] no principals specified, using `user@hostname` as principal");
            }
            vec![user_at_hostname.clone()]
        }
        (true, None) => {
            eprintln!("[err] user@hostname not found in ssh args and no principals are specified.");
            std::process::exit(1);
        }
    };

    // Verify expire or set default
    let expire = args.expire;

    // Make sure we have a user@hostname (and if we don't, that -o was specified)
    if user_at_hostname.is_none() && !args.output_cert {
        eprintln!("[err] user@hostname not found in ssh args, and -o not specified");
        std::process::exit(1);
    }

    if args.verbose > 0 {
        eprintln!("[arg] ace domain: {:?}", ace_domain);
        eprintln!("[arg] identity file: {:?}", identity.private_key_path);
        eprintln!("[arg] principals: {:?}", principals);
        eprintln!("[arg] ssh args: {:?}", args.ssh_args);
    }

    // obtain the certificate or die trying
    let cert = perform_request_and_get_cert_or_die(
        args.verbose,
        &ace_domain,
        &identity,
        &principals,
        expire,
    );

    // print cert to stdout and exit if --output-cert is specified
    if args.output_cert {
        println!("{}", cert);
        return;
    }

    // validate that we have a user@hostname in the ssh args
    if user_at_hostname.is_none() {
        eprintln!("[err] not found in ssh args: user@hostname");
        std::process::exit(1);
    }

    // create a temp cert
    let temp_cert = CertificateTempFile::from_cert_str(&cert, args.verbose);

    // create the ssh command with full pass through of all arguments and environment variables
    let mut ssh_cmd = Command::new("ssh");

    // point to the identity
    ssh_cmd.arg("-i").arg(&identity.private_key_path);

    // add the certificate
    ssh_cmd
        .arg("-o")
        .arg(format!("CertificateFile={}", temp_cert.path.display()));

    // if verbose is > 1, then pass the -v flag to ssh
    if args.verbose > 1 {
        ssh_cmd.arg("-v");
    }

    // add the rest of the ssh args
    ssh_cmd.args(args.ssh_args);

    if args.verbose > 0 {
        eprintln!("[ssh] running: {:?}", ssh_cmd);
    }

    // run the ssh command and cleanup temp cert
    let ssh_exit_code = match ssh_cmd.status() {
        Ok(status) => status.code().unwrap_or(-1),
        Err(e) => {
            eprintln!("[err] failed to run ssh: {}", e);
            -1
        }
    };

    if args.verbose > 0 {
        eprintln!("[ssh] ssh exited with status: {:?}", ssh_exit_code);
    }

    // cleanup temp cert unless the user specified to keep it
    if args.keep_cert {
        eprintln!("[api] keeping temp cert file: {}", temp_cert.path.display());
    } else {
        temp_cert.cleanup(args.verbose);
    }

    // return the ssh exit code
    std::process::exit(ssh_exit_code);
}

/// Sends a POST request to the ACE server to obtain a signed certificate.
///
/// # Exits
/// - If the ssh-keygen command to sign the query string message fails.
/// - If stdin cannot be opened.
/// - If the query string cannot be written to stdin for the ssh-keygen command.
/// - If the ssh-keygen command fails or fails to read the output.
/// - If the POST request fails to send
/// - If the response is not successful or the body cannot be read
fn perform_request_and_get_cert_or_die(
    verbose: u8,
    ace_domain: &str,
    identity: &Identity,
    principals: &Vec<String>,
    expire: Option<u32>,
) -> String {
    // Build the query string
    let mut serializer_to_sign = form_urlencoded::Serializer::new(String::new());
    let mut serializer_to_send = form_urlencoded::Serializer::new(String::new());

    serializer_to_sign.append_pair("fingerprint", &identity.fingerprint);
    serializer_to_send.append_pair("fingerprint", &identity.fingerprint);

    // Add principal parameters
    for principal in principals {
        serializer_to_sign.append_pair("principal", principal);
        serializer_to_send.append_pair("principal", principal);
    }

    // Add expire parameter
    if let Some(expire) = expire {
        serializer_to_sign.append_pair("expire", &expire.to_string());
        serializer_to_send.append_pair("expire", &expire.to_string());
    }

    let query_string_to_sign = serializer_to_sign.finish();
    drop(serializer_to_sign); // this library has a panic if you use it after finish so we need to drop it

    if verbose > 0 {
        eprintln!("[api] query string to sign: {:?}", query_string_to_sign);
    }

    // Use ssh-keygen to sign the query string via stdin
    let mut cmd = Command::new("ssh-keygen");
    cmd.arg("-Y").arg("sign");
    cmd.arg("-f").arg(&identity.private_key_path);

    // Namespace
    cmd.arg("-n").arg("ace_ssh_signing");

    // Identity
    cmd.arg("-I").arg("ace-ssh");

    if verbose > 1 {
        cmd.arg("-v");
    }

    // Read querystring/message from stdin
    cmd.arg("-");

    cmd.stdin(Stdio::piped());
    cmd.stdout(Stdio::piped());
    cmd.stderr(Stdio::piped());

    if verbose > 0 {
        eprintln!("[cmd] running ssh-keygen command: {:?}", cmd);
    }

    let child = match cmd.spawn() {
        Ok(child) => child,
        Err(e) => {
            eprintln!("[err] spawning ssh-keygen command: {}", e);
            std::process::exit(1);
        }
    };

    // write the query string to ssh-keygen's stdin
    {
        let mut stdin = match child.stdin {
            Some(stdin) => stdin,
            None => {
                eprintln!("[err] ssh-keygen did not open stdin");
                std::process::exit(1);
            }
        };

        match stdin.write_all(query_string_to_sign.as_bytes()) {
            Ok(_) => {}
            Err(err) => {
                eprintln!("[err] writing to ssh-keygen stdin: {}", err);
                std::process::exit(1);
            }
        };
    }

    // read the signature from ssh-keygen's stdout
    let output = match cmd.output() {
        Ok(output) => output,
        Err(e) => {
            eprintln!("[err] failed to read ssh-keygen output: {}", e);
            std::process::exit(1);
        }
    };

    if !output.status.success() {
        eprintln!(
            "[err] ssh-keygen stderr follows:\n{}",
            String::from_utf8_lossy(&output.stderr)
        );
        eprintln!(
            "[err] ssh-keygen failed with status: {}",
            output.status.code().unwrap_or(-1)
        );
        std::process::exit(1);
    }

    if verbose > 0 {
        eprintln!(
            "[cmd] ssh-keygen output follows:\n{}",
            String::from_utf8_lossy(&output.stdout)
        );
    }

    // The signature is in stdout
    let signature_data = match String::from_utf8(output.stdout) {
        Ok(signature_data) => signature_data,
        Err(e) => {
            eprintln!("[err] invalid utf-8 in ssh-keygen output: {}", e);
            std::process::exit(1);
        }
    };

    // URL-encode the signature
    serializer_to_send.append_pair("signature", &signature_data);

    let query_string_to_send = serializer_to_send.finish();
    drop(serializer_to_send); // this library has a panic if you use it after finish so we need to drop it

    // build the full URL
    let url = format!("https://{}/ace-ssh/sign", ace_domain);

    if verbose > 0 {
        eprintln!("[api] sending POST request to: {}", url);
        eprintln!("[api] post data: {}", query_string_to_send);
    }

    // send the POST request
    let client = Client::new();
    let response = match client
        .post(&url)
        .header("Content-Type", "application/x-www-form-urlencoded")
        .body(query_string_to_send)
        .send()
    {
        Ok(response) => response,
        Err(err) => {
            eprintln!("[err] sending POST request: {}", err);
            std::process::exit(1);
        }
    };

    // handle the response
    if response.status().is_success() {
        match response.text() {
            Ok(text) => text,
            Err(err) => {
                eprintln!("[err] reading response body:\n[err]  {}", err);
                std::process::exit(1);
            }
        }
    } else {
        eprintln!("[err] request failed with:\n[err]  {}", response.status());
        eprintln!(
            "[err] response body:\n[err]  {}",
            response
                .text()
                .unwrap_or_else(|_| "(not available)".to_string())
        );
        std::process::exit(1);
    }
}

/// Represents a temporary certificate file that will be cleaned up if -k or --keep-cert are not specified.
pub struct CertificateTempFile {
    path: PathBuf,
}

impl CertificateTempFile {
    /// Create a new `CertificateTempFile` from a certificate string and writes it to the owner's .ssh directory with owner-only read and write permissions.
    ///
    /// # Exits
    /// - If the home directory cannot be found
    /// - If the .ssh directory does not exist
    /// - If the certificate file cannot be written
    /// - If the permissions cannot be set on the certificate file
    pub fn from_cert_str(cert: &str, verbose: u8) -> Self {
        // get home .ssh dir
        let home_dir = match dirs::home_dir() {
            Some(home_dir) => home_dir,
            None => {
                eprintln!("[err] could not find home directory");
                std::process::exit(1);
            }
        };

        let ssh_dir = home_dir.join(".ssh");

        // validate that the .ssh dir exists
        if !ssh_dir.exists() {
            eprintln!("[err] could not find .ssh directory: {}", ssh_dir.display());
            std::process::exit(1);
        }

        // create a random filename
        let filename = format!("ace-ssh-cert-{}.pem", uuid::Uuid::new_v4());

        let path = ssh_dir.join(filename);

        if verbose > 0 {
            eprintln!("[api] writing cert to file: {}", path.display());
        }

        // write the cert to the file
        match std::fs::write(&path, cert) {
            Ok(_) => {}
            Err(e) => {
                eprintln!("[err] failed to write cert to file: {}", e);
                std::process::exit(1);
            }
        }

        // set the permissions on the file
        match std::fs::set_permissions(&path, std::fs::Permissions::from_mode(0o600)) {
            Ok(_) => {}
            Err(e) => {
                eprintln!("[err] failed to set permissions on cert file: {}", e);
                std::process::exit(1);
            }
        };

        Self { path }
    }

    /// take ownership of the struct and clean up the temp file
    fn cleanup(self, verbose: u8) {
        if verbose > 0 {
            eprintln!("[api] removing temp cert file: {}", self.path.display());
        }

        match std::fs::remove_file(&self.path) {
            Ok(_) => {}
            Err(e) => {
                eprintln!("{}", e);
                eprintln!("[wrn] failed to remove cert file: {}", self.path.display());
            }
        }
    }
}

pub struct Identity {
    pub private_key_path: PathBuf,
    pub public_key_path: PathBuf,
    pub fingerprint: String,
}

impl Identity {
    /// Loads an `Identity` from a keypair or dies.  Includes the public key path and fingerprint.
    ///
    /// # Exits
    /// - If the public key file does not exist
    /// - If `ssh-keygen` fails
    pub fn load_or_die(private_key_path: PathBuf) -> Self {
        // calculate the name of the .pub file
        let public_key_path = private_key_path.with_extension("pub");

        // verify that the PUBLIC key file exists
        if !public_key_path.exists() {
            eprintln!(
                "[err] public key file does not exist: {}",
                public_key_path.display()
            );
            std::process::exit(1);
        }

        // calculate the fingerprint with ssh-keygen -l -E sha256 -f private_key_path
        let mut cmd = Command::new("ssh-keygen");
        cmd.arg("-l");
        cmd.arg("-E").arg("sha256");
        cmd.arg("-f").arg(&private_key_path);
        cmd.stdout(Stdio::piped());
        cmd.stderr(Stdio::piped());

        let output = match cmd.output() {
            Ok(output) => output,
            Err(e) => {
                eprintln!("[err] failed to run ssh-keygen: {}", e);
                std::process::exit(1);
            }
        };

        if !output.status.success() {
            eprintln!(
                "[err] ssh-keygen failed with status: {}",
                output.status.code().unwrap_or(-1)
            );
            std::process::exit(1);
        }

        let fingerprint = String::from_utf8_lossy(&output.stdout).trim().to_string();

        Self {
            private_key_path,
            public_key_path,
            fingerprint,
        }
    }
}

/// Returns a path to a private key or dies.
/// 1. Checks for a command line option (-i or --ace-identity)
/// 2. Checks for an environment variable (ACE_IDENTITY)
/// 3. If neither are found, it will attempt to find a default identity file.
///
/// Default identity files are checked in the following order:
/// 1. ~/.ssh/id_ed25519
/// 2. ~/.ssh/id_rsa
/// 3. ~/.ssh/id_ecdsa
/// 4. ~/.ssh/id_dsa
///
/// # Exits
/// - If a command line option is provided but the file does not exist
/// - If an environment variable is set but the file does not exist
/// - If no command line option or environment variable is set and no existing default identity file is found
#[allow(clippy::needless_return)] // auditing return statments is easier
fn get_path_or_die(identity_path: Option<PathBuf>) -> PathBuf {
    match (identity_path, std::env::var("ACE_IDENTITY")) {
        // if they pass the arg, use it
        (Some(identity_path), _) => {
            if !identity_path.exists() {
                eprintln!(
                    "[err] private key specified by --identity does not exist: {}",
                    identity_path.display()
                );
            }

            return identity_path;
        }

        // if the don't pass the arg, but have an env var, use it
        (None, Ok(identity_path)) => {
            let identity_path = PathBuf::from(identity_path);
            if !identity_path.exists() {
                eprintln!(
                    "[err] private key specified by env variable ACE_IDENTITY does not exist: {}",
                    identity_path.display()
                );
            }

            return identity_path;
        }

        // otherwise, attempt to look it up
        (_, _) => {
            // Move on to looking for defaults
            let ordered_default_identities = vec![
                "id_ed25519".to_string(),
                "id_rsa".to_string(),
                "id_ecdsa".to_string(),
                "id_dsa".to_string(),
            ];

            let home_dir = match dirs::home_dir() {
                Some(home_dir) => home_dir,
                None => {
                    eprintln!("[err] could not find home directory.");
                    std::process::exit(1);
                }
            };

            for identity in ordered_default_identities {
                let identity_path = home_dir.join(".ssh").join(identity);

                if identity_path.exists() {
                    return identity_path;
                }
            }

            eprintln!("[err] default private key could not be found and none of -i, --identity-path, or ACE_IDENTITY were provided");
            std::process::exit(1);
        }
    };
}
