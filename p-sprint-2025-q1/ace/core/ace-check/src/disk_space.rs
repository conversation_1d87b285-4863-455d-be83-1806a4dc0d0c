use crate::Check;
use crate::{Check<PERSON><PERSON><PERSON>rait, Check<PERSON><PERSON><PERSON>, DiskSpaceCheck};
use async_trait::async_trait;

pub struct DiskSpaceInfo {
    pub check_key: DiskSpaceCheck,
    pub status: DiskSpaceCheckStatus,
}

impl DiskSpaceInfo {
    pub fn new(check_key: DiskSpaceCheck, status: DiskSpaceCheckStatus) -> Self {
        DiskSpaceInfo { check_key, status }
    }
}

#[async_trait]
impl CheckTrait for DiskSpaceInfo {
    fn execute(&self) -> crate::CheckOutcome {
        // Placeholder:
        crate::CheckOutcome {
            details: Box::new(DiskSpaceInfo::new(
                DiskSpaceCheck::Instance(ace_graph::Instance::Ace),
                DiskSpaceCheckStatus::Unexecuted,
            )),
        }
    }

    async fn get_subject_string(&self, _ace_db_app: &ace_db::App) -> String {
        match &self.check_key {
            DiskSpaceCheck::Instance(instance) => instance.to_string(),
        }
    }

    fn get_check_key(&self) -> String {
        self.check_key.to_string()
    }

    fn get_check_type(&self) -> String {
        "DiskSpace".to_string()
    }
}

pub enum DiskSpaceCheckStatus {
    Error,
    CloseToLimit,
    OverLimit,
    UnderLimit,
    Unexecuted,
}

#[async_trait]
impl CheckKeyTrait for DiskSpaceCheck {
    fn create(self) -> Box<dyn CheckTrait> {
        Box::new(DiskSpaceInfo::new(self, DiskSpaceCheckStatus::Unexecuted))
    }
}

pub async fn select_checks(ace_db_app: &ace_db::App) -> Vec<Check> {
    let mut rval = vec![];

    let instances = match ace_graph::ins::select(&ace_graph::InstanceFilter::All, ace_db_app).await
    {
        Ok(instances) => instances,
        Err(_e) => return rval,
    };

    for instance in instances {
        rval.push(Check::DiskSpace(DiskSpaceCheck::Instance(
            instance.graphkey,
        )));
    }

    rval
}
