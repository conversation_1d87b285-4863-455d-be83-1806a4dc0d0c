use ace_graph::parser;
use ace_graph::GraphKeyExt;
use ace_proc::GraphKeyDerive;
use async_trait::async_trait;
use std::fmt::Debug;

pub mod cpu_usage;
pub mod disk_space;
pub mod ns;
pub mod server_load;
pub mod tls;

#[async_trait]
pub trait CheckKeyTrait: std::fmt::Display + Send + Debug {
    fn create(self) -> Box<dyn CheckTrait>;
}

#[async_trait]
pub trait CheckTrait {
    fn execute(&self) -> CheckOutcome;
    async fn get_subject_string(&self, app: &ace_db::App) -> String;
    fn get_check_key(&self) -> String;
    fn get_check_type(&self) -> String;
}

pub struct CheckOutcome {
    pub details: Box<dyn CheckTrait>,
}

/// List all possible checks: (returns executable checks that haven't been run yet)
pub async fn list_checks(ace_db_app: &ace_db::App) -> Vec<Check> {
    let mut rval = vec![];

    rval.extend(cpu_usage::select_checks(ace_db_app).await);
    rval.extend(disk_space::select_checks(ace_db_app).await);
    rval.extend(ns::select_checks());
    rval.extend(server_load::select_checks());
    rval.extend(tls::select_checks(ace_db_app).await);

    // ... add more checks here

    rval
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "check"]
/// Master check list
pub enum Check {
    #[graphkey = "cpu-usage"]
    CpuUsage(CpuUsageCheck),

    #[graphkey = "disk-space"]
    DiskSpace(DiskSpaceCheck),

    #[graphkey = "ns"]
    Ns(NsCheck),

    #[graphkey = "server-load"]
    ServerLoad(ServerLoadCheck),

    #[graphkey = "tls"]
    Tls(TlsCheck),
}

#[async_trait]
impl CheckKeyTrait for Check {
    fn create(self) -> Box<dyn CheckTrait> {
        match self {
            Check::CpuUsage(cpu_usage) => cpu_usage.create(),
            Check::DiskSpace(disk_space) => disk_space.create(),
            Check::Ns(ns) => ns.create(),
            Check::ServerLoad(server_load) => server_load.create(),
            Check::Tls(tls) => tls.create(),
        }
    }
}

// ---------------- Check Enums ----------------

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "cpu-usage"]
pub enum CpuUsageCheck {
    #[graphkey = "instance"]
    Instance(ace_graph::Instance),
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "disk-space"]
pub enum DiskSpaceCheck {
    #[graphkey = "instance"]
    Instance(ace_graph::Instance),
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "ns"]
pub enum NsCheck {
    #[graphkey = "ace-subdomain-private"]
    AceSubdomainPrivate,

    #[graphkey = "ace-subdomain-public"]
    AceSubdomainPublic,
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "server-load"]
pub enum ServerLoadCheck {
    #[graphkey = "ace-server"]
    AceServer,
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "tls"]
pub enum TlsCheck {
    #[graphkey = "tlscert"]
    TlsCert(ace_graph::TlsCert),
}

// ---------------- End Check Enums ----------------
