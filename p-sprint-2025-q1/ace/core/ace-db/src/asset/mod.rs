use crate::ErrorStack;
use error_stack::ResultExt;
use glob::glob;
use regex::Regex;
use serde::Deserialize;
use std::path::{Path, PathBuf};

#[derive(Debug, Default, Deserialize, Clone)]
#[serde(deny_unknown_fields)]
pub struct Asset {
    pub bin_name: String,
    pub target: String,
    pub version: String,
    pub hash: String,

    pub path: PathBuf,
}

impl Asset {
    pub fn new() -> Self {
        Asset {
            bin_name: "".to_string(),
            path: PathBuf::new(),
            target: "".to_string(),
            hash: "".to_string(),
            version: "0.0.0".to_string(),
        }
    }
}

/// Selects ALL assets (that have the correct naming convention) from the /assets directory
pub async fn select(asset_path: &Path) -> crate::SelectResult<Asset, ErrorStack> {
    let mut rval = Vec::new();

    // Capture every file in the /assets directory
    let glob_pattern = format!("{}/*", asset_path.to_string_lossy());

    let paths = match glob(&glob_pattern) {
        Ok(paths) => paths,
        Err(e) => {
            error_stack::bail!(ErrorStack::GlobPatternError(e));
        }
    };

    for entry in paths {
        let file = match entry {
            Ok(some_file) => some_file,
            Err(e) => {
                rval.push(Err(ErrorStack::GlobError(e).into()));
                continue;
            }
        };

        match extract_info(file.as_path()).await {
            Ok(Some(asset)) => {
                rval.push(Ok(asset));
            }
            Ok(None) => {
                continue;
            }
            Err(e) => {
                rval.push(Err(e));
            }
        }
    }

    Ok(rval)
}

async fn extract_info(path: &Path) -> error_stack::Result<Option<Asset>, ErrorStack> {
    let name = r"^([A-Za-z-]+)";
    let version = r"(\d+\.\d+\.\d+)";
    let target = r"([a-zA-Z][a-zA-Z0-9_-]+(?:-[a-zA-Z0-9_-]+)*)";
    let hash = r"([a-f0-9]+)$";

    let re = Regex::new(format!("{name}-{version}-{target}-{hash}").as_str())
        .change_context(ErrorStack::RegexCreationError)?;

    let filename = match path.file_name() {
        Some(filename) => filename.to_string_lossy(),
        None => error_stack::bail!(ErrorStack::ExtractNameError(path.to_path_buf())),
    };

    // If the filename doesn't start with one of the "canon" binaries, skip it.
    if !filename.starts_with("ace-agent")
        && !filename.starts_with("ace-agent-updater")
        && !filename.starts_with("mp-process")
        && !filename.starts_with("mp-stream")
    {
        return Ok(None);
    }

    // Capture information from the filename:
    let captures = match re.captures(&filename) {
        Some(captures) => captures,
        None => {
            error_stack::bail!(ErrorStack::InvalidFilename(path.to_path_buf()));
        }
    };

    let name = match captures.get(1) {
        Some(name) => name.as_str(),
        None => error_stack::bail!(ErrorStack::NoAssetNameCapture(path.to_path_buf())),
    };

    let version = match captures.get(2) {
        Some(version) => version.as_str(),
        None => error_stack::bail!(ErrorStack::NoAssetVersionCapture(path.to_path_buf())),
    };

    let target = match captures.get(3) {
        Some(architecture) => architecture.as_str(),
        None => error_stack::bail!(ErrorStack::NoAssetOsCapture(path.to_path_buf())),
    };

    let hash = match captures.get(4) {
        Some(hash) => hash.as_str(),
        None => error_stack::bail!(ErrorStack::NoAssetHashCapture(path.to_path_buf())),
    };

    // Extract the asset info from the filename
    let asset = Asset {
        bin_name: name.to_string(),
        version: version.to_string(),
        target: target.to_string(),
        path: path.to_path_buf(),
        hash: hash.to_string(),
    };

    Ok(Some(asset))
}
