use crate::<PERSON>rrorStack;
use error_stack::ResultExt;
use garbage::{CNSL, JE};
use ipnetwork::IpNetwork;
use serde::Deserialize;
use serde_with::serde_as;
use std::{net::Ipv4Addr, path::Path, time::Duration};

#[serde_as]
#[derive(Debug, Clone, Deserialize)]
#[serde(deny_unknown_fields)]
pub struct Config {
    pub account_key: String,
    pub region: String,

    pub my_ip: Option<std::net::Ipv4Addr>,
    pub zerossl_key: Option<String>,

    pub create_nat_gateways: Option<bool>,
    pub ace_instance_volume_size: Option<u32>,
    pub ace_instance_allow_ssh_from: Option<Vec<IpNetwork>>,

    pub graylog: Option<Graylog>,

    #[serde(default = "default_version")]
    pub agent_version: String,
    pub vpn: Option<Vpn>,
}

#[derive(Debug, Deserialize, Clone)]
#[serde(deny_unknown_fields)]
pub struct Graylog {
    #[serde(default = "default_instance_type")]
    pub instance_type: String,
    #[serde(default = "default_volume_size")]
    pub volume_size: u32,
}

#[derive(Debug, Deserialize, Clone)]
#[serde(deny_unknown_fields)]
pub struct Vpn {
    #[serde(default = "default_instance_type")]
    pub instance_type: String,
    #[serde(default = "default_volume_size")]
    pub volume_size: u32,
    #[serde(default)]
    pub additional_routed_subnets: Vec<IpNetwork>,
}

fn default_instance_type() -> String {
    "t3.small".to_string()
}

fn default_volume_size() -> u32 {
    20
}

fn default_version() -> String {
    "1.0.0".to_string()
}

#[rustfmt::skip]
/// # Errors
/// 
/// Returns an error if the call to `current_account_key_and_region()` fails.
pub fn example_string() -> error_stack::Result<String, ErrorStack> {
    let env_info = crate::current_account_key_and_region().change_context(ErrorStack::GetAccountKeyAndRegion)?;
    let my_ip = get_my_ip()?;

    Ok(CNSL!(r"
        account_key = ", JE!(env_info.account_key), r"
        region = ", JE!(env_info.region), r"
        my_ip = ", JE!(my_ip), r"

        create_nat_gateways = false
    "))
}

/// # Errors
///
/// The function only returns an error if the `parse_file` function fails.  (Has no inherent errors)
pub async fn get(config_file_path: &Path) -> error_stack::Result<Config, ErrorStack> {
    let config = parse_file(config_file_path).await?;

    Ok(config)
}

async fn parse_file(config_file_path: &Path) -> error_stack::Result<Config, ErrorStack> {
    let content = tokio::fs::read_to_string(config_file_path)
        .await
        .change_context(ErrorStack::ReadFile(config_file_path.to_path_buf()))?;

    let config: Config = toml::from_str(&content)
        .change_context_lazy(|| ErrorStack::ParseFile(config_file_path.to_path_buf()))?;

    // Make sure the env.path_accountkey_region matches these values
    // (Will bail if error)
    verify_account_key_and_region(&config.account_key, &config.region)?;

    Ok(config)
}

fn verify_account_key_and_region(
    account_key: &str,
    region: &str,
) -> error_stack::Result<(), ErrorStack> {
    // This function pieced together from code in ace_core::lib.rs

    let git2_repo = git2::Repository::discover(".").change_context(ErrorStack::DiscoverGitRepo)?;

    let current_path = match git2_repo.workdir() {
        Some(path) => path.to_path_buf(),
        None => {
            error_stack::bail!(ErrorStack::UndiscoverableDirectory(
                "Error loading path info from git repository.".to_string()
            ));
        }
    };

    // Parse the path to check for account key and region
    let basename = current_path.file_name().unwrap().to_string_lossy();

    // Bail out if dir does not have @
    if !basename.contains('@') {
        error_stack::bail!(ErrorStack::DirectoryFormatting(format!(
            "Directory name does not contain @ symbol: {basename}\nFormatting should be: accountkey@region"
        )));
    }

    // split on first -
    let mut parts = basename.splitn(2, '@');
    let current_dir_account_key = parts.next().unwrap_or("").to_string();
    let current_dir_region = parts.next().unwrap_or("").to_string();

    if current_dir_account_key != account_key {
        error_stack::bail!(ErrorStack::DirectoryMismatchAccountKey(format!(
            "Expected: {account_key}   Found: {current_dir_account_key}",
        )));
    }

    if current_dir_region != region {
        error_stack::bail!(ErrorStack::DirectoryMismatchRegion(format!(
            "Expected: {region}   Found: {current_dir_region}",
        )));
    }

    Ok(())
}

fn sync_parse_file(
    config_path: &std::path::PathBuf,
    init_needed: bool,
) -> error_stack::Result<Config, ErrorStack> {
    if !config_path.exists() {
        if init_needed {
            // Return a "default" config.
            let path_acct_key_region = crate::current_account_key_and_region()
                .change_context(ErrorStack::GetAccountKeyAndRegion)?;

            // We're still initializing, config file doesn't exist yet.
            let config = Config {
                account_key: path_acct_key_region.account_key,
                region: path_acct_key_region.region,
                my_ip: None,
                zerossl_key: None,
                create_nat_gateways: None,
                ace_instance_volume_size: None,
                ace_instance_allow_ssh_from: None,
                graylog: None,
                agent_version: default_version(),
                vpn: None,
            };

            return Ok(config);
        } else {
            //throw an error...this file should still exist.
            error_stack::bail!(ErrorStack::InitNotNeededAndConfigFileDoesNotExist);
        }
    }

    let content = std::fs::read_to_string(config_path)
        .change_context(ErrorStack::ReadFile(config_path.clone()))?;

    let config: Config = toml::from_str(&content)
        .change_context_lazy(|| ErrorStack::ParseFile(config_path.clone()))?;

    // Make sure the env.path_accountkey_region matches these values
    // (Will bail if error)
    verify_account_key_and_region(&config.account_key, &config.region)
        .change_context_lazy(|| ErrorStack::VerifyAccountKeyAndRegion(config_path.clone()))?;

    Ok(config)
}

/// # Errors
///
/// This function will bail under the following circumstances:
/// -
pub fn sync_get_config_and_ip(
    config_path: &std::path::PathBuf,
    init_needed: bool,
) -> error_stack::Result<(Config, Ipv4Addr), ErrorStack> {
    let config =
        sync_parse_file(config_path, init_needed).change_context(ErrorStack::ParseEtcConfigFile)?;

    if let Some(my_ip) = config.my_ip {
        Ok((config, my_ip))
    } else {
        let my_ip = get_my_ip()?;
        Ok((config, my_ip))
    }
}

pub fn get_my_ip() -> error_stack::Result<Ipv4Addr, ErrorStack> {
    // attempt to fetch IP from aws metadata
    let response = ureq::get("http://***************/latest/meta-data/local-ipv4")
        .timeout(Duration::from_millis(100)) // set the timeout here (milliseconds for both connection and read)
        .call();

    let my_ip = match response {
        Ok(response) => response
            .into_string()
            .unwrap_or_default()
            .parse::<std::net::Ipv4Addr>()
            .change_context(ErrorStack::ParseAwsMetadataResponse)?,
        Err(_e) => {
            // attempt to fetch IP from https://ipinfo.io/ip

            let response = ureq::get("https://ipinfo.io/ip")
                .timeout(Duration::from_millis(5000)) // set the timeout here (milliseconds for both connection and read)
                .call()
                .change_context(ErrorStack::FetchIpFromIpInfo)?;

            response
                .into_string()
                .unwrap_or_default()
                .parse::<std::net::Ipv4Addr>()
                .change_context(ErrorStack::ParseIpInfoResponse)?
        }
    };

    Ok(my_ip)
}
