use error_stack::ResultExt;
use glob::glob;
use ipnetwork::IpNetwork;
use regex::Regex;
use serde::Deserialize;
use std::collections::HashMap;
use std::path::{Path, PathBuf};

use crate::ErrorStack;

#[derive(Debug, Deserialize)]
#[serde(deny_unknown_fields)]
pub(crate) struct EtcUser {
    pub name: String,

    #[serde(default)]
    pub ssh_keys: Vec<String>,

    #[serde(default)]
    pub static_networks: Vec<IpNetwork>,

    #[serde(default)]
    pub allowed_principals: Vec<String>,

    #[serde(skip)]
    pub source: PathBuf,
}

/// Selects users from the etc directory.
///
/// # Errors
///
/// Bails under the following circumstances:
/// - Cannot create glob pattern.
pub(crate) async fn select(
    filter_name: crate::Filter<String>,
    etc_path: &Path,
) -> error_stack::Result<HashMap<String, error_stack::Result<EtcUser, ErrorStack>>, ErrorStack> {
    if matches!(filter_name, crate::Filter::None) {
        return Ok(HashMap::new());
    }

    let pattern = format!("{}/user.*.toml", etc_path.to_string_lossy());

    let mut rval = HashMap::new();

    let paths = match glob(&pattern) {
        Ok(paths) => paths,
        Err(e) => {
            error_stack::bail!(ErrorStack::GlobPatternError(e));
        }
    };

    // Exceptional circumstances (issues creating glob patterns, extracting filenames, etc.) are relegated to tracing.
    for entry in paths {
        let file = match entry {
            Ok(some_file) => some_file,
            Err(e) => {
                tracing::warn!("Error accessing file entry: {:?}", e);
                continue;
            }
        };

        let user_name = match extract_name(&file) {
            Ok(name) => name,
            Err(e) => {
                tracing::warn!("Invalid filename or entry name: {:?}", e);
                continue;
            }
        };

        // Any errors after obtaining a valid name ARE PROPAGATED.
        // Handle filtering
        if !match &filter_name {
            crate::Filter::All => true,
            crate::Filter::One(filter_name) => user_name == *filter_name,
            crate::Filter::Many(filter_names) => filter_names.contains(&user_name),
            crate::Filter::None => false,
        } {
            continue;
        }

        // rval.push(parse_file(&name, &file).await);
        let user_result = parse_file(&user_name, &file).await;
        rval.insert(user_name, user_result);
    }

    Ok(rval)
}

fn extract_name(path: &Path) -> error_stack::Result<String, ErrorStack> {
    let re = Regex::new(r"^user\.([a-z][a-z0-9]*(?:-[a-z0-9]+)*)\.toml")
        .change_context(ErrorStack::RegexCreationError)?;

    // peel off the file name from the path
    let filename = match path.file_name() {
        Some(filename) => filename.to_string_lossy(),
        None => error_stack::bail!(ErrorStack::ExtractNameError(path.to_path_buf())),
    };

    match re.captures(&filename) {
        Some(capture) => Ok(capture[1].to_string()),
        None => error_stack::bail!(ErrorStack::FilenameInvalidNameComponent(path.to_path_buf())),
    }
}

async fn parse_file(name: &str, path: &Path) -> error_stack::Result<EtcUser, ErrorStack> {
    let content = tokio::fs::read_to_string(path)
        .await
        .change_context_lazy(|| ErrorStack::ReadFile(path.to_owned()))?;

    let mut user: EtcUser =
        toml::from_str(&content).change_context_lazy(|| ErrorStack::ParseFile(path.to_owned()))?;

    if name != user.name {
        error_stack::bail!(ErrorStack::NameAndFilenameMismatch(
            user.name,
            path.to_path_buf()
        ));
    }

    user.source = path.to_path_buf();

    Ok(user)
}
