use std::path::Path;

use crate::ErrorStack;
use error_stack::ResultExt;
use glob::glob;
use indexmap::IndexMap;
use regex::Regex;
use serde::Deserialize;

#[derive(Debug, Deserialize)]
#[serde(deny_unknown_fields)]
pub struct App {
    pub name: String,

    #[serde(default)]
    pub conf: toml::value::Table,

    #[serde(default = "default_hosting")]
    pub hosting: AppHosting,

    #[serde(default)]
    pub conf_live: toml::value::Table,

    #[serde(default)]
    pub conf_test: toml::value::Table,

    #[serde(default)]
    pub bucket: IndexMap<String, AppBucket>,
}

#[derive(Debug, Deserialize)]
#[serde(deny_unknown_fields)]
#[serde(rename_all = "lowercase")]
pub enum AppHosting {
    None,
    Instance {
        ami: String,
        subnet: String,

        #[serde(default = "default_instance_type")]
        instance_type: String,

        ingress: Vec<super::instance::InstanceIngress>,

        #[serde(default = "default_use_elastic_ip")]
        use_elastic_ip: bool,

        #[serde(default = "default_volume_size")]
        volume_size: u32,
    },
}

#[derive(Debug, Deserialize)]
#[serde(deny_unknown_fields)]
pub struct AppBucket {
    #[serde(default)]
    pub public_read: Vec<String>,

    #[serde(default = "developer_access_default")]
    pub developer_access: bool,
}

fn default_hosting() -> AppHosting {
    AppHosting::None
}

fn developer_access_default() -> bool {
    false
}

fn default_use_elastic_ip() -> bool {
    false
}

fn default_instance_type() -> String {
    "t3.micro".to_string()
}

fn default_volume_size() -> u32 {
    20
}

/// Selects apps from the etc directory.
///
/// # Errors
///
/// Bails under the following circumstances:
/// - Cannot create glob pattern.
pub async fn select(
    filter_name: crate::Filter<&str>,
    etc_path: &Path,
) -> crate::SelectResult<App, ErrorStack> {
    if matches!(filter_name, crate::Filter::None) {
        return Ok(Vec::new());
    }

    let pattern = format!("{}/app.*.toml", etc_path.to_string_lossy());

    let mut rval = Vec::new();

    let paths = match glob(&pattern) {
        Ok(paths) => paths,
        Err(e) => {
            error_stack::bail!(ErrorStack::GlobPatternError(e));
        }
    };

    for entry in paths {
        let file = match entry {
            Ok(some_file) => some_file,
            Err(e) => {
                rval.push(Err(ErrorStack::GlobError(e).into()));
                continue;
            }
        };

        let app_name = extract_name(&file);

        match app_name {
            Ok(name) => {
                // Handle filtering
                if !match &filter_name {
                    crate::Filter::All => true,
                    crate::Filter::One(filter_name) => name == *filter_name,
                    crate::Filter::Many(filter_names) => filter_names.contains(&name.as_str()),
                    crate::Filter::None => false,
                } {
                    continue;
                }

                rval.push(parse_file(&name, &file).await);
            }
            Err(e) => {
                rval.push(Err(e));
            }
        }
    }

    Ok(rval)
}

fn extract_name(path: &Path) -> error_stack::Result<String, ErrorStack> {
    // takes a path and returns a Result<String>
    // path is expected to be a file name like etc/app.name.toml
    // returns the name part of the file name

    let re = Regex::new(r"^app\.([a-z][a-z0-9]*(?:-[a-z0-9]+)*)\.toml")
        .change_context(ErrorStack::RegexCreationError)?;

    // peel off the file name from the path
    let filename = match path.file_name() {
        Some(filename) => filename.to_string_lossy(),
        None => error_stack::bail!(ErrorStack::ExtractNameError(path.to_path_buf())),
    };

    match re.captures(&filename) {
        Some(capture) => Ok(capture[1].to_string()),
        None => error_stack::bail!(ErrorStack::FilenameInvalidNameComponent(path.to_path_buf())),
    }
}

async fn parse_file(name: &str, path: &Path) -> error_stack::Result<App, ErrorStack> {
    let content = tokio::fs::read_to_string(path)
        .await
        .change_context_lazy(|| ErrorStack::ReadFile(path.to_owned()))?;

    let app: App =
        toml::from_str(&content).change_context_lazy(|| ErrorStack::ParseFile(path.to_owned()))?;

    if name != app.name {
        error_stack::bail!(ErrorStack::NameAndFilenameMismatch(
            app.name,
            path.to_path_buf()
        ));
    }

    // iterate over buckets, iterate over public_read, validate they start with /
    for app_bucket in app.bucket.values() {
        for public_read in &app_bucket.public_read {
            if !public_read.starts_with('/') {
                error_stack::bail!(ErrorStack::AppBucketPublicKeyDoesNotStartWithSlash(
                    public_read.clone()
                ));
            }
        }
    }

    Ok(app)
}
