use crate::ErrorStack;
use error_stack::ResultExt;
use glob::glob;
use regex::Regex;
use serde::Deserialize;
use std::path::Path;

#[derive(Debug, Deserialize)]
#[serde(deny_unknown_fields)]
pub struct MediaProctor {
    pub name: String,
    pub secret: String,
    pub ami_graphkey: Option<String>,
    #[serde(rename = "mp-process")]
    pub mp_process: MediaProctorProcess,
    #[serde(rename = "mp-stream")]
    pub mp_stream: MediaProctorStream,
    #[serde(skip)]
    pub path: std::path::PathBuf,
}

#[derive(Debug, Deserialize)]
#[serde(deny_unknown_fields)]
pub struct MediaProctorProcess {
    pub instance_type: String,

    #[serde(default = "default_timeout")]
    pub timeout: u32,
}

#[derive(Debug, Deserialize)]
#[serde(deny_unknown_fields)]
pub struct MediaProctorStream {
    pub instance_type: String,

    #[serde(default = "default_timeout")]
    pub timeout: u32,
}

fn default_timeout() -> u32 {
    300
}

/// Selects mediaproctors from the etc directory.
///
/// # Errors
///
/// Bails under the following circumstances:
/// - Cannot create glob pattern.
pub async fn select(
    filter_name: crate::Filter<String>,
    etc_path: &Path,
) -> crate::SelectResult<MediaProctor, ErrorStack> {
    // Use glob to get all the file names, then iterate and call parse on each one
    // etc/mediaproctor.*.toml

    if matches!(filter_name, crate::Filter::None) {
        return Ok(Vec::new());
    }

    let pattern = format!("{}/mediaproctor.*.toml", etc_path.to_string_lossy());

    let mut rval = Vec::new();

    let paths = match glob(&pattern) {
        Ok(paths) => paths,
        Err(e) => {
            error_stack::bail!(ErrorStack::GlobPatternError(e));
        }
    };

    for entry in paths {
        let file = match entry {
            Ok(some_file) => some_file,
            Err(e) => {
                rval.push(Err(ErrorStack::GlobError(e).into()));
                continue;
            }
        };

        let mediaproctor_name = extract_name(&file);
        match mediaproctor_name {
            Ok(name) => {
                // Handle filtering
                if !match &filter_name {
                    crate::Filter::All => true,
                    crate::Filter::One(filter_name) => name == *filter_name,
                    crate::Filter::Many(filter_names) => filter_names.contains(&name),
                    crate::Filter::None => false,
                } {
                    continue;
                }

                rval.push(parse_file(&name, &file).await);
            }
            Err(e) => {
                rval.push(Err(e));
            }
        }
    }

    Ok(rval)
}

fn extract_name(path: &Path) -> error_stack::Result<String, ErrorStack> {
    // takes a path and returns a Result<String>
    // path is expected to be a file name like etc/.name.toml
    // return the name part of the file name

    let re = Regex::new(r"^mediaproctor\.([a-z][a-z0-9]*(?:-[a-z0-9]+)*)\.toml")
        .change_context(ErrorStack::RegexCreationError)?;

    // peel off the file name from the path
    let filename = match path.file_name() {
        Some(filename) => filename.to_string_lossy(),
        None => error_stack::bail!(ErrorStack::ExtractNameError(path.to_path_buf())),
    };

    match re.captures(&filename) {
        Some(capture) => Ok(capture[1].to_string()),
        None => error_stack::bail!(ErrorStack::FilenameInvalidNameComponent(path.to_path_buf())),
    }
}

async fn parse_file(name: &str, path: &Path) -> error_stack::Result<MediaProctor, ErrorStack> {
    let content = tokio::fs::read_to_string(path)
        .await
        .change_context_lazy(|| ErrorStack::ReadFile(path.to_owned()))?;

    let mut mediaproctor: MediaProctor =
        toml::from_str(&content).change_context_lazy(|| ErrorStack::ParseFile(path.to_owned()))?;

    if name != mediaproctor.name {
        error_stack::bail!(ErrorStack::NameAndFilenameMismatch(
            mediaproctor.name,
            path.to_path_buf()
        ));
    }

    path.clone_into(&mut mediaproctor.path);

    Ok(mediaproctor)
}
