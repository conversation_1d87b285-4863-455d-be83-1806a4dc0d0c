use crate::ErrorStack;
use error_stack::ResultExt;
use glob::glob;
use regex::Regex;
use serde::Deserialize;
use std::collections::HashMap;
use std::path::Path;

#[derive(Debug, Deserialize)]
#[serde(deny_unknown_fields)]
pub struct Developer {
    // In ace-db
    pub name: String,
    pub subnet: String,
    pub secret: String,

    #[serde(default = "default_instance_type")]
    pub instance_type: String,

    #[serde(default = "default_volume_size")]
    pub volume_size: u32,

    #[serde(default)]
    pub read_mysqldevimg: Vec<String>,

    #[serde(default)]
    pub app: HashMap<String, DeveloperAppConfig>,
    pub tls: Option<DeveloperTls>,
    pub public_ip: Option<String>,
    pub private_ip: Option<String>,
    pub resource_id: Option<String>,
    pub ssh_public_key: Option<String>,

    // username:password
    pub ace_legacy_auth: Option<String>,

    #[serde(default = "default_active")]
    pub active: bool,
}

fn default_active() -> bool {
    true
}

fn default_instance_type() -> String {
    "t3.small".to_string()
}

fn default_volume_size() -> u32 {
    80
}

#[derive(Debug, Deserialize, Clone)]
#[serde(deny_unknown_fields)]
pub struct DeveloperAppConfig {
    #[serde(default)]
    pub conf: toml::value::Table,
}

#[derive(Debug, Deserialize)]
#[serde(deny_unknown_fields)]
pub struct DeveloperTls {
    pub cert: String,
    pub key: String,
}

/// Selects developers from the etc directory.
///
/// # Errors
///
/// Bails under the following circumstances:
/// - Cannot create glob pattern.
pub async fn select(
    filter_name: crate::Filter<String>,
    etc_path: &Path,
) -> crate::SelectResult<Developer, ErrorStack> {
    // Uses glob to get all the file names, then iterate and call parse on each one
    // etc/developer.*.toml

    if matches!(filter_name, crate::Filter::None) {
        return Ok(Vec::new());
    }

    let pattern = format!("{}/developer.*.toml", etc_path.to_string_lossy());

    let mut rval = Vec::new();

    // Create glob, check if created successfully
    let paths = match glob(&pattern) {
        Ok(paths) => paths,
        Err(e) => {
            error_stack::bail!(ErrorStack::GlobPatternError(e));
        }
    };

    // Iterate over paths
    for entry in paths {
        let file = match entry {
            Ok(some_file) => some_file,
            Err(e) => {
                rval.push(Err(ErrorStack::GlobError(e).into()));
                continue;
            }
        };

        let developer_name = extract_name(&file);
        match developer_name {
            Ok(name) => {
                // Handle filtering
                if !match &filter_name {
                    crate::Filter::All => true,
                    crate::Filter::One(filter_name) => name == *filter_name,
                    crate::Filter::Many(filter_names) => filter_names.contains(&name),
                    crate::Filter::None => false,
                } {
                    continue;
                }

                let developer = parse_file(&name, &file).await;
                match developer {
                    Ok(dev) => rval.push(Ok(dev)),
                    Err(e) => rval.push(Err(e)),
                }
            }
            Err(e) => {
                rval.push(Err(e));
            }
        }
    }

    Ok(rval)
}

fn extract_name(path: &Path) -> error_stack::Result<String, ErrorStack> {
    // takes a path and returns a Result<String>
    // path is expected to be a file name like etc/developer.name.toml
    // return the name part of the file name

    let re = Regex::new(r"^developer\.([a-z][a-z0-9]*(?:-[a-z0-9]+)*)\.toml")
        .change_context(ErrorStack::RegexCreationError)?;

    // peel off the file name from the path
    let filename = match path.file_name() {
        Some(filename) => filename.to_string_lossy(),
        None => error_stack::bail!(ErrorStack::ExtractNameError(path.to_path_buf())),
    };

    match re.captures(&filename) {
        Some(capture) => Ok(capture[1].to_string()),
        None => error_stack::bail!(ErrorStack::FilenameInvalidNameComponent(path.to_path_buf())),
    }
}

async fn parse_file(name: &str, path: &Path) -> error_stack::Result<Developer, ErrorStack> {
    let content = tokio::fs::read_to_string(path)
        .await
        .change_context_lazy(|| ErrorStack::ReadFile(path.to_owned()))?;

    let developer: Developer =
        toml::from_str(&content).change_context_lazy(|| ErrorStack::ParseFile(path.to_owned()))?;

    if name != developer.name {
        error_stack::bail!(ErrorStack::NameAndFilenameMismatch(
            developer.name,
            path.to_path_buf()
        ));
    }

    if developer.secret.len() < 16 {
        error_stack::bail!(ErrorStack::DeveloperInvalidSecretLength(
            "Secret must be at least 16 characters in length".to_string(),
            developer.secret.len()
        ));
    }

    Ok(developer)
}
