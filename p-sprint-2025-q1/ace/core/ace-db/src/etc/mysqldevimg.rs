use crate::ErrorStack;
use error_stack::ResultExt;
use glob::glob;
use regex::Regex;
use serde::Deserialize;
use std::path::{Path, PathBuf};

#[derive(Debug, Deserialize)]
#[serde(deny_unknown_fields)]
struct SerdeStruct {
    name: String,
}

#[derive(Debug)]
pub struct MysqlDevImg {
    pub source_path: PathBuf,
    pub name: String,
    pub sql_path: PathBuf,
}

/// Selects mysqldevimg from the etc directory.
///
/// # Errors
///
/// Bails under the following circumstances:
/// - Cannot create glob pattern.
pub async fn select(
    filter_name: crate::Filter<String>,
    etc_path: &Path,
) -> crate::SelectResult<MysqlDevImg, ErrorStack> {
    // Uses glob to get all the file names, then iterates and calls parse on each one
    // etc/keypair.*.pub

    if matches!(filter_name, crate::Filter::None) {
        return Ok(Vec::new());
    }

    let pattern = format!("{}/mysqldevimg.*.toml", etc_path.to_string_lossy());

    let mut rval = Vec::new();

    let paths = match glob(&pattern) {
        Ok(paths) => paths,
        Err(e) => {
            error_stack::bail!(ErrorStack::GlobPatternError(e));
        }
    };

    for entry in paths {
        let file = match entry {
            Ok(some_file) => some_file,
            Err(e) => {
                rval.push(Err(ErrorStack::GlobError(e).into()));
                continue;
            }
        };

        let mysqldevimg_name = extract_name(&file);
        match mysqldevimg_name {
            Ok(name) => {
                // Handle filtering
                if !match &filter_name {
                    crate::Filter::All => true,
                    crate::Filter::One(filter_name) => name == *filter_name,
                    crate::Filter::Many(filter_names) => filter_names.contains(&name),
                    crate::Filter::None => false, // Will never reach this arm due to previous check.
                } {
                    continue;
                }

                rval.push(parse_toml_file(&name, &file, etc_path).await);
            }
            Err(e) => {
                rval.push(Err(e));
            }
        }
    }

    Ok(rval)
}

fn extract_name(path: &Path) -> error_stack::Result<String, ErrorStack> {
    // takes a path and returns a Result<String>
    // path is expected to be a file name like etc/mysqldevimg.name.toml
    // returns the name part of the file name

    let re = Regex::new(r"^mysqldevimg\.([a-z][a-z0-9]*(?:-[a-z0-9]+)*)\.toml")
        .change_context(ErrorStack::RegexCreationError)?;

    // peel off the file name from the path
    let filename = match path.file_name() {
        Some(filename) => filename.to_string_lossy(),
        None => error_stack::bail!(ErrorStack::ExtractNameError(path.to_path_buf())),
    };

    match re.captures(&filename) {
        Some(capture) => Ok(capture[1].to_string()),
        None => error_stack::bail!(ErrorStack::FilenameInvalidNameComponent(path.to_path_buf())),
    }
}

async fn parse_toml_file(
    name: &str,
    path: &Path,
    etc_path: &Path,
) -> error_stack::Result<MysqlDevImg, ErrorStack> {
    let toml_content = tokio::fs::read_to_string(path)
        .await
        .change_context_lazy(|| ErrorStack::ReadFile(path.to_owned()))?;

    let serdestruct: SerdeStruct = toml::from_str(&toml_content)
        .change_context_lazy(|| ErrorStack::ParseFile(path.to_owned()))?;

    if name != serdestruct.name {
        error_stack::bail!(ErrorStack::NameAndFilenameMismatch(
            serdestruct.name,
            path.to_path_buf()
        ));
    }

    Ok(MysqlDevImg {
        source_path: path.to_path_buf(),
        name: serdestruct.name,
        sql_path: etc_path.join(format!("mysqldevimg.{name}.sql")),
    })
}
