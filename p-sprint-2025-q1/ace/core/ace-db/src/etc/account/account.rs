use crate::ErrorStack;
use error_stack::ResultExt;
use ipnetwork::IpNetwork;
use regex::Regex;
use serde::Deserialize;
use serde_with::serde_as;
use std::{collections::HashMap, path::Path};

pub type AccountKey = String;
pub type AwsAccountId = String;

#[derive(Debug, Deserialize, Clone)]
#[serde(deny_unknown_fields)]
pub struct SerdeAccount {
    pub aws_account_id: AwsAccountId,

    pub public_domain: String,
    pub private_domain: String,
    pub sysadmins: Vec<String>,

    // add a default for this field
    #[serde(default)]
    pub region: HashMap<String, SerdeAccountRegion>,
}

#[serde_as]
#[derive(Debug, Deserialize, Clone)]
#[serde(deny_unknown_fields)]
pub struct SerdeAccountRegion {
    pub vpc_id: Option<String>,
    pub cidr_block: IpNetwork,
    pub ace2: bool,
}

#[derive(Debug, Clone)]
pub struct Account {
    pub account_key: String,
    pub aws_account_id: AwsAccountId,
    pub public_domain: String,
    pub private_domain: String,
    pub sysadmins: Vec<String>,
    pub region: HashMap<String, AccountRegion>,
}

#[derive(Debug, Clone)]
pub struct AccountRegion {
    pub vpc_id: Option<String>,
    pub cidr_block: IpNetwork,
    pub ace2: bool,
}

fn validate_account_id(account_id: &str) -> error_stack::Result<(), ErrorStack> {
    let re = Regex::new(r"^[0-9]{12}$").unwrap();
    if re.is_match(account_id) {
        Ok(())
    } else {
        error_stack::bail!(ErrorStack::InvalidAccountID(account_id.to_string()));
    }
}

fn validate_account_regions(
    account: &SerdeAccount,
    account_key: &AccountKey,
) -> error_stack::Result<(), ErrorStack> {
    for (region_name, region) in &account.region {
        if region.cidr_block.prefix() != 16 && region.ace2 {
            let e = format!(
                "[account.{account_key}.region.{region_name}].subnet must be a /16 network"
            );

            error_stack::bail!(ErrorStack::InvalidSubnet(e));
        }
    }

    Ok(())
}

/// # Errors
///
/// Bails only if `get_account_map()` bails.
pub async fn select(
    filter_account_key: crate::Filter<String>,
    etc_account_file_path: &Path,
) -> crate::SelectResult<Account, ErrorStack> {
    if matches!(filter_account_key, crate::Filter::None) {
        return Ok(Vec::new());
    }

    let mut rval = Vec::new();

    let account_map = get_account_map(etc_account_file_path)
        .await
        .change_context(ErrorStack::GetAccountMap)?;

    for (account_key, account) in account_map {
        // Handle filtering
        if !match &filter_account_key {
            crate::Filter::All => true,
            crate::Filter::One(filter_name) => account_key == *filter_name,
            crate::Filter::Many(filter_names) => filter_names.contains(&account_key),
            crate::Filter::None => false,
        } {
            continue;
        }

        rval.push(process_account(&account_key, &account));
    }

    Ok(rval)
}

/// # Errors
///
/// Bails under the following circumstances:
/// - If the account table is not found in the account file.
/// - If the account table cannot be un-flattened.
/// - If the account table cannot be converted into a `HashMap`.
pub async fn get_account_map(
    etc_account_file_path: &Path,
) -> error_stack::Result<HashMap<String, SerdeAccount>, ErrorStack> {
    let account_toml = super::parse_account_file(etc_account_file_path).await?;

    let Some(flat_account_table) = account_toml.get("account") else {
        error_stack::bail!(ErrorStack::NoAccountTableFound(
            etc_account_file_path.to_path_buf()
        ));
    };

    let Some(account_table) = flat_account_table.as_table() else {
        error_stack::bail!(ErrorStack::UnflattenAccountTable(
            etc_account_file_path.to_path_buf()
        ));
    };

    let account_map: HashMap<String, SerdeAccount> = account_table
        .clone()
        .try_into()
        .change_context(ErrorStack::ParseAccountHashMap(
            etc_account_file_path.to_path_buf(),
        ))?;

    Ok(account_map)
}

fn process_account(
    account_key: &str,
    account: &SerdeAccount,
) -> error_stack::Result<Account, ErrorStack> {
    validate_account_id(&account.aws_account_id).change_context(ErrorStack::ValidateAccountId)?;

    validate_account_regions(account, &account_key.to_string())
        .change_context(ErrorStack::ValidateAccountRegions)?;

    let region_map = account
        .region
        .iter()
        .map(|(region_name, region)| {
            (
                region_name.clone(),
                AccountRegion {
                    vpc_id: region.vpc_id.clone(),
                    cidr_block: region.cidr_block,
                    ace2: region.ace2,
                },
            )
        })
        .collect();

    let account = Account {
        account_key: account_key.to_string(),
        aws_account_id: account.aws_account_id.clone(),
        public_domain: account.public_domain.clone(),
        private_domain: account.private_domain.clone(),
        sysadmins: account.sysadmins.clone(),
        region: region_map,
    };

    Ok(account)
}
