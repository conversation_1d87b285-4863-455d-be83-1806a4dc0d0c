use std::path::Path;

use crate::etc::keypair::KeyPair;
use crate::ErrorStack;
use error_stack::ResultExt;

/// # Errors
///
/// Bails lazily under the following circumstances:
/// - If the file private ace key file cannot be read.
pub async fn get_public_ace_key(
    data_path: &Path,
) -> error_stack::Result<Option<KeyPair>, ErrorStack> {
    let path = data_path.join("ace2_key.pub");

    if data_path.exists() {
        let content = tokio::fs::read_to_string(path.clone())
            .await
            .change_context_lazy(|| ErrorStack::ReadFile(path.to_owned()))?;

        return Ok(Some(KeyPair {
            name: "ace".to_string(),
            public_key: content,
        }));
    }

    Ok(None)
}
