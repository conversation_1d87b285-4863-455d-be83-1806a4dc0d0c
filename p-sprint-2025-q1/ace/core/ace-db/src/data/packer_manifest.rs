use crate::ErrorStack;
use crate::PackerManifestFilter;
use error_stack::ResultExt;
use serde::Deserialize;
use std::collections::HashMap;
use std::path::Path;

#[derive(Debug, Deserialize)]
pub struct SerdeManifest {
    pub builds: Vec<SerdeBuild>,
}

#[derive(Debug, Deserialize, Clone)]
pub struct SerdeBuild {
    pub name: String,
    pub builder_type: String,
    pub build_time: u64,
    pub files: Option<Vec<String>>,
    pub artifact_id: String,
    pub packer_run_uuid: String,
    pub custom_data: Option<String>,
}

/// # Errors
///
/// Bails if the packer manifest file cannot be read/parsed.
pub async fn select(
    filter: &crate::PackerManifestFilter,
    data_path: &Path,
) -> error_stack::Result<Vec<SerdeBuild>, ErrorStack> {
    let manifest_path = data_path.join("packer.manifest.json");

    let packer_builds = parse_file(&manifest_path, filter).await?;
    Ok(packer_builds)
}

async fn parse_file(
    path: &Path,
    filter: &crate::PackerManifestFilter,
) -> error_stack::Result<Vec<SerdeBuild>, ErrorStack> {
    let content = match tokio::fs::read_to_string(path).await {
        Ok(content) => content,
        Err(err) => {
            if err.kind() == std::io::ErrorKind::NotFound {
                return Ok(Vec::new()); // There's nothing here.
            }
            error_stack::bail!(ErrorStack::ReadFile(path.to_path_buf()));
        }
    };

    let manifest: SerdeManifest =
        serde_json::from_str(&content).change_context(ErrorStack::ParseFile(path.to_path_buf()))?;

    let mut rval: Vec<SerdeBuild> = Vec::new();

    match filter {
        PackerManifestFilter::None => Ok(rval), // return early
        PackerManifestFilter::All => Ok(manifest.builds),
        PackerManifestFilter::AllByName(name) => {
            for build in manifest.builds {
                if build.name == *name {
                    rval.push(build);
                }
            }
            Ok(rval)
        }
        PackerManifestFilter::LatestOneByName(name) => {
            let mut build_map: HashMap<String, SerdeBuild> = HashMap::new();

            for build in manifest.builds {
                if build.name != *name {
                    continue;
                }
                if !build_map.contains_key(build.name.as_str()) {
                    build_map.insert(build.name.clone(), build);
                } else if let Some(existing) = build_map.get(build.name.as_str()) {
                    if build.build_time > existing.build_time {
                        build_map.insert(build.name.clone(), build);
                    }
                }
            }

            // Double check that the desired build exists in the map and pull it out
            if let Some(build) = build_map.remove(name) {
                Ok(vec![build])
            } else {
                error_stack::bail!(ErrorStack::NotOneLatestPackerManifestFound(
                    name.to_string()
                ))
            }
        }
        PackerManifestFilter::LatestEachByName => {
            let mut build_map: HashMap<String, SerdeBuild> = std::collections::HashMap::new();

            for build in manifest.builds {
                if !build_map.contains_key(build.name.as_str()) {
                    build_map.insert(build.name.clone(), build);
                } else if let Some(existing) = build_map.get(build.name.as_str()) {
                    if build.build_time > existing.build_time {
                        build_map.insert(build.name.clone(), build);
                    }
                }
            }
            Ok(build_map.into_values().collect())
        }
        PackerManifestFilter::One(name, packer_run_uuid) => {
            for build in manifest.builds {
                if build.name == *name && build.packer_run_uuid == *packer_run_uuid {
                    return Ok(vec![build]);
                }
            }
            error_stack::bail!(ErrorStack::NotOnePackerManifestFound(
                name.to_string(),
                packer_run_uuid.to_string()
            ))
        }
    }
}
