#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    NotOneIamUserPolicyAttachFound(crate::IamUserPolicyAttach, usize),
}

#[derive(Debug)]
pub struct IamUserPolicyAttach {
    pub graphkey: crate::IamUserPolicyAttach,
    pub name: String,
}

impl crate::GraphValueExt for IamUserPolicyAttach {}

pub async fn get(
    gk_iam_u_pol_att: crate::IamUserPolicyAttach,
) -> error_stack::Result<IamUserPolicyAttach, ErrorStack> {
    let filter = crate::IamUserPolicyAttachFilter::One(gk_iam_u_pol_att.clone());

    let mut atts = select(filter).await?;
    if atts.len() == 1 {
        return Ok(atts.pop().unwrap());
    }

    error_stack::bail!(ErrorStack::NotOneIamUserPolicyAttachFound(
        gk_iam_u_pol_att,
        atts.len()
    ));
}

pub async fn select(
    filter: crate::IamUserPolicyAttachFilter,
) -> error_stack::Result<Vec<IamUserPolicyAttach>, ErrorStack> {
    let mut rval = Vec::new();

    for iam_user_pol_atts in select_result(filter).await? {
        match iam_user_pol_atts {
            Ok(i) => rval.push(i),
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    _filter: crate::IamUserPolicyAttachFilter,
) -> error_stack::Result<Vec<error_stack::Result<IamUserPolicyAttach, ErrorStack>>, ErrorStack> {
    Ok(vec![])
}
