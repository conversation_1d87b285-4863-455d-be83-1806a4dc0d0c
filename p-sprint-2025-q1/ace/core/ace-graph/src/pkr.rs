use error_stack::ResultExt;
use std::{collections::HashMap, path::Path};

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    CheckForMediaproctors,
    DbError,
    GetConfig,
    IndividualRecordError,
    NonHardcodedPackerType,
    NotOneFound(usize),
    SelectEtcPackers,
    UnknownPacker(String),
}

#[derive(Debug)]
pub struct Packer {
    pub graphkey: crate::Packer,
    pub name: String,

    pub bash_text: String,
    pub source_ami_filter: SourceAmiFilter,
    pub instance_type: String,
    pub ssh_username: String,
    pub provisioners: Vec<Provisioner>,
}

impl Packer {
    #[must_use]
    /// Returns a new vector of `self.source_ami_filter.filters` sorted by key.
    pub fn sort_filters_by_key(&self) -> Vec<(String, String)> {
        let mut filter_vec: Vec<(String, String)> = self
            .source_ami_filter
            .filters
            .iter()
            .map(|(k, v)| (k.clone(), v.clone()))
            .collect();

        filter_vec.sort_by(|a, b| a.0.cmp(&b.0));
        filter_vec
    }
}

#[derive(Debug)]
pub enum Provisioner {
    File {
        /// This is the path to the file that will be copied to the server that is building the image.
        /// It is relative to the ace path.
        rel_source: std::path::PathBuf,

        /// This is the path on the server that is building the image.
        /// It MUST be absolute.
        abs_destination: std::path::PathBuf,
    },
}

#[derive(Debug, Clone)]
pub struct SourceAmiFilter {
    filters: HashMap<String, String>,
    pub owners: Vec<String>,
    pub most_recent: bool,
}

pub async fn get(
    filter: crate::PackerFilter,
    app: &ace_db::App,
) -> error_stack::Result<Packer, ErrorStack> {
    let mut packer = select_result(filter, app).await?;

    if packer.len() == 1 {
        // Found one (even if error)
        return packer.pop().unwrap();
    }

    error_stack::bail!(ErrorStack::NotOneFound(packer.len()))
}

pub async fn select(
    filter: crate::PackerFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<Packer>, ErrorStack> {
    let mut rval = Vec::new();

    // Get all packers
    for packer in select_result(filter, app).await? {
        match packer {
            Ok(packer) => {
                rval.push(packer);
            }
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

/// Returns (depending on filtering) packers from etc directory and/or hardcoded packers.
pub async fn select_result(
    filter: crate::PackerFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<Packer, ErrorStack>>, ErrorStack> {
    let (built_in_filter, db_filter) = match filter {
        crate::PackerFilter::All => (None, ace_db::Filter::All),
        crate::PackerFilter::One(gk_pkr) => match gk_pkr {
            crate::Packer::Db(name) => (None, ace_db::Filter::One(name)),
            _ => (Some(gk_pkr), ace_db::Filter::None),
        },
        crate::PackerFilter::None => return Ok(Vec::new()), // Return early
    };

    let mut rval = Vec::new();

    // Get any etc packers
    let etc_packers = ace_db::etc::packer::select(db_filter, &app.etc_path)
        .await
        .change_context(ErrorStack::SelectEtcPackers)?;

    // Process etc packers
    for entry in etc_packers {
        match entry {
            Ok(pkr) => rval.push(Ok(construct_from_etc(pkr))),
            Err(e) => rval.push(Err(e.change_context(ErrorStack::IndividualRecordError))),
        }
    }

    // Check for mediaproctors in the current environment
    let mediaproctors = crate::mediaproctor::select(crate::MediaProctorFilter::All, app)
        .await
        .change_context(ErrorStack::CheckForMediaproctors)?;

    // Include videoproc if mediaproctors exist
    let include_videoproc = !mediaproctors.is_empty();

    // Add hardcoded builtin packers:
    rval.extend(hard_coded_packers(
        built_in_filter,
        include_videoproc,
        &app.asset_path,
    ));

    Ok(rval)
}

fn construct_from_etc(pkr: ace_db::etc::packer::Packer) -> Packer {
    // Explode the etc pkr into its parts (to avoid cloning provisioners)
    let name = pkr.name;
    let instance_type = pkr.instance_type;
    let ssh_username = pkr.ssh_username;
    let most_recent = pkr.most_recent;
    let filters = pkr.filters;
    let owners = pkr.owners;
    let serde_provisioners = pkr.provisioners;
    let bash_text = pkr.bash_text;

    let source_ami_filter = SourceAmiFilter {
        filters,
        owners,
        most_recent,
    };

    let mut provisioners = Vec::new();
    for p in serde_provisioners {
        provisioners.push(Provisioner::File {
            rel_source: p.rel_source,
            abs_destination: p.abs_dest,
        });
    }

    Packer {
        graphkey: crate::Packer::Db(name.clone()),
        name,

        bash_text,
        source_ami_filter,
        instance_type,
        ssh_username,
        provisioners,
    }
}

/// If given a specific graphkey, returns a single packer.
/// If given None, returns all hardcoded packers.
fn hard_coded_packers(
    gk_pkr: Option<crate::Packer>,
    include_video_proc: bool,
    ace_db_asset_path: &Path,
) -> Vec<error_stack::Result<Packer, ErrorStack>> {
    let filtered_builtins = if let Some(gk) = gk_pkr {
        vec![gk]
    } else {
        let mut builtins = vec![
            crate::Packer::Ubuntu2204Ace2,
            crate::Packer::Ubuntu2204Devbox,
            crate::Packer::Ubuntu2204Docker,
            crate::Packer::Ubuntu2204Openvpn,
            crate::Packer::Ubuntu2204Postgresql,
        ];

        if include_video_proc {
            builtins.push(crate::Packer::Ubuntu2204Videoproc);
        }

        builtins
    };

    let mut rval = Vec::new();

    // Default information for built-in packers:
    let source_ami_filter = SourceAmiFilter {
        filters: HashMap::from([
            ("virtualization-type".to_string(), "hvm".to_string()),
            (
                "name".to_string(),
                "ubuntu/images/hvm-ssd/ubuntu-jammy-22.04-amd64-server-*".to_string(),
            ),
            ("root-device-type".to_string(), "ebs".to_string()),
        ]),
        owners: vec!["099720109477".to_string()],
        most_recent: true,
    };

    let instance_type = "c5.large".to_string();
    let ssh_username = "ubuntu".to_string();

    for builtin in filtered_builtins {
        let (bash_text, provisioners) = match builtin {
            crate::Packer::Ubuntu2204Ace2 => (get_ace2_script(), vec![]),
            crate::Packer::Ubuntu2204Devbox => (get_devbox_script(), vec![]),
            crate::Packer::Ubuntu2204Docker => (get_docker_script(), vec![]),
            crate::Packer::Ubuntu2204Openvpn => (get_openvpn_script(), vec![]),
            crate::Packer::Ubuntu2204Postgresql => (get_postgresql_script(), vec![]),
            crate::Packer::Ubuntu2204Videoproc => {
                let source_path = ace_db_asset_path;
                (
                    get_videoproc_script(),
                    vec![
                        Provisioner::File {
                            // Needs to be the absolute path...not relative
                            rel_source: source_path.join("mp-process"),
                            abs_destination: std::path::PathBuf::from("/tmp/mp-process"),
                        },
                        Provisioner::File {
                            rel_source: source_path.join("mp-stream"),
                            abs_destination: std::path::PathBuf::from("/tmp/mp-stream"),
                        },
                    ],
                )
            }
            crate::Packer::Db(_) => {
                return vec![Err(error_stack::report!(ErrorStack::UnknownPacker(
                    builtin.variant_to_name(),
                )))]
            }
        };

        rval.push(Ok(Packer {
            graphkey: builtin.clone(),
            name: builtin.variant_to_name(),

            bash_text,
            source_ami_filter: source_ami_filter.clone(),
            instance_type: instance_type.clone(),
            ssh_username: ssh_username.clone(),
            provisioners,
        }));
    }

    rval
}

fn get_ace2_script() -> String {
    #[rustfmt::skip]
    let bash_text = format!("{}\n{}", get_base_sh(), garbage::CNSL!(r#"
        
        # Basic packages 
        apt install -y ca-certificates curl gnupg lsb-release vim \
            amazon-ecr-credential-helper \
            git git-lfs rsync curl net-tools postgresql-client-14 tree jq unzip \
            easy-rsa openvpn  # For ca purposes

        # Grab easy-tls and put it on system path from https://raw.githubusercontent.com/TinCanTech/easy-tls/v2.7.0/easytls
        curl https://raw.githubusercontent.com/TinCanTech/easy-tls/v2.7.0/easytls -o /usr/local/bin/easytls
        chmod +x /usr/local/bin/easytls

        update-alternatives --set editor /usr/bin/vim.basic
        
        # Python 3.10
        apt install -y python3.10
        
        # Redis
        apt install -y redis-server redis-tools
        systemctl enable redis-server
        
        # Postgresql-14
        apt install -y postgresql-14 postgresql-client-14
        systemctl enable postgresql
               
        # Install awscli 2
        curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
        unzip awscliv2.zip
        ./aws/install

        # Enable docker
        systemctl enable docker
        systemctl start docker

        # Create empty etc/ace file (signals this is an ace server)
        sudo touch /etc/ace


    "#));

    bash_text
}

fn get_devbox_script() -> String {
    #[rustfmt::skip]
    let bash_text = format!("{}\n{}", get_base_sh(), garbage::CNSL!(r#"
        
        # Install appcove developer software repo
        curl -sLO https://appcove.github.io/developer-software/ubuntu/dists/jammy/main/binary-amd64/ads-release_1.0.0custom22.04_amd64.deb
        dpkg -i ads-release_1.0.0custom22.04_amd64.deb
    
        # Update apt
        apt update

        # Basic packages 
        apt install -y ads-everything build-essential git tig git-lfs vim rsync curl net-tools tree fzf jq libssl-dev

        # Servers for development
        apt install -y mariadb-client mariadb-server memcached redis-tools redis postgresql postgresql-contrib postgresql-client

        # enable all 
        systemctl enable mariadb memcached redis-server postgresql

        # Start all
        systemctl start mariadb memcached redis-server postgresql

        # Wait a few seconds just to make sure they are ready to go
        sleep 10

        # create a mariadb user `app` and password `pass` and database `app`
        echo 'CREATE DATABASE `app`; GRANT ALL PRIVILEGES ON *.* TO `app`@`localhost` IDENTIFIED BY PASSWORD "*196BDEDE2AE4F84CA44C47D54D78478C7E2BD7B7" WITH GRANT OPTION;' | sudo mysql

        echo "
        [client]
        user = app
        password = pass
        " > /home/<USER>/.my.cnf

        chown app:app /home/<USER>/.my.cnf
        chmod 600 /home/<USER>/.my.cnf
        
        # create a postgrsql user `app` and password `pass` and database `app`
        echo "create role app with login superuser password 'pass'; create database app owner app;" | su - -c psql postgres

        # Config git alias
        git config --global alias.ff 'merge --ff-only'

        # Enable docker
        systemctl enable docker
        systemctl start docker


    "#));

    bash_text
}

fn get_docker_script() -> String {
    #[rustfmt::skip]
    let bash_text = format!("{}\n{}", get_base_sh(), garbage::CNSL!(r"

        # Enable docker
        systemctl enable docker
        systemctl restart docker.socket
        systemctl start docker

    "));

    bash_text
}

fn get_openvpn_script() -> String {
    #[rustfmt::skip]
    let bash_text = format!("{}\n{}", get_base_sh(), garbage::CNSL!(r"

        # Basic packages 
        apt install -y curl vim rsync unzip
        update-alternatives --set editor /usr/bin/vim.basic

        # openvpn
        apt install -y openvpn 

        # Enable IP forwarding
        sed -i 's/#net.ipv4.ip_forward=1/net.ipv4.ip_forward=1/' /etc/sysctl.conf

        # Turn on nftables
        sudo systemctl enable nftables

    "));

    bash_text
}

fn get_postgresql_script() -> String {
    #[rustfmt::skip]
    let bash_text = format!("{}\n{}", get_base_minimal_sh(), garbage::CNSL!(r#"

        # Import the repository signing key:
        install -d /usr/share/postgresql-common/pgdg
        curl -o /usr/share/postgresql-common/pgdg/apt.postgresql.org.asc --fail https://www.postgresql.org/media/keys/ACCC4CF8.asc

        # Create the repository configuration file:
        sh -c 'echo "deb [signed-by=/usr/share/postgresql-common/pgdg/apt.postgresql.org.asc] https://apt.postgresql.org/pub/repos/apt $(lsb_release -cs)-pgdg main" > /etc/apt/sources.list.d/pgdg.list'

        # Update the package lists:
        apt update

        # Install the latest version of PostgreSQL:
        # If you want a specific version, use 'postgresql-16' or similar instead of 'postgresql'
        apt -y install postgresql-17 postgresql-17-pgvector

        # Postgresql
        systemctl enable postgresql
        systemctl start postgresql
    "#));

    bash_text
}

fn get_videoproc_script() -> String {
    // Use MINIMAL CONFIG
    #[rustfmt::skip]
    let bash_text = format!("{}\n{}", get_base_minimal_sh(), garbage::CNSL!(r"

        # Move files
        mv /tmp/mp-process /usr/local/bin/mp-process
        mv /tmp/mp-stream /usr/local/bin/mp-stream
        chown root:root /usr/local/bin/mp-process
        chown root:root /usr/local/bin/mp-stream
        chmod 755 /usr/local/bin/mp-process
        chmod 755 /usr/local/bin/mp-stream
        
        # Video processing tools
        apt install -y inkscape ffmpeg fontconfig

        # Install fonts for video processing 
        # This is from the google font github repo
        # If a file had an odd name like ...[wght].ttf, we renamed it to -Regular.ttf
    
        wget -O '/usr/share/fonts/AbrilFatface-Regular.ttf' 'https://github.com/google/fonts/raw/ab2b31e4bf2b49ce38ebedf10e657963521dc680/ofl/abrilfatface/AbrilFatface-Regular.ttf'

        wget -O '/usr/share/fonts/Alkatra-Regular.ttf' 'https://github.com/google/fonts/raw/ab2b31e4bf2b49ce38ebedf10e657963521dc680/ofl/alkatra/Alkatra%5Bwght%5D.ttf'

        wget -O '/usr/share/fonts/Bangers-Regular.ttf' 'https://github.com/google/fonts/raw/ab2b31e4bf2b49ce38ebedf10e657963521dc680/ofl/bangers/Bangers-Regular.ttf'

        wget -O '/usr/share/fonts/BungeeShade-Regular.ttf' 'https://github.com/google/fonts/raw/ab2b31e4bf2b49ce38ebedf10e657963521dc680/ofl/bungeeshade/BungeeShade-Regular.ttf'

        wget -O '/usr/share/fonts/Calistoga-Regular.ttf' 'https://github.com/google/fonts/raw/ab2b31e4bf2b49ce38ebedf10e657963521dc680/ofl/calistoga/Calistoga-Regular.ttf'

        wget -O '/usr/share/fonts/Caprasimo-Regular.ttf' 'https://github.com/google/fonts/raw/ab2b31e4bf2b49ce38ebedf10e657963521dc680/ofl/caprasimo/Caprasimo-Regular.ttf'

        wget -O '/usr/share/fonts/Comfortaa-Regular.ttf' 'https://github.com/google/fonts/raw/ab2b31e4bf2b49ce38ebedf10e657963521dc680/ofl/comfortaa/Comfortaa%5Bwght%5D.ttf'

        wget -O '/usr/share/fonts/Electrolize-Regular.ttf' 'https://github.com/google/fonts/raw/ab2b31e4bf2b49ce38ebedf10e657963521dc680/ofl/electrolize/Electrolize-Regular.ttf'

        wget -O '/usr/share/fonts/Lato-Regular.ttf' 'https://github.com/google/fonts/raw/ab2b31e4bf2b49ce38ebedf10e657963521dc680/ofl/lato/Lato-Regular.ttf'

        wget -O '/usr/share/fonts/LilitaOne-Regular.ttf' 'https://github.com/google/fonts/raw/ab2b31e4bf2b49ce38ebedf10e657963521dc680/ofl/lilitaone/LilitaOne-Regular.ttf'

        wget -O '/usr/share/fonts/Lobster-Regular.ttf' 'https://github.com/google/fonts/raw/ab2b31e4bf2b49ce38ebedf10e657963521dc680/ofl/lobster/Lobster-Regular.ttf'

        wget -O '/usr/share/fonts/RampartOne-Regular.ttf' 'https://github.com/google/fonts/raw/ab2b31e4bf2b49ce38ebedf10e657963521dc680/ofl/rampartone/RampartOne-Regular.ttf'

        wget -O '/usr/share/fonts/Righteous-Regular.ttf' 'https://github.com/google/fonts/raw/ab2b31e4bf2b49ce38ebedf10e657963521dc680/ofl/righteous/Righteous-Regular.ttf'

        wget -O '/usr/share/fonts/Roboto-Regular.ttf' 'https://github.com/google/fonts/raw/ab2b31e4bf2b49ce38ebedf10e657963521dc680/apache/roboto/Roboto%5Bwdth%2Cwght%5D.ttf'

        fc-cache -f

    "));

    bash_text
}

#[rustfmt::skip]
#[must_use]
pub fn get_base_minimal_sh() -> String {
    garbage::STSL!(r#"
        #!/bin/bash
                
        set -x
        
        export DEBIAN_FRONTEND=noninteractive 

        sleep 10
        
        apt update
        apt upgrade -y

        apt install -y \
            ca-certificates \
            curl \
            gnupg \
            lsb-release \
            vim \
            rsync

        update-alternatives --set editor /usr/bin/vim.basic
        mkdir -p /etc/apt/keyrings

        groupadd app
        useradd app -d /home/<USER>/bin/bash -m -g app
            
        rsync -a /home/<USER>/.ssh /home/<USER>/
        chown -R app:app /home/<USER>
        
        echo "app ALL=(ALL) NOPASSWD:ALL" > /etc/sudoers.d/91-app-user

    "#)
}

#[rustfmt::skip]
#[must_use]
pub fn get_base_sh() -> String {
    garbage::STSL!(r#"
        #!/bin/bash
                
        set -x
        
        export DEBIAN_FRONTEND=noninteractive 
        
        sleep 10
        
        apt update
        apt upgrade -y
        
        apt install -y \
            ca-certificates \
            curl \
            gnupg \
            lsb-release \
            vim \
            amazon-ecr-credential-helper \
            git-lfs \
            rsync \
            python3 \
            python3-requests \
            python3-rich \
            python3-toml \
            python3-yaml

        update-alternatives --set editor /usr/bin/vim.basic
        
        mkdir -p /etc/apt/keyrings
        
        # Docker repo
        curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /etc/apt/keyrings/docker.gpg
        echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" > /etc/apt/sources.list.d/docker.list

        apt update
        
        apt install -y docker-ce-cli containerd.io docker-compose-plugin docker-ce amazon-ecr-credential-helper
        systemctl disable docker
        
        groupadd app
        useradd app -d /home/<USER>/bin/bash -m -g app -G docker
            
        rsync -a --chown=app:app /home/<USER>/.ssh /home/<USER>/
        
        echo "app ALL=(ALL) NOPASSWD:ALL" > /etc/sudoers.d/91-app-user
        
        # Configure credential helper 
        mkdir -p /home/<USER>/.docker;
        echo '{ "credsStore": "ecr-login" }' > /home/<USER>/.docker/config.json
        chown -R app:app /home/<USER>/.docker
        chmod 700 /home/<USER>/.docker

        

    "#)
}
