use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    SelectDevInstances,
    SelectAppInstances,
    NotOneInstanceProfileFound(crate::InstanceProfile, usize),
    IndividualError,
}

#[derive(Debug)]
pub struct InstanceProfile {
    pub graphkey: crate::InstanceProfile,
    pub name: String,
}

impl crate::GraphValueExt for InstanceProfile {}

pub async fn get(
    gk_ins_profile: crate::InstanceProfile,
    app: &ace_db::App,
) -> error_stack::Result<InstanceProfile, ErrorStack> {
    let filter = crate::InstanceProfileFilter::One(gk_ins_profile.clone());

    let mut ins_profiles = select(filter, app).await?;
    if ins_profiles.len() == 1 {
        return Ok(ins_profiles.pop().unwrap());
    }

    error_stack::bail!(ErrorStack::NotOneInstanceProfileFound(
        gk_ins_profile,
        ins_profiles.len()
    ));
}

pub async fn select(
    filter: crate::InstanceProfileFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<InstanceProfile>, ErrorStack> {
    let mut rval = Vec::new();

    for inst_profiles in select_result(&filter, app).await? {
        match inst_profiles {
            Ok(i) => rval.push(i),
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    filter: &crate::InstanceProfileFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<InstanceProfile, ErrorStack>>, ErrorStack> {
    let mut rval = Vec::new();

    // Since InstanceProfiles depend purely on instances...but only App and Developer instances, redirect over to those modules
    // We do not want the builtin instances (like Ace, Graylog etc) to be included

    let ins_filter = match filter {
        crate::InstanceProfileFilter::One(gk) => match gk {
            crate::InstanceProfile::Instance(ins_gk) => crate::InstanceFilter::One(ins_gk.clone()),
        },
        crate::InstanceProfileFilter::All => crate::InstanceFilter::All,
        crate::InstanceProfileFilter::None => crate::InstanceFilter::None,
    };

    let dev_ins = crate::developer::select_result_instance(&ins_filter, app)
        .await
        .change_context_lazy(|| ErrorStack::SelectDevInstances)?;
    let app_ins = crate::app::select_result_instance(&ins_filter, app)
        .await
        .change_context_lazy(|| ErrorStack::SelectAppInstances)?;

    for ins in dev_ins {
        match ins {
            Ok(i) => rval.push(Ok(construct_from_ins(&i))),
            Err(e) => rval.push(Err(e).change_context(ErrorStack::IndividualError)),
        }
    }

    for ins in app_ins {
        match ins {
            Ok(i) => rval.push(Ok(construct_from_ins(&i))),
            Err(e) => rval.push(Err(e).change_context(ErrorStack::IndividualError)),
        }
    }

    Ok(rval)
}

fn construct_from_ins(ins: &crate::ins::Instance) -> InstanceProfile {
    InstanceProfile {
        graphkey: crate::InstanceProfile::Instance(ins.graphkey.clone()),
        name: ins.name.clone(),
    }
}
