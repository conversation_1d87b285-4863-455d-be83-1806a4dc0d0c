#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    NotOneIamPolicyFound(crate::IamPolicy, usize),
}

#[derive(Debug)]
pub struct IamPolicy {
    pub graphkey: crate::IamPolicy,
    pub name: String,
}

impl crate::GraphValueExt for IamPolicy {}

pub async fn get(gk_iam_r_pol: crate::IamPolicy) -> error_stack::Result<IamPolicy, ErrorStack> {
    let filter = crate::IamPolicyFilter::One(gk_iam_r_pol.clone());

    let mut atts = select(filter).await?;
    if atts.len() == 1 {
        return Ok(atts.pop().unwrap());
    }

    error_stack::bail!(ErrorStack::NotOneIamPolicyFound(gk_iam_r_pol, atts.len()));
}

pub async fn select(
    filter: crate::IamPolicyFilter,
) -> error_stack::Result<Vec<IamPolicy>, ErrorStack> {
    let mut rval = Vec::new();

    for iam_policies in select_result(filter).await? {
        match iam_policies {
            Ok(i) => rval.push(i),
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    _filter: crate::IamPolicyFilter,
) -> error_stack::Result<Vec<error_stack::Result<IamPolicy, ErrorStack>>, ErrorStack> {
    Ok(vec![])
}
