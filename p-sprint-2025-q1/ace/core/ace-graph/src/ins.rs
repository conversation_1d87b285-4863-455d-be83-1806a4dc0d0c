use crate::GraphKeyName;
use error_stack::ResultExt;
use serde::{Deserialize, Serialize};
use std::{fmt::Display, path::Path};

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    DbError,
    DeserializeAmiGraphkey(String, std::path::PathBuf),
    GetAceIngress,
    GetConfig,
    GetGraylogIngress,
    GetInstanceFromDb(crate::Instance),
    GetOpenVpnIngress,
    IpNetworkParse(ipnetwork::IpNetworkError),
    NotOneFound(crate::Instance, usize),
    SelectFromApp,
    SelectFromDb,
    SelectFromDeveloper,
}

#[derive(Debug)]
pub struct Instance {
    pub graphkey: crate::Instance,
    pub name: String,
    pub subnet: String,
    pub instance_type: String,
    pub ami_graphkey: crate::Ami,
    pub volume_size: VolumeSize,
    pub ingress: Vec<InstanceIngress>,
    pub use_elastic_ip: bool,
}

impl crate::GraphValueExt for Instance {}

impl GraphKeyName for crate::Instance {
    fn get_name(&self) -> String {
        match self {
            crate::Instance::Ace => "ace".to_string(),
            crate::Instance::App(app) => app.get_name(),
            crate::Instance::Developer(developer) => developer.get_name(),
            crate::Instance::Db(name) => name.clone(),
            crate::Instance::Graylog => "graylog".to_string(),
            crate::Instance::Vpn => "vpn".to_string(),
        }
    }
}

#[derive(Debug)]
pub struct InstanceIngress {
    pub protocol: String,
    pub ports: PortRange,
    pub cidr_blocks: Vec<ipnetwork::IpNetwork>,
}

#[derive(Debug, Deserialize, Serialize)]
pub enum VolumeSize {
    Specific(u32),
    DeterminedByAws,
}

impl std::fmt::Display for VolumeSize {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            VolumeSize::Specific(size) => write!(f, "{}GB", size),
            VolumeSize::DeterminedByAws => write!(f, "auto"),
        }
    }
}

#[derive(Debug)]
pub enum PortRange {
    /// Represents -1 for ICMP
    All,
    One(u16),
    Range(u16, u16),
}

impl PortRange {
    pub fn to_aws_from_port(&self) -> u16 {
        match self {
            PortRange::All => 0,
            PortRange::One(port) => *port,
            PortRange::Range(from, _to) => *from,
        }
    }
    pub fn to_aws_to_port(&self) -> u16 {
        match self {
            PortRange::All => 0,
            PortRange::One(port) => *port,
            PortRange::Range(_from, to) => *to,
        }
    }
}

impl Display for PortRange {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            PortRange::All => write!(f, "all"),
            PortRange::One(port) => write!(f, "{}", port),
            PortRange::Range(from, to) => write!(f, "{}-{}", from, to),
        }
    }
}

impl crate::GraphValueExt for InstanceIngress {}

pub async fn get(
    gk: &crate::Instance,
    app: &ace_db::App,
) -> error_stack::Result<Instance, ErrorStack> {
    let ins_filter = crate::InstanceFilter::One(gk.clone());
    let mut instances = select_result(&ins_filter, app)
        .await
        .change_context(ErrorStack::GetInstanceFromDb(gk.clone()))?;

    let instance: Result<Instance, error_stack::Report<ErrorStack>> = {
        if instances.len() == 1 {
            instances.pop().unwrap()
        } else {
            error_stack::bail!(ErrorStack::NotOneFound(gk.clone(), instances.len()));
        }
    };

    instance.change_context_lazy(|| ErrorStack::GetInstanceFromDb(gk.clone()))
}

pub async fn select(
    filter: &crate::InstanceFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<Instance>, ErrorStack> {
    // Fails at ANY error found
    let mut rval = Vec::new();

    for instance in select_result(filter, app).await? {
        match instance {
            Ok(instance) => {
                rval.push(instance);
            }
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    filter: &crate::InstanceFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<Instance, ErrorStack>>, ErrorStack> {
    let mut rval = Vec::new();

    let config = crate::config::get(&crate::Config::EtcConfig, app)
        .await
        .change_context_lazy(|| ErrorStack::GetConfig)?;

    // Handle the unique Instances: (Ace, Vpn, Graylog)
    if matches!(
        filter,
        crate::InstanceFilter::One(crate::Instance::Ace) | crate::InstanceFilter::All
    ) {
        let ingress = ace_instance_ingress(&config)
            .await
            .change_context_lazy(|| ErrorStack::GetAceIngress)?;

        // Include the Ace instance here
        rval.push(Ok(Instance {
            graphkey: crate::Instance::Ace,
            name: "ace".to_string(),
            subnet: "sn-ace".to_string(),
            instance_type: "t3.small".to_string(),
            ami_graphkey: crate::Ami::Packer(crate::Packer::Ubuntu2204Ace2),
            volume_size: VolumeSize::Specific(config.ace_instance_volume_size),
            ingress,
            use_elastic_ip: true,
        }));
    }

    // vpn only exists if mentioned in config
    if config.vpn.is_some()
        && matches!(
            filter,
            crate::InstanceFilter::One(crate::Instance::Vpn) | crate::InstanceFilter::All
        )
    {
        let ingress = openvpn_ingress(&config)
            .await
            .change_context_lazy(|| ErrorStack::GetOpenVpnIngress)?;

        // Include the Vpn instance here
        rval.push(Ok(Instance {
            graphkey: crate::Instance::Vpn,
            name: "vpn".to_string(),
            subnet: "sn-vpn".to_string(),
            instance_type: "t3.micro".to_string(),
            ami_graphkey: crate::Ami::Packer(crate::Packer::Ubuntu2204Openvpn),
            volume_size: VolumeSize::DeterminedByAws,
            ingress,
            use_elastic_ip: true,
        }));
    }

    // greylog only exists if mentioned in config
    if config.graylog.is_some()
        && matches!(
            filter,
            crate::InstanceFilter::One(crate::Instance::Graylog) | crate::InstanceFilter::All
        )
    {
        let ingress = graylog_ingress(&config)
            .await
            .change_context_lazy(|| ErrorStack::GetGraylogIngress)?;

        // Include the Graylog instance here
        rval.push(Ok(Instance {
            graphkey: crate::Instance::Graylog,
            name: "graylog".to_string(),
            subnet: "sn-ace".to_string(),
            instance_type: "t3.micro".to_string(),
            ami_graphkey: crate::Ami::Packer(crate::Packer::Ubuntu2204Docker),
            volume_size: VolumeSize::DeterminedByAws,
            ingress,
            use_elastic_ip: false,
        }));
    }

    let (db_filter, app_ins_filter, dev_ins_filter) = match &filter {
        crate::InstanceFilter::All => (
            ace_db::Filter::All,
            crate::InstanceFilter::All,
            crate::InstanceFilter::All,
        ),
        crate::InstanceFilter::One(ins_gk) => match ins_gk {
            crate::Instance::Ace => (
                ace_db::Filter::None,
                crate::InstanceFilter::None,
                crate::InstanceFilter::None,
            ),
            crate::Instance::App(_app) => (
                ace_db::Filter::None,
                crate::InstanceFilter::One(ins_gk.clone()),
                crate::InstanceFilter::None,
            ),
            crate::Instance::Developer(_developer) => (
                ace_db::Filter::None,
                crate::InstanceFilter::None,
                crate::InstanceFilter::One(ins_gk.clone()),
            ),
            crate::Instance::Db(name) => (
                ace_db::Filter::One(name.clone()),
                crate::InstanceFilter::None,
                crate::InstanceFilter::None,
            ), // Only variant relevant to ace_db
            crate::Instance::Graylog => (
                ace_db::Filter::None,
                crate::InstanceFilter::None,
                crate::InstanceFilter::None,
            ),
            crate::Instance::Vpn => (
                ace_db::Filter::None,
                crate::InstanceFilter::None,
                crate::InstanceFilter::None,
            ),
        },
        crate::InstanceFilter::None => (
            ace_db::Filter::None,
            crate::InstanceFilter::None,
            crate::InstanceFilter::None,
        ),
    };

    // Collect from etc
    let etc_instances = ace_db::etc::instance::select(db_filter, &app.etc_path)
        .await
        .change_context_lazy(|| ErrorStack::SelectFromDb)?;

    for etc_instance in etc_instances {
        match etc_instance {
            Ok(etc_instance) => {
                let instance = construct(etc_instance, &app.etc_path)
                    .await
                    .change_context_lazy(|| ErrorStack::SelectFromDb)?;
                rval.push(Ok(instance));
            }
            Err(e) => {
                rval.push(Err(e.change_context(ErrorStack::SelectFromDb)));
            }
        }
    }

    // Collect from Apps
    rval.extend(
        crate::app::select_result_instance(&app_ins_filter, app)
            .await
            .change_context_lazy(|| ErrorStack::SelectFromApp)?
            .into_iter()
            .map(|r| r.change_context_lazy(|| ErrorStack::SelectFromApp)),
    );

    // Collect from Developers
    rval.extend(
        crate::developer::select_result_instance(&dev_ins_filter, app)
            .await
            .change_context_lazy(|| ErrorStack::SelectFromDeveloper)?
            .into_iter()
            .map(|r| r.change_context_lazy(|| ErrorStack::SelectFromDeveloper)),
    );

    Ok(rval)
}

async fn construct(
    instance: ace_db::etc::instance::Instance,
    etc_path: &Path,
) -> error_stack::Result<Instance, ErrorStack> {
    let mut ingress = Vec::new();

    for i_ingress in instance.ingress {
        ingress.push(InstanceIngress {
            protocol: i_ingress.protocol,
            ports: match i_ingress.ports {
                ace_db::etc::instance::PortRange::One(port) => PortRange::One(port),
                ace_db::etc::instance::PortRange::Range(from, to) => PortRange::Range(from, to),
                ace_db::etc::instance::PortRange::All => PortRange::All,
            },
            cidr_blocks: i_ingress.cidr_blocks,
        });
    }

    // Deserialize the instance's ami_id to a crate::Ami (GraphKeyExt)
    let ami = match crate::GraphKeyExt::deserialize(&instance.ami) {
        Ok(a) => a,
        Err(e) => {
            // For the moment, all instances come from the etc directory, so reconstructing the path this way is safe.
            let file_name = format!("instance.{}.toml", &instance.name);
            let file_path = etc_path.join(file_name);

            error_stack::bail!(ErrorStack::DeserializeAmiGraphkey(
                e,
                file_path.to_path_buf()
            ));
        }
    };

    Ok(Instance {
        graphkey: crate::Instance::Db(instance.name.clone()),
        name: instance.name,
        subnet: instance.subnet,
        instance_type: instance.instance_type,
        ami_graphkey: ami,
        volume_size: VolumeSize::Specific(instance.volume_size),
        ingress,
        use_elastic_ip: instance.use_elastic_ip,
    })
}

async fn ace_instance_ingress(
    config: &crate::config::Config,
) -> error_stack::Result<Vec<InstanceIngress>, ErrorStack> {
    let mut rval = Vec::new();

    let ten_cidr_block = match "10.0.0.0/8".parse::<ipnetwork::Ipv4Network>() {
        Ok(c) => c,
        Err(e) => {
            error_stack::bail!(ErrorStack::IpNetworkParse(e));
        }
    };

    // Allow inbound SSH traffic from my ip
    rval.push(InstanceIngress {
        ports: PortRange::One(22),
        protocol: "tcp".to_string(),
        cidr_blocks: vec![ipnetwork::IpNetwork::V4(ten_cidr_block)],
    });

    // allow inbound HTTPS traffic
    rval.push(InstanceIngress {
        ports: PortRange::One(443),
        protocol: "tcp".to_string(),
        cidr_blocks: vec![ipnetwork::IpNetwork::V4(ten_cidr_block)],
    });

    // Allow inbound ICMP traffic from private network
    rval.push(InstanceIngress {
        ports: PortRange::All,
        protocol: "icmp".to_string(),
        cidr_blocks: vec![ipnetwork::IpNetwork::V4(ten_cidr_block)],
    });

    // Allow inbound SSH traffic from my ip
    rval.push(InstanceIngress {
        ports: PortRange::One(22),
        protocol: "tcp".to_string(),
        cidr_blocks: vec![config.my_ipnetwork],
    });

    // Allow inbound SSH traffic
    rval.push(InstanceIngress {
        ports: PortRange::One(22),
        protocol: "tcp".to_string(),
        cidr_blocks: config.ace_instance_allow_ssh_from.clone(),
    });

    Ok(rval)
}

async fn openvpn_ingress(
    config: &crate::config::Config,
) -> error_stack::Result<Vec<InstanceIngress>, ErrorStack> {
    let mut rval = vec![];

    // Allow inbound SSH traffic from vpc
    rval.push(InstanceIngress {
        ports: PortRange::One(22),
        protocol: "tcp".to_string(),
        cidr_blocks: vec![], // Bad?  Can't know - it's the main vpc cidr block.
    });

    // Allow inbound SSH traffic from my ip
    rval.push(InstanceIngress {
        ports: PortRange::One(22),
        protocol: "tcp".to_string(),
        cidr_blocks: vec![config.my_ipnetwork],
    });

    let zero_cidr_block = match "0.0.0.0/0".parse::<ipnetwork::Ipv4Network>() {
        Ok(c) => c,
        Err(e) => {
            error_stack::bail!(ErrorStack::IpNetworkParse(e));
        }
    };

    // Allow openvpn traffic
    rval.push(InstanceIngress {
        ports: PortRange::One(1194),
        protocol: "udp".to_string(),
        cidr_blocks: vec![ipnetwork::IpNetwork::V4(zero_cidr_block)],
    });

    Ok(rval)
}

async fn graylog_ingress(
    config: &crate::config::Config,
) -> error_stack::Result<Vec<InstanceIngress>, ErrorStack> {
    let mut rval = vec![];

    // Allow inbound SSH traffic from vpc
    rval.push(InstanceIngress {
        ports: PortRange::One(22),
        protocol: "tcp".to_string(),
        cidr_blocks: vec![], // Bad?  Can't know - it's the main vpc cidr block.
    });

    // Allow inbound SSH traffic from my ip
    rval.push(InstanceIngress {
        ports: PortRange::One(22),
        protocol: "tcp".to_string(),
        cidr_blocks: vec![config.my_ipnetwork],
    });

    let zero_cidr_block = match "0.0.0.0/0".parse::<ipnetwork::Ipv4Network>() {
        Ok(c) => c,
        Err(e) => {
            error_stack::bail!(ErrorStack::IpNetworkParse(e));
        }
    };

    rval.push(InstanceIngress {
        ports: PortRange::One(80),
        protocol: "tcp".to_string(),
        cidr_blocks: vec![ipnetwork::IpNetwork::V4(zero_cidr_block)],
    });

    rval.push(InstanceIngress {
        ports: PortRange::One(443),
        protocol: "tcp".to_string(),
        cidr_blocks: vec![ipnetwork::IpNetwork::V4(zero_cidr_block)],
    });

    Ok(rval)
}
