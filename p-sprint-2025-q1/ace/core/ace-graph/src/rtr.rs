#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    NotOneRouteTableRouteFound(crate::RouteTableRoute, usize),
}

#[derive(Debug)]
pub struct RouteTableRoute {
    pub graphkey: crate::RouteTableRoute,
    pub name: String,
    // Other information...
}

impl crate::GraphValueExt for RouteTableRoute {}

pub async fn get(
    gk_route_table_route: crate::RouteTableRoute,
) -> error_stack::Result<RouteTableRoute, ErrorStack> {
    let filter = crate::RouteTableRouteFilter::One(gk_route_table_route.clone());

    let mut rtrs = select(filter).await?;
    if rtrs.len() == 1 {
        return Ok(rtrs.pop().unwrap());
    }

    error_stack::bail!(ErrorStack::NotOneRouteTableRouteFound(
        gk_route_table_route,
        rtrs.len()
    ));
}

pub async fn select(
    filter: crate::RouteTableRouteFilter,
) -> error_stack::Result<Vec<RouteTableRoute>, ErrorStack> {
    let mut rval = Vec::new();

    for bucket in select_result(filter).await? {
        match bucket {
            Ok(bucket) => rval.push(bucket),
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    _filter: crate::RouteTableRouteFilter,
) -> error_stack::Result<Vec<error_stack::Result<RouteTableRoute, ErrorStack>>, ErrorStack> {
    Ok(vec![])
}
