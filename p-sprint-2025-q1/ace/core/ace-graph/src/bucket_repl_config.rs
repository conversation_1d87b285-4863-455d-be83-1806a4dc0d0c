#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    NotOneBucketReplConfigFound(crate::BucketReplicationConfig, usize),
}

#[derive(Debug)]
pub struct BucketReplConfig {
    pub graphkey: crate::BucketReplicationConfig,
    pub name: String,
}

impl crate::GraphValueExt for BucketReplConfig {}

pub async fn get(
    gk_b_repl_conf: crate::BucketReplicationConfig,
) -> error_stack::Result<BucketReplConfig, ErrorStack> {
    let filter = crate::BucketReplicationConfigFilter::One(gk_b_repl_conf.clone());

    let mut b_repl_confs = select(filter).await?;
    if b_repl_confs.len() == 1 {
        return Ok(b_repl_confs.pop().unwrap());
    }

    error_stack::bail!(ErrorStack::NotOneBucketReplConfigFound(
        gk_b_repl_conf,
        b_repl_confs.len()
    ));
}

pub async fn select(
    filter: crate::BucketReplicationConfigFilter,
) -> error_stack::Result<Vec<BucketReplConfig>, ErrorStack> {
    let mut rval = Vec::new();

    for b_repl_confs in select_result(filter).await? {
        match b_repl_confs {
            Ok(b) => rval.push(b),
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    _filter: crate::BucketReplicationConfigFilter,
) -> error_stack::Result<Vec<error_stack::Result<BucketReplConfig, ErrorStack>>, ErrorStack> {
    Ok(vec![])
}
