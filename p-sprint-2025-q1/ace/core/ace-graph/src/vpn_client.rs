use std::path::Path;

use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    DbError,
    GetVpnClient(crate::VpnClient),
    NotOneFound(crate::VpnClient),
}

#[derive(Debu<PERSON>, <PERSON>lone)]
/// Represents a VPN client.
pub struct VpnClient {
    pub graphkey: crate::VpnClient,
    pub common_name: String,
    pub client_cert: String,
    pub ca: String,
    pub not_before: chrono::DateTime<chrono::FixedOffset>,
    pub not_after: chrono::DateTime<chrono::FixedOffset>,
}

impl crate::GraphValueExt for VpnClient {}

/// Selects a single `VpnClient` by calling `select_result()` with a filter based on the graphkey input.
/// Bails if anything other than one record is returned.
pub async fn get(
    gk_vpn_client: &crate::VpnClient,
    vpn_path: &Path,
) -> error_stack::Result<VpnClient, ErrorStack> {
    let filter = crate::VpnClientFilter::One(gk_vpn_client.clone());
    let mut vpn_clients = select_result(&filter, vpn_path).await?;

    // Validate that one record was returned
    let vpn_client: Result<VpnClient, error_stack::Report<ErrorStack>> = {
        // if length != 1, return error
        if vpn_clients.len() == 1 {
            vpn_clients.pop().unwrap()
        } else {
            error_stack::bail!(ErrorStack::NotOneFound(gk_vpn_client.clone()))
        }
    };

    vpn_client.change_context_lazy(|| ErrorStack::GetVpnClient(gk_vpn_client.to_owned()))
}

/// An error-catching wrapper around `select_result()`.  Bails at any error found in results from `select_result()`.
/// (Produces only correct output)
pub async fn select(
    filter: &crate::VpnClientFilter,
    vpn_path: &Path,
) -> error_stack::Result<Vec<VpnClient>, ErrorStack> {
    // Fails at ANY error found
    let mut rval = Vec::new();

    for vpn in select_result(filter, vpn_path).await? {
        match vpn {
            Ok(v) => {
                rval.push(v);
            }
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

/// Selects raw ace-db `VpnClient` records and constructs them into finished `VpnClients`.
/// Converts input filter into an ace-db filter.
///
/// Bails only if there is an overall error talking to ace-db.
/// Otherwise keeps track of all individual erroneous records, but denotes a `DbError`.
pub async fn select_result(
    filter: &crate::VpnClientFilter,
    vpn_path: &Path,
) -> error_stack::Result<Vec<error_stack::Result<VpnClient, ErrorStack>>, ErrorStack> {
    let db_filter = match filter {
        crate::VpnClientFilter::All => ace_db::Filter::All,
        crate::VpnClientFilter::One(gk_vpn_client) => match gk_vpn_client {
            crate::VpnClient::Manual(crate::Vpn::Ace, common_name) => {
                ace_db::Filter::One(common_name.clone())
            }
        },
        crate::VpnClientFilter::None => ace_db::Filter::None,
    };

    let vpn_clients = ace_db::vpn::ovpn::select(db_filter, vpn_path)
        .await
        .change_context(ErrorStack::DbError)?;

    let mut rval = Vec::new();

    for vpn_client in vpn_clients {
        match vpn_client {
            Ok(v) => {
                rval.push(Ok(construct(v)));
            }
            Err(e) => rval.push(Err(e.change_context(ErrorStack::DbError))),
        }
    }

    Ok(rval)
}

/// Constructs a `VpnClient` from an `ace-db::vpn::ovpn::VpnClient` record. (Mostly just adds a graphkey)
fn construct(vpn: ace_db::vpn::ovpn::VpnClient) -> VpnClient {
    VpnClient {
        graphkey: crate::VpnClient::Manual(crate::Vpn::Ace, vpn.common_name.clone()),
        common_name: vpn.common_name,
        client_cert: vpn.client_cert,
        ca: vpn.ca_cert,
        not_before: vpn.not_before,
        not_after: vpn.not_after,
    }
}
