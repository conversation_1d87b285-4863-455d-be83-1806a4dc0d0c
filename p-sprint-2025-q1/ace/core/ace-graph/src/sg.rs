use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    AppSecurityGroup,
    DeveloperSecurityGroup,
    SelectFromApp,
    SelectFromDeveloper,
}

#[derive(Debug)]
pub struct SecurityGroup {
    pub graphkey: crate::SecurityGroup,
    pub name: String,
}

impl crate::GraphValueExt for SecurityGroup {}

pub async fn select(
    filter: &crate::SecurityGroupFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<SecurityGroup>, ErrorStack> {
    let mut rval = Vec::new();

    for sg in select_result(filter, app).await? {
        match sg {
            Ok(sg) => {
                rval.push(sg);
            }
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    filter: &crate::SecurityGroupFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<SecurityGroup, ErrorStack>>, ErrorStack> {
    let mut rval = Vec::new();

    rval.extend(
        crate::developer::select_result_securitygroup(filter, app)
            .await
            .change_context_lazy(|| ErrorStack::SelectFromDeveloper)?
            .into_iter()
            .map(|v| v.change_context(ErrorStack::DeveloperSecurityGroup)),
    );

    rval.extend(
        crate::app::select_result_securitygroup(filter, app)
            .await
            .change_context_lazy(|| ErrorStack::SelectFromApp)?
            .into_iter()
            .map(|v| v.change_context(ErrorStack::AppSecurityGroup)),
    );

    Ok(rval)
}
