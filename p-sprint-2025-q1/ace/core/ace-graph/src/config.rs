use error_stack::ResultExt;
use ipnetwork::IpNetwork;
use std::collections::HashMap;
use std::path::PathBuf;

#[derive(Debug)]
pub struct Config {
    pub account_key_at_region: String,
    pub account_key: String,
    pub account: crate::account::Account,
    pub ace_hostname: String,
    pub ace_instance_allow_ssh_from: Vec<IpNetwork>,
    pub ace_instance_volume_size: u32,
    pub ace2_private_key_file_path: PathBuf,
    pub ace2_public_key_file_path: PathBuf,
    pub ace2_server_name_prefix: String,
    pub ace2_server_name: String,
    pub agent_version: String,
    pub aws_account_id: String,
    pub create_nat_gateways: bool,
    pub ecr_registry: String,
    pub graylog: Option<Graylog>,
    pub hosted_zone_map: HashMap<String, HostedZone>,
    pub is_ace_instance: bool,
    pub my_ip: std::net::Ipv4Addr,
    pub my_ipnetwork: IpNetwork,
    pub private_domain_name: String,
    pub private_subdomain_name: String,
    pub public_domain_name: String,
    pub public_subdomain_name: String,
    pub region_at_account_key: String,
    pub region: String,
    pub cidr_block: IpNetwork,
    pub users: Vec<crate::User>,
    pub vpc_public_subnet_map: HashMap<String, String>,
    pub vpn_client_cidr_block: IpNetwork,
    pub vpn_cidr_block: IpNetwork,
    pub vpn: Option<Vpn>,
    pub zerossl_key: Option<String>,
}

impl super::GraphValueExt for Config {}

#[derive(Debug)]
pub struct Graylog {
    pub server_name: String,
    pub instance_type: String,
    pub volume_size: u32,
}

#[derive(Debug, Clone)]
pub struct Vpn {
    pub public_server_name: String,
    pub private_server_name: String,
    pub instance_type: String,
    pub volume_size: u32,
    pub additional_routed_subnets: Vec<IpNetwork>,
}

#[derive(Debug, Clone)]
pub struct HostedZone {
    pub key: String,
    pub hosted_zone_id: Option<String>,
}

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    AceDbAPPIpIsNone,
    GetCurrentAccountFromDB,
    GetHostname,
    InvalidSubnet(IpNetwork),
    LoadingAccountFromDb,
    LoadingConfigFromDb,
    LoadingDataFromDb,
    NewIpv4Network,
    RegionNotFound(String),
    SelectUsers,
    WrongConfigType,
}

pub async fn get_default(app: &ace_db::App) -> error_stack::Result<Config, ErrorStack> {
    get(&crate::Config::EtcConfig, app).await
}

#[allow(clippy::too_many_lines)]
pub async fn get(
    graphkey: &crate::Config,
    app: &ace_db::App,
) -> error_stack::Result<Config, ErrorStack> {
    #[allow(unreachable_patterns)]
    let etc_config = match graphkey {
        crate::Config::EtcConfig => app.etc_config.clone(),
        _ => {
            error_stack::bail!(ErrorStack::WrongConfigType);
        }
    };

    let db_terraform_data = ace_db::data::terraform_output::get(&app.data_path)
        .await
        .change_context(ErrorStack::LoadingDataFromDb)?;

    let account_key = &etc_config.account_key;
    let account = crate::account::get(&crate::Account::Db(account_key.clone()), app)
        .await
        .change_context(ErrorStack::GetCurrentAccountFromDB)?;

    let account_region = match account.region.get(&etc_config.region) {
        Some(region) => region.clone(),
        None => {
            eprintln!(
                "Error: region {} not found in account {} (etc/account.toml)",
                etc_config.region, account_key
            );
            std::process::exit(1);
        }
    };

    let mut hosted_zone_map = HashMap::new();
    hosted_zone_map.insert(
        "ace-public-domain".to_string(),
        HostedZone {
            key: "public_domain".to_string(),
            hosted_zone_id: db_terraform_data.dns_public_domain_zone_id,
        },
    );
    hosted_zone_map.insert(
        "ace-private-domain".to_string(),
        HostedZone {
            key: "private_domain".to_string(),
            hosted_zone_id: db_terraform_data.dns_private_domain_zone_id,
        },
    );

    let my_ip = app.my_ip;
    let my_ipnetwork = ipnetwork::IpNetwork::V4(
        ipnetwork::Ipv4Network::new(my_ip, 32).change_context(ErrorStack::NewIpv4Network)?,
    );

    let zerossl_key = etc_config.zerossl_key.clone();
    let region = etc_config.region.clone();

    let region_at_account_key = format!("{region}@{account_key}");
    let account_key_at_region = format!("{account_key}@{region}");

    let ecr_registry = format!(
        "{}.dkr.ecr.{}.amazonaws.com",
        &account.aws_account_id, &region
    );

    let aws_account_id = account.aws_account_id.clone();
    let public_domain_name = account.public_domain.clone();
    let private_domain_name = account.private_domain.clone();
    let public_subdomain_name = format!("{region}.{public_domain_name}");
    let private_subdomain_name = format!("{region}.{private_domain_name}");
    let cidr_block = account_region.cidr_block;

    let mut vpc_subnet_map = HashMap::new();
    if let Some(subnet_id) = db_terraform_data.vpc_sn_a_id {
        vpc_subnet_map.insert("sn-a".to_string(), subnet_id);
    }
    if let Some(subnet_id) = db_terraform_data.vpc_sn_b_id {
        vpc_subnet_map.insert("sn-b".to_string(), subnet_id);
    }
    if let Some(subnet_id) = db_terraform_data.vpc_sn_c_id {
        vpc_subnet_map.insert("sn-c".to_string(), subnet_id);
    }

    let vpn_cidr_block = match cidr_block {
        IpNetwork::V4(v4_network) => {
            let new_address = std::net::Ipv4Addr::new(
                v4_network.ip().octets()[0],
                v4_network.ip().octets()[1],
                252,
                0,
            );

            ipnetwork::IpNetwork::V4(
                ipnetwork::Ipv4Network::new(new_address, 22)
                    .change_context(ErrorStack::NewIpv4Network)?,
            )
        }
        IpNetwork::V6(_) => {
            error_stack::bail!(ErrorStack::InvalidSubnet(cidr_block));
        }
    };

    let vpn_client_cidr_block = match cidr_block {
        IpNetwork::V4(v4_network) => {
            let new_address = std::net::Ipv4Addr::new(10, 252, v4_network.ip().octets()[1], 0);

            ipnetwork::IpNetwork::V4(
                ipnetwork::Ipv4Network::new(new_address, 24)
                    .change_context(ErrorStack::NewIpv4Network)?,
            )
        }
        IpNetwork::V6(_) => {
            error_stack::bail!(ErrorStack::InvalidSubnet(cidr_block));
        }
    };

    let create_nat_gateways = etc_config.create_nat_gateways.unwrap_or(false);
    let ace_instance_volume_size = etc_config.ace_instance_volume_size.unwrap_or(100);
    let ace_instance_allow_ssh_from = etc_config
        .ace_instance_allow_ssh_from
        .clone()
        .unwrap_or(vec![]);

    let ace_hostname = format!("ace.{private_subdomain_name}");
    let hostname = hostname::get()
        .change_context(ErrorStack::GetHostname)?
        .to_string_lossy()
        .into_owned();
    let is_ace_instance = hostname == ace_hostname;

    let ace2_private_key_file_path = app.secure_mountpoint.join("ace2_key");
    let ace2_public_key_file_path = app.data_path.join("ace2_key.pub");

    let ace2_server_name = format!("ace.{}", &private_subdomain_name);
    let ace2_server_name_prefix = "ace".to_string();

    let graylog = etc_config.graylog.as_ref().map(|graylog| Graylog {
        server_name: format!("graylog.{}", &private_subdomain_name),
        instance_type: graylog.instance_type.clone(),
        volume_size: graylog.volume_size,
    });

    let vpn = etc_config.vpn.as_ref().map(|vpn| Vpn {
        public_server_name: format!("vpn.{}", &public_subdomain_name),
        private_server_name: format!("vpn.{}", &private_subdomain_name),
        instance_type: vpn.instance_type.clone(),
        volume_size: vpn.volume_size,
        additional_routed_subnets: vpn.additional_routed_subnets.clone(),
    });

    let users = crate::user::select(&crate::UserFilter::All, app)
        .await
        .change_context(ErrorStack::SelectUsers)?
        .iter()
        .map(|v| v.graphkey.clone())
        .collect();

    let config = Config {
        account_key_at_region,
        account_key: account_key.to_string(),
        account,
        ace_hostname,
        ace_instance_allow_ssh_from,
        ace_instance_volume_size,
        ace2_private_key_file_path,
        ace2_public_key_file_path,
        ace2_server_name_prefix,
        ace2_server_name,
        agent_version: etc_config.agent_version.clone(),
        aws_account_id,
        create_nat_gateways,
        ecr_registry,
        graylog,
        hosted_zone_map,
        is_ace_instance,
        my_ip,
        my_ipnetwork,
        private_domain_name,
        private_subdomain_name,
        public_domain_name,
        public_subdomain_name,
        region_at_account_key,
        region,
        cidr_block,
        users,
        vpc_public_subnet_map: vpc_subnet_map,
        vpn_client_cidr_block,
        vpn_cidr_block,
        vpn,
        zerossl_key,
    };

    Ok(config)
}
