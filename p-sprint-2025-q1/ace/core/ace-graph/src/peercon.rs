use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    CurrentRegionStructNotFound,
    DbError,
    GetCurrentAccount,
    GetCurrentAccountAndRegionKeys,
    GetPeerAccount(String),
    GetPeercon(crate::Peercon),
    LookupLocal,
    NonLocalPeercon,
    NotOneFound(crate::Peercon),
    RegionNotFound(String),
    VPCIDNotDefinedForCurrentRegion,
    VPCIDNotFound,
}

#[derive(Debug, Clone)]
pub struct LocalRemotePeerCon {
    pub graphkey: crate::Peercon,
    pub local: LocalPeeringInfo,
    pub remote: VpcInfo,
    pub peering_connection_id: String,
}

impl crate::GraphValueExt for LocalRemotePeerCon {}

#[derive(Debug, Clone)]
pub struct VpcInfo {
    pub account_key: String,
    pub region: String,
    pub vpc_id: String,
    pub aws_account_id: String,
    pub cidr_block: ipnetwork::IpNetwork,
}

impl crate::GraphValueExt for VpcInfo {}

#[derive(Debug, Clone)]
pub enum LocalPeeringInfo {
    VpcInfo(VpcInfo),
    Error(String),
}

impl crate::GraphValueExt for LocalPeeringInfo {}

pub async fn get(
    gk_peercon: &crate::Peercon,
    app: &ace_db::App,
) -> error_stack::Result<LocalRemotePeerCon, ErrorStack> {
    let filter = crate::PeerconFilter::One(gk_peercon.clone());
    let mut peercons = select_result(&filter, app).await?;

    // Validate that one record was returned
    let peercon: Result<LocalRemotePeerCon, error_stack::Report<ErrorStack>> = {
        // if length != 1, return error
        if peercons.len() == 1 {
            peercons.pop().unwrap()
        } else {
            error_stack::bail!(ErrorStack::NotOneFound(gk_peercon.clone()))
        }
    };

    peercon.change_context_lazy(|| ErrorStack::GetPeercon(gk_peercon.to_owned()))
}

pub async fn select(
    filter: &crate::PeerconFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<LocalRemotePeerCon>, ErrorStack> {
    // Fails at ANY error found
    let mut rval = Vec::new();

    for peercon in select_result(filter, app).await? {
        match peercon {
            Ok(peercon) => {
                rval.push(peercon);
            }
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    filter: &crate::PeerconFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<LocalRemotePeerCon, ErrorStack>>, ErrorStack> {
    let db_filter = match filter {
        crate::PeerconFilter::All => ace_db::Filter::All,
        crate::PeerconFilter::One(crate::Peercon::Db(name)) => ace_db::Filter::One(name.to_owned()),
        crate::PeerconFilter::None => return Ok(Vec::new()), // exit early
    };

    let mut rval = Vec::new();

    let local_info = lookup_local(app)
        .await
        .change_context(ErrorStack::LookupLocal)?;

    let local_vpc_info = match &local_info {
        LocalPeeringInfo::Error(_e) => {
            eprintln!("Warning: VPC ID not defined for current region...");
            return Ok(rval);
        }
        LocalPeeringInfo::VpcInfo(vpc_info) => vpc_info,
    };

    let peercons = ace_db::etc::account::peercon::select(db_filter, app).await;

    for peercon in peercons {
        match peercon {
            Ok(peercon) => {
                match construct(local_vpc_info, peercon, &local_info, app).await {
                    Ok(Some(peercon)) => {
                        rval.push(Ok(peercon));
                    }
                    Ok(None) => continue,
                    Err(e) => {
                        rval.push(Err(e));
                    }
                };
            }
            Err(e) => {
                rval.push(Err(e.change_context(ErrorStack::DbError)));
            }
        }
    }

    Ok(rval)
}

async fn construct(
    local_vpc_info: &VpcInfo,
    peercon: ace_db::etc::account::peercon::SerdePeeringConnection,
    local_info: &LocalPeeringInfo,
    app: &ace_db::App,
) -> error_stack::Result<Option<LocalRemotePeerCon>, ErrorStack> {
    // Either find a match in [peer1,peer2] and use the opposite one, or it's not applicable, so return None
    let peer = {
        if peercon.peer1.account_key == local_vpc_info.account_key
            && peercon.peer1.region == local_vpc_info.region
        {
            &peercon.peer2
        } else if peercon.peer2.account_key == local_vpc_info.account_key
            && peercon.peer2.region == local_vpc_info.region
        {
            &peercon.peer1
        } else {
            return Ok(None);
        }
    };

    // Validate the account referenced by the peer
    let peer_account_struct =
        crate::account::get(&crate::Account::Db(peer.account_key.clone()), app)
            .await
            .change_context_lazy(|| ErrorStack::GetPeerAccount(peer.account_key.clone()))?;

    let Some(peer_region_struct) = peer_account_struct.region.get(&peer.region) else {
        error_stack::bail!(ErrorStack::RegionNotFound(peer.region.clone()));
    };

    let Some(ref vpc_id) = peer_region_struct.vpc_id else {
        error_stack::bail!(ErrorStack::VPCIDNotFound);
    };

    Ok(Some(LocalRemotePeerCon {
        graphkey: crate::Peercon::Db(peercon.peering_connection_id.clone()),
        local: local_info.clone(),
        remote: VpcInfo {
            account_key: peer.account_key.clone(),
            region: peer.region.clone(),
            vpc_id: vpc_id.clone(),
            aws_account_id: peer_account_struct.aws_account_id.clone(),
            cidr_block: peer_region_struct.cidr_block,
        },
        peering_connection_id: peercon.peering_connection_id,
    }))
}

pub async fn lookup_local(app: &ace_db::App) -> error_stack::Result<LocalPeeringInfo, ErrorStack> {
    // get config so we can get the current account and region
    let config = crate::config::get(&crate::Config::EtcConfig, app)
        .await
        .change_context(ErrorStack::GetCurrentAccountAndRegionKeys)?;

    let current_account_key = config.account_key;
    let current_region_key = config.region;

    // get account so we can get the current region struct
    let current_account = crate::account::get(&crate::Account::Db(current_account_key), app)
        .await
        .change_context(ErrorStack::GetCurrentAccount)?;

    // get region struct
    let current_region = current_account
        .region
        .get(&current_region_key)
        .ok_or(ErrorStack::CurrentRegionStructNotFound)?;

    let local_info = match current_region.vpc_id {
        Some(ref vpc_id) => LocalPeeringInfo::VpcInfo(VpcInfo {
            account_key: current_account.account_key.to_string(),
            region: current_region.name.to_string(),
            vpc_id: vpc_id.clone(),
            aws_account_id: current_account.aws_account_id.clone(),
            cidr_block: current_region.cidr_block,
        }),
        None => {
            LocalPeeringInfo::Error("Warning: VPC ID not defined for current region...".to_string())
        }
    };

    Ok(local_info)
}
