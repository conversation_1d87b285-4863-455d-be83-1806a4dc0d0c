#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    NotOneEipFound(crate::ElasticIp, usize),
}

#[derive(Debug)]
pub struct ElasticIp {
    pub graphkey: crate::ElasticIp,
    pub name: String,
    pub domain: String,
}

impl crate::GraphValueExt for ElasticIp {}

pub async fn get(gk_eip: crate::ElasticIp) -> error_stack::Result<ElasticIp, ErrorStack> {
    let filter = crate::EipFilter::One(gk_eip.clone());

    let mut eips = select(filter).await?;
    if eips.len() == 1 {
        return Ok(eips.pop().unwrap());
    }

    error_stack::bail!(ErrorStack::NotOneEipFound(gk_eip, eips.len()));
}

pub async fn select(filter: crate::EipFilter) -> error_stack::Result<Vec<ElasticIp>, ErrorStack> {
    let mut rval = Vec::new();

    for eip in select_result(filter).await? {
        match eip {
            Ok(eip) => rval.push(eip),
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    _filter: crate::EipFilter,
) -> error_stack::Result<Vec<error_stack::Result<ElasticIp, ErrorStack>>, ErrorStack> {
    Ok(vec![])
}
