use crate::{Graph<PERSON>eyExt, GraphKeyName};
use error_stack::ResultExt;
use indexmap::IndexMap;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    CurrentRegionStructNotFound,
    DbError,
    DeserializeAmi(String),
    GetAppFromDB,
    GetAppTlscert(crate::App),
    GetConfig,
    GetCurrentAccount,
    NotOneFound(crate::App, usize),
    SelectApps,
}

#[derive(Debug)]
pub struct App {
    pub graphkey: crate::App,
    pub name: String,
    pub hosting: AppHosting,
    pub conf: toml::value::Table,
    pub conf_live: toml::value::Table,
    pub conf_test: toml::value::Table,
    pub db_bucket_map: IndexMap<String, AppBucket>,
}

impl crate::GraphValueExt for App {}

impl GraphKeyName for crate::App {
    fn get_name(&self) -> String {
        match self {
            crate::App::Db(name) => name.clone(),
        }
    }
}

#[derive(Debug)]
pub enum AppHosting {
    None,
    Instance {
        ami: crate::Ami,
        subnet: String,
        instance_type: String,
        ingress: Vec<crate::ins::InstanceIngress>,
        use_elastic_ip: bool,
        volume_size: crate::ins::VolumeSize,
    },
}

#[derive(Debug, Clone)]
pub struct AppBucket {
    pub suffix: String,
    pub bucket_name: String,
    pub public_read: Vec<String>,
    pub developer_access: bool,
}

impl crate::GraphValueExt for AppBucket {}

pub type AppList = Vec<App>;

pub async fn get(app_name: &str, app: &ace_db::App) -> error_stack::Result<App, ErrorStack> {
    let gk_app = crate::App::Db(app_name.to_string());
    let filter = crate::AppFilter::One(gk_app.clone());
    let mut apps = select_result(&filter, app).await?;

    // Validate that one record was returned
    let app = {
        if apps.len() == 1 {
            apps.pop().unwrap()
        } else {
            error_stack::bail!(ErrorStack::NotOneFound(gk_app, apps.len()))
        }
    };

    app.change_context_lazy(|| ErrorStack::GetAppFromDB)
}

pub async fn select(
    filter: &crate::AppFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<App>, ErrorStack> {
    // Fails at ANY error found (but not necessarily at "hey didn't find a match"?).
    let mut rval = Vec::new();

    for app_result in select_result(filter, app).await? {
        match app_result {
            Ok(app) => {
                rval.push(app);
            }
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    filter: &crate::AppFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<App, ErrorStack>>, ErrorStack> {
    let db_filter = match filter {
        crate::AppFilter::All => ace_db::Filter::All,
        crate::AppFilter::One(crate::App::Db(name)) => ace_db::Filter::One(name.as_ref()),
        crate::AppFilter::None => return Ok(Vec::new()), // exit early
    };

    let mut rval = Vec::new();

    let (current_account_key, current_region_key) = {
        let config = crate::config::get(&crate::Config::EtcConfig, app)
            .await
            .change_context(ErrorStack::GetConfig)?;

        (config.account_key, config.region)
    };

    let current_account = crate::account::get(&crate::Account::Db(current_account_key), app)
        .await
        .change_context(ErrorStack::GetCurrentAccount)?;

    let Some(current_region) = current_account.region.get(&current_region_key) else {
        error_stack::bail!(ErrorStack::CurrentRegionStructNotFound);
    };

    let apps = ace_db::etc::app::select(db_filter, &app.etc_path)
        .await
        .change_context(ErrorStack::DbError)?;

    for app in apps {
        match app {
            Ok(a) => {
                rval.push(construct(a, &current_account, current_region));
            }
            Err(e) => {
                rval.push(Err(e.change_context(ErrorStack::DbError)));
            }
        }
    }

    Ok(rval)
}

fn construct(
    app: ace_db::etc::app::App,
    account: &crate::account::Account,
    region: &crate::account::AccountRegion,
) -> error_stack::Result<App, ErrorStack> {
    let mut bucket_map = IndexMap::new();
    for (key, ace_db_bucket) in &app.bucket {
        let suffix = key.to_owned();
        let bucket_name = format!(
            "{}-{}-app-{}-{}",
            account.account_key, region.name, app.name, suffix
        );
        bucket_map.insert(
            suffix.clone(),
            AppBucket {
                suffix,
                bucket_name,
                public_read: ace_db_bucket.public_read.clone(),
                developer_access: ace_db_bucket.developer_access,
            },
        );
    }

    let hosting = match app.hosting {
        ace_db::etc::app::AppHosting::None => AppHosting::None,
        ace_db::etc::app::AppHosting::Instance {
            ami,
            subnet,
            instance_type,
            ingress,
            use_elastic_ip,
            volume_size,
        } => {
            // TODO: move the PortRange type to a common ace types module and use it consistently

            let ami = match crate::Ami::deserialize(&ami) {
                Ok(ami) => ami,
                Err(e) => {
                    error_stack::bail!(ErrorStack::DeserializeAmi(e));
                }
            };

            AppHosting::Instance {
                ami,
                subnet,
                instance_type,
                ingress: ingress
                    .into_iter()
                    .map(|i| crate::ins::InstanceIngress {
                        ports: match i.ports {
                            ace_db::etc::instance::PortRange::One(port) => {
                                crate::ins::PortRange::One(port)
                            }
                            ace_db::etc::instance::PortRange::Range(from, to) => {
                                crate::ins::PortRange::Range(from, to)
                            }
                            ace_db::etc::instance::PortRange::All => crate::ins::PortRange::All,
                        },
                        cidr_blocks: i.cidr_blocks,
                        protocol: i.protocol,
                    })
                    .collect(),
                use_elastic_ip,
                volume_size: crate::ins::VolumeSize::Specific(volume_size),
            }
        }
    };

    Ok(App {
        graphkey: crate::App::Db(app.name.clone()),
        name: app.name,
        hosting,
        conf: app.conf,
        conf_live: app.conf_live,
        conf_test: app.conf_test,
        db_bucket_map: bucket_map,
    })
}

pub async fn select_result_bucket(
    filter: &crate::BucketFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<crate::bucket::Bucket, ErrorStack>>, ErrorStack> {
    let mut rval = Vec::new();

    for app_result in select_result(&filter.into(), app).await? {
        match app_result {
            Ok(app) => {
                for app_bucket in app.db_bucket_map.values() {
                    let bucket = crate::bucket::Bucket {
                        // Suffix?  Purpose?
                        graphkey: crate::Bucket::App(
                            app.graphkey.clone(),
                            app_bucket.suffix.clone(),
                        ),
                        name: app_bucket.bucket_name.clone(),
                        developer_access: app_bucket.developer_access,
                        public_read: app_bucket.public_read.clone(),
                        suffix: app_bucket.suffix.clone(),
                    };

                    rval.push(Ok(bucket));
                }
            }
            Err(e) => {
                rval.push(Err(e));
            }
        }
    }

    Ok(rval)
}

pub async fn select_result_instance(
    filter: &crate::InstanceFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<crate::ins::Instance, ErrorStack>>, ErrorStack> {
    let mut rval = Vec::new();

    let app_filter: crate::AppFilter = filter.into();

    for app in select_result(&app_filter, app).await? {
        match app {
            Ok(App {
                hosting: AppHosting::None,
                ..
            }) => {
                continue;
            }
            Ok(App {
                name,
                graphkey,
                hosting:
                    AppHosting::Instance {
                        ami,
                        subnet,
                        instance_type,
                        ingress,
                        use_elastic_ip,
                        volume_size,
                    },
                ..
            }) => {
                let instance = crate::ins::Instance {
                    graphkey: crate::Instance::App(graphkey.clone()),
                    ami_graphkey: ami,
                    name: format!("{}.app", name),
                    subnet,
                    instance_type,
                    volume_size,
                    ingress,
                    use_elastic_ip,
                };

                rval.push(Ok(instance));
            }
            Err(e) => {
                rval.push(Err(e));
            }
        }
    }

    Ok(rval)
}

pub async fn select_result_securitygroup(
    filter: &crate::SecurityGroupFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<crate::sg::SecurityGroup, ErrorStack>>, ErrorStack>
{
    let mut rval = Vec::new();

    let filter: crate::AppFilter = filter.into();

    for app in select_result(&filter, app)
        .await
        .change_context(ErrorStack::SelectApps)?
    {
        match app {
            Ok(app) => {
                let securitygroup = crate::sg::SecurityGroup {
                    graphkey: crate::SecurityGroup::App(app.graphkey.clone()),
                    name: format!("app-{}", app.name),
                };

                rval.push(Ok(securitygroup));
            }
            Err(e) => {
                rval.push(Err(e));
            }
        }
    }

    Ok(rval)
}

// Lists tls certs for all filtered apps
// TODO: Refactor due to changes in return value of list()
pub async fn select_result_tlscert(
    filter: &crate::TlsCertFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<crate::tlscert::TlsCert, ErrorStack>>, ErrorStack>
{
    let mut rval = Vec::new();

    let config = crate::config::get(&crate::Config::EtcConfig, app)
        .await
        .change_context(ErrorStack::GetConfig)?;

    // Iterate over list of apps - filter happens in list()
    for app_result in select_result(&filter.into(), app).await? {
        match app_result {
            Ok(a) => {
                let gk_priv = crate::TlsCert::App(a.graphkey.clone(), "pvt".to_string());
                let gk_pub = crate::TlsCert::App(a.graphkey.clone(), "pub".to_string());

                let common_name = format!("{}.app.{}", a.name, config.public_subdomain_name);

                match crate::tlscert::read_expiry(&gk_priv, app).await {
                    Ok(expiration_time) => {
                        rval.push(Ok(crate::tlscert::TlsCert {
                            graphkey: gk_priv,
                            common_name: common_name.clone(),
                            expiration_time,
                            subject_alternative_names: vec![common_name.clone()],
                        }));
                    }
                    Err(e) => {
                        rval.push(Err(
                            e.change_context(ErrorStack::GetAppTlscert(a.graphkey.clone()))
                        ));
                    }
                }

                match crate::tlscert::read_expiry(&gk_pub, app).await {
                    Ok(expiration_time) => {
                        rval.push(Ok(crate::tlscert::TlsCert {
                            graphkey: gk_pub,
                            common_name: common_name.clone(),
                            expiration_time,
                            subject_alternative_names: vec![common_name],
                        }));
                    }
                    Err(e) => {
                        rval.push(Err(e.change_context(ErrorStack::GetAppTlscert(a.graphkey))));
                    }
                }
            }
            Err(e) => {
                rval.push(Err(e));
            }
        }

        // only process appserver-private if it has been asked for
        // ...
    }

    Ok(rval)
}
