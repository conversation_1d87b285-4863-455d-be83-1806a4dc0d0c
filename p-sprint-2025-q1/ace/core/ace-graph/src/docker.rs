use std::path::Path;

use error_stack::ResultExt;

use crate::GraphKeyExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    DbError,
    GetConfig,
    Get<PERSON><PERSON><PERSON>A<PERSON>unt,
    GetDocker(crate::Docker),
    NotOneFound(crate::Docker, usize),
    SelectDockers,
    SelectDockersFromDb,
}

#[derive(Debug)]
pub struct Docker {
    pub graphkey: crate::Docker,
    pub name: String,
    pub path: std::path::PathBuf,
    pub deploy: Deploy,
}

impl crate::GraphValueExt for Docker {}

#[derive(Debug)]
pub enum Deploy {
    Ecr,
    EcrPublic,
}

impl Docker {
    /// Generates the tag name for the `Docker` image based on the deploy type.
    pub async fn get_tag_name(&self, app: &ace_db::App) -> error_stack::Result<String, ErrorStack> {
        // Get the current AWS account ID and region
        let config = crate::config::get(&crate::Config::EtcConfig, app)
            .await
            .change_context(ErrorStack::GetConfig)?;

        let current_account = crate::account::get(&crate::Account::Db(config.account_key), app)
            .await
            .change_context(ErrorStack::GetCurrentAccount)?;

        match self.deploy {
            Deploy::Ecr => {
                let ecr_registry = format!(
                    "{}.dkr.ecr.{}.amazonaws.com",
                    current_account.aws_account_id, config.region
                );

                // Generate ecr graphkey: (based off self)
                let ecr_gk = crate::Ecr::Docker(self.graphkey.clone());
                let tag_name = format!("{ecr_registry}/{}", ecr_gk.serialize_dashed());

                Ok(tag_name)
            }
            Deploy::EcrPublic => {
                let tag_name = format!(
                    "public.ecr.aws/{}/{}",
                    current_account.aws_account_id, self.name
                );

                Ok(tag_name)
            }
        }
    }
}

pub async fn get(
    gk_docker: &crate::Docker,
    app: &ace_db::App,
) -> error_stack::Result<Docker, ErrorStack> {
    let filter = crate::DockerFilter::One(gk_docker.clone());
    let mut dockers = select_result(&filter, app)
        .await
        .change_context_lazy(|| ErrorStack::GetDocker(gk_docker.to_owned()))?;

    // Validate that one record was returned
    let docker: Result<Docker, error_stack::Report<ErrorStack>> = {
        if dockers.len() == 1 {
            dockers.pop().unwrap()
        } else {
            error_stack::bail!(ErrorStack::NotOneFound(gk_docker.clone(), dockers.len()))
        }
    };

    docker.change_context_lazy(|| ErrorStack::GetDocker(gk_docker.to_owned()))
}

pub async fn select(
    filter: &crate::DockerFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<Docker>, ErrorStack> {
    // Fails at ANY error found
    let mut rval = Vec::new();

    for docker in select_result(filter, app).await? {
        match docker {
            Ok(d) => {
                rval.push(d);
            }
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    filter: &crate::DockerFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<Docker, ErrorStack>>, ErrorStack> {
    let db_filter = match filter {
        crate::DockerFilter::All => ace_db::Filter::All,
        crate::DockerFilter::One(crate::Docker::Db(name)) => ace_db::Filter::One(name.to_owned()),
        crate::DockerFilter::None => return Ok(Vec::new()), // exit early
    };
    let mut rval = Vec::new();

    let dockers = ace_db::etc::docker::select(db_filter, &app.etc_path)
        .await
        .change_context(ErrorStack::SelectDockersFromDb)?;

    for docker in dockers {
        match docker {
            Ok(docker) => {
                rval.push(Ok(construct(docker, &app.path)));
            }
            Err(e) => {
                rval.push(Err(e.change_context(ErrorStack::DbError)));
            }
        }
    }

    Ok(rval)
}

fn construct(docker: ace_db::etc::docker::Docker, ace_db_app_path: &Path) -> Docker {
    let path = ace_db_app_path.join(&docker.name);

    let deploy = match docker.deploy {
        ace_db::etc::docker::Deploy::Ecr => Deploy::Ecr,
        ace_db::etc::docker::Deploy::EcrPublic => Deploy::EcrPublic,
    };

    Docker {
        graphkey: crate::Docker::Db(docker.name.clone()),
        name: docker.name,
        path,
        deploy,
    }
}

pub async fn select_result_ecr(
    filter: &crate::EcrFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<crate::ecr::Ecr, ErrorStack>>, ErrorStack> {
    let mut rval = Vec::new();

    let docker_filter: crate::DockerFilter = filter.into();

    let dockers = select_result(&docker_filter, app)
        .await
        .change_context(ErrorStack::SelectDockers)?;

    // Get config/account info for construction:
    let config = crate::config::get(&crate::Config::EtcConfig, app)
        .await
        .change_context(ErrorStack::GetConfig)?;
    let account = crate::account::get(&crate::Account::Db(config.account_key), app)
        .await
        .change_context(ErrorStack::GetCurrentAccount)?;

    for docker in dockers {
        match docker {
            Ok(docker) => {
                let ecr_name = docker.name;

                if matches!(docker.deploy, Deploy::Ecr) {
                    // let arn = format!("arn:aws:ecr:{region}:{account_id}:repository/{graphkey}");
                    let arn = format!(
                        "arn:aws:ecr:{}:{}:repository/{}",
                        config.region,
                        account.aws_account_id,
                        docker.graphkey.serialize_dashed()
                    );

                    rval.push(Ok(crate::ecr::Ecr {
                        graphkey: crate::Ecr::Docker(docker.graphkey),
                        name: ecr_name,
                        image_tag_mutability: "MUTABLE".to_string(),
                        scan_on_push: true,
                        arn,
                    }));
                    continue;
                }
            }
            Err(e) => {
                rval.push(Err(e));
            }
        }
    }

    Ok(rval)
}

pub async fn select_result_ecr_public(
    filter: &crate::EcrPublicFilter,
    app: &ace_db::App,
) -> error_stack::Result<
    Vec<error_stack::Result<crate::ecr_public::EcrPublic, ErrorStack>>,
    ErrorStack,
> {
    let mut rval = Vec::new();

    let docker_filter: crate::DockerFilter = filter.into();

    let dockers = select_result(&docker_filter, app)
        .await
        .change_context(ErrorStack::SelectDockers)?;

    // Get config/account info for construction:
    let config = crate::config::get(&crate::Config::EtcConfig, app)
        .await
        .change_context(ErrorStack::GetConfig)?;
    let account = crate::account::get(&crate::Account::Db(config.account_key), app)
        .await
        .change_context(ErrorStack::GetCurrentAccount)?;

    for docker in dockers {
        match docker {
            Ok(docker) => {
                let ecr_name = docker.name;

                let arn = format!(
                    "arn:aws:ecr-public::{}:repository/{}",
                    account.aws_account_id, ecr_name
                );

                if matches!(docker.deploy, Deploy::EcrPublic) {
                    rval.push(Ok(crate::ecr_public::EcrPublic {
                        graphkey: crate::EcrPublic::Docker(docker.graphkey),
                        repository_name: ecr_name,
                        arn,
                    }));
                    continue;
                }
            }
            Err(e) => {
                rval.push(Err(e));
            }
        }
    }

    Ok(rval)
}
