use error_stack::ResultExt;
use ipnetwork::IpNetwork;
use std::collections::HashMap;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    DbError,
    GetAccount(crate::Account),
    GetUserFromDb,
    NotOneFound(crate::Account),
    SelectFromDb,
}

pub type AccountKey = String;
pub type AwsAccountId = String;

#[derive(Debug, Clone)]
pub struct Account {
    pub graphkey: crate::Account,
    pub account_key: String,
    pub aws_account_id: AwsAccountId,
    pub public_domain: String,
    pub private_domain: String,
    pub sysadmins: Vec<String>,
    pub region: HashMap<String, AccountRegion>,
}

impl Account {
    pub async fn get_ssh_keys_text(
        &self,
        app: &ace_db::App,
    ) -> error_stack::Result<String, ErrorStack> {
        let mut rval = String::new();

        for sysadmin in &self.sysadmins {
            let user = crate::user::get(&crate::User::Db(sysadmin.to_owned()), app)
                .await
                .change_context(ErrorStack::GetUserFromDb)?;

            for ssh_key in &user.ssh_keys {
                rval.push_str(ssh_key);
                rval.push('\n');
            }
        }

        Ok(rval)
    }
}

impl crate::GraphValueExt for Account {}

#[derive(Debug, Clone)]
pub struct AccountRegion {
    pub name: String,
    pub vpc_id: Option<String>,
    pub cidr_block: IpNetwork,
    pub ace2: bool,
}

impl crate::GraphValueExt for AccountRegion {}

pub async fn get(
    gk_account: &crate::Account,
    app: &ace_db::App,
) -> error_stack::Result<Account, ErrorStack> {
    let filter = crate::AccountFilter::One(gk_account.clone());
    let mut accounts = select_result(&filter, app).await?;

    // Validate that one record was returned
    let account: Result<Account, error_stack::Report<ErrorStack>> = {
        // if length != 1, return error
        if accounts.len() == 1 {
            accounts.pop().unwrap()
        } else {
            error_stack::bail!(ErrorStack::NotOneFound(gk_account.clone()))
        }
    };

    account.change_context_lazy(|| ErrorStack::GetAccount(gk_account.to_owned()))
}

pub async fn select(
    filter: &crate::AccountFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<Account>, ErrorStack> {
    // Fails at ANY error found (but not necessarily at "hey didn't find a match"?).
    let mut rval = Vec::new();

    let accounts = select_result(filter, app).await?;

    for account in accounts {
        match account {
            Ok(account) => {
                rval.push(account);
            }
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    filter: &crate::AccountFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<Account, ErrorStack>>, ErrorStack> {
    let db_filter = match filter {
        crate::AccountFilter::All => ace_db::Filter::All,
        crate::AccountFilter::One(crate::Account::Db(name)) => ace_db::Filter::One(name.to_owned()),
        crate::AccountFilter::None => return Ok(Vec::new()),
    };

    let mut rval = Vec::new();

    let accounts = ace_db::etc::account::account::select(db_filter, &app.etc_account_file_path)
        .await
        .change_context(ErrorStack::SelectFromDb)?;

    for account in accounts {
        match account {
            Ok(a) => {
                rval.push(Ok(construct(a)));
            }
            Err(e) => {
                rval.push(Err(e.change_context(ErrorStack::DbError)));
            }
        }
    }

    Ok(rval)
}

fn construct(account: ace_db::etc::account::account::Account) -> Account {
    let mut region_map = HashMap::new();
    for (region_name, region) in account.region {
        region_map.insert(
            region_name.clone(),
            AccountRegion {
                name: region_name,
                vpc_id: region.vpc_id,
                cidr_block: region.cidr_block,
                ace2: region.ace2,
            },
        );
    }

    Account {
        graphkey: crate::Account::Db(account.account_key.clone()),
        account_key: account.account_key,
        aws_account_id: account.aws_account_id,
        public_domain: account.public_domain,
        private_domain: account.private_domain,
        sysadmins: account.sysadmins,
        region: region_map,
    }
}
