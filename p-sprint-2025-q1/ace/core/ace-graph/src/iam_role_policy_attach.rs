#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    NotOneIamRolePolAttachFound(crate::IamRolePolicyAttach, usize),
}

#[derive(Debug)]
pub struct IamRolePolicyAttach {
    pub graphkey: crate::IamRolePolicyAttach,
    pub name: String,
}

impl crate::GraphValueExt for IamRolePolicyAttach {}

pub async fn get(
    gk_iam_r_pol_att: crate::IamRolePolicyAttach,
) -> error_stack::Result<IamRolePolicyAttach, ErrorStack> {
    let filter = crate::IamRolePolicyAttachFilter::One(gk_iam_r_pol_att.clone());

    let mut atts = select(filter).await?;
    if atts.len() == 1 {
        return Ok(atts.pop().unwrap());
    }

    error_stack::bail!(ErrorStack::NotOneIamRolePolAttachFound(
        gk_iam_r_pol_att,
        atts.len()
    ));
}

pub async fn select(
    filter: crate::IamRolePolicyAttachFilter,
) -> error_stack::Result<Vec<IamRolePolicyAttach>, ErrorStack> {
    let mut rval = Vec::new();

    for iam_role_pol_atts in select_result(filter).await? {
        match iam_role_pol_atts {
            Ok(a) => rval.push(a),
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    _filter: crate::IamRolePolicyAttachFilter,
) -> error_stack::Result<Vec<error_stack::Result<IamRolePolicyAttach, ErrorStack>>, ErrorStack> {
    Ok(vec![])
}
