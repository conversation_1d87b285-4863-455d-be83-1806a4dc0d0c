#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    NotOneIamAccessKeyFound(crate::IamAccess<PERSON>ey, usize),
}

#[derive(Debug)]
pub struct IamAccessKey {
    pub graphkey: crate::<PERSON>am<PERSON><PERSON><PERSON><PERSON><PERSON>,
    pub name: String,
}

impl crate::GraphValueExt for IamAccessKey {}

pub async fn get(
    gk_iam_acc_key: crate::IamAccessKey,
) -> error_stack::Result<IamAccessKey, ErrorStack> {
    let filter = crate::IamAccessKeyFilter::One(gk_iam_acc_key.clone());

    let mut access_keys = select(filter).await?;
    if access_keys.len() == 1 {
        return Ok(access_keys.pop().unwrap());
    }

    error_stack::bail!(ErrorStack::NotOneIamAccessKeyFound(
        gk_iam_acc_key,
        access_keys.len()
    ));
}

pub async fn select(
    filter: crate::IamAccessKeyFilter,
) -> error_stack::Result<Vec<IamAccessKey>, ErrorStack> {
    let mut rval = Vec::new();

    for iam_acc_keys in select_result(filter).await? {
        match iam_acc_keys {
            Ok(iamk) => rval.push(iamk),
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    _filter: crate::IamAccessKeyFilter,
) -> error_stack::Result<Vec<error_stack::Result<IamAccessKey, ErrorStack>>, ErrorStack> {
    Ok(vec![])
}
