#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    NotOneIamRoleFound(crate::IamRole, usize),
}

#[derive(Debug)]
pub struct IamRole {
    pub graphkey: crate::IamRole,
    pub name: String,
    pub assume_role_policy: String, //?
}

impl crate::GraphValueExt for IamRole {}

pub async fn get(gk_eip: crate::IamRole) -> error_stack::Result<IamRole, ErrorStack> {
    let filter = crate::IamRoleFilter::One(gk_eip.clone());

    let mut iam_roles = select(filter).await?;
    if iam_roles.len() == 1 {
        return Ok(iam_roles.pop().unwrap());
    }

    error_stack::bail!(ErrorStack::NotOneIamRoleFound(gk_eip, iam_roles.len()));
}

pub async fn select(filter: crate::IamRoleFilter) -> error_stack::Result<Vec<IamRole>, ErrorStack> {
    let mut rval = Vec::new();

    for iam_role in select_result(filter).await? {
        match iam_role {
            Ok(role) => rval.push(role),
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    _filter: crate::IamRoleFilter,
) -> error_stack::Result<Vec<error_stack::Result<IamRole, ErrorStack>>, ErrorStack> {
    Ok(vec![])
}
