use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    DbError,
    GetCurrentEnvInfo,
    NotOneAmiFound(crate::Ami, usize),
    UnknownPacker(String),
    ExtractAmiIdFromPackerArtifactId(String),
}

#[derive(Debug)]
pub struct Ami {
    pub graphkey: crate::Ami,
    pub account_key: String,
    pub name: String,
    pub region: String,
    pub ami_id: String,
    pub build_timestamp: Option<u64>,
}

impl crate::GraphValueExt for Ami {}

pub async fn get(gk_ami: crate::Ami, app: &ace_db::App) -> error_stack::Result<Ami, ErrorStack> {
    let filter = crate::AmiFilter::One(gk_ami.clone());

    let mut amis = select(filter, app).await?;
    if amis.len() == 1 {
        return Ok(amis.pop().unwrap());
    }
    error_stack::bail!(ErrorStack::NotOneAmiFound(gk_ami, amis.len()))
}

/// Passes filter on to `select_result()` and processes the results.
/// Bails at any error found.
pub async fn select(
    filter: crate::AmiFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<Ami>, ErrorStack> {
    let mut rval = vec![];

    // May seem redundant now, but may not be later when ami's can exist on disk?
    for ami_result in select_result(filter, app).await? {
        match ami_result {
            Ok(ami) => rval.push(ami),
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

#[allow(clippy::too_many_lines)]
/// This function only talks to `ace_db::etc::ami` and `ace_db::data::packer_manifest`
pub async fn select_result(
    filter: crate::AmiFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<Ami, ErrorStack>>, ErrorStack> {
    let (etc_filter, manifest_filter) = match filter {
        crate::AmiFilter::All => (
            ace_db::Filter::All,
            vec![
                ace_db::PackerManifestFilter::All,
                ace_db::PackerManifestFilter::LatestEachByName,
            ],
        ),
        crate::AmiFilter::AllLatest => (
            ace_db::Filter::All,
            vec![ace_db::PackerManifestFilter::LatestEachByName],
        ),
        crate::AmiFilter::One(gk_ami) => {
            match gk_ami {
                crate::Ami::Packer(gk_packer) => {
                    // Implies latest
                    (
                        ace_db::Filter::None,
                        vec![ace_db::PackerManifestFilter::LatestOneByName(
                            gk_packer.variant_to_name(),
                        )],
                    )
                }
                crate::Ami::PackerManifest(gk_packer, packer_run_uuid) => {
                    // A specific, likely archived packer build
                    (
                        ace_db::Filter::None,
                        vec![ace_db::PackerManifestFilter::One(
                            gk_packer.variant_to_name(),
                            packer_run_uuid,
                        )],
                    )
                }
                crate::Ami::Db(name) => (
                    ace_db::Filter::One(name.to_string()),
                    vec![ace_db::PackerManifestFilter::None],
                ),
            }
        }
        crate::AmiFilter::None => return Ok(Vec::new()),
    };

    let mut latest_images = Vec::new();
    let mut all_images = Vec::new();

    // Collect packer images from data::packer_manifest
    // (match on manifest filters to decide which vec the images are stored in (necessary for later processing))
    for filter in manifest_filter {
        match filter {
            ace_db::PackerManifestFilter::All => all_images.extend(
                ace_db::data::packer_manifest::select(&filter, &app.data_path)
                    .await
                    .change_context(ErrorStack::DbError)?,
            ),
            ace_db::PackerManifestFilter::AllByName(_) => all_images.extend(
                ace_db::data::packer_manifest::select(&filter, &app.data_path)
                    .await
                    .change_context(ErrorStack::DbError)?,
            ),
            ace_db::PackerManifestFilter::LatestEachByName => latest_images.extend(
                ace_db::data::packer_manifest::select(&filter, &app.data_path)
                    .await
                    .change_context(ErrorStack::DbError)?,
            ),
            ace_db::PackerManifestFilter::One(_, _) => all_images.extend(
                ace_db::data::packer_manifest::select(&filter, &app.data_path)
                    .await
                    .change_context(ErrorStack::DbError)?,
            ),
            ace_db::PackerManifestFilter::LatestOneByName(_) => latest_images.extend(
                ace_db::data::packer_manifest::select(&filter, &app.data_path)
                    .await
                    .change_context(ErrorStack::DbError)?,
            ),
            ace_db::PackerManifestFilter::None => continue, // Do nothing
        }
    }

    // Collect etc amis
    let etc_amis = ace_db::etc::ami::select(etc_filter, &app.etc_path)
        .await
        .change_context(ErrorStack::DbError)?;

    // Get current account_key and region
    let env_info =
        ace_db::current_account_key_and_region().change_context(ErrorStack::GetCurrentEnvInfo)?;

    let mut rval = Vec::new();

    // Process etc amis
    for image in etc_amis {
        match image {
            Ok(ami) => {
                rval.push(Ok(construct_etc_ami(
                    ami,
                    &env_info.account_key,
                    &env_info.region,
                )));
            }
            Err(e) => {
                rval.push(Err(e.change_context(ErrorStack::DbError)));
            }
        }
    }

    // Process latest_images
    for image in latest_images {
        rval.push(construct_from_latest_pkr_image(
            image,
            &env_info.account_key,
            &env_info.region,
        ));
    }

    // Process all_images
    for image in all_images {
        rval.push(construct_from_specific_image(
            image,
            &env_info.account_key,
            &env_info.region,
        ));
    }

    Ok(rval)
}

fn extract_ami_id(artifact_id: &str) -> Option<String> {
    let mut artifacts = artifact_id.split(',');
    let artifact = artifacts.next()?;
    let mut parts = artifact.splitn(2, ':');
    let _artifact_region = parts.next()?.to_string();
    let artifact_ami_id = parts.next()?.to_string();

    Some(artifact_ami_id)
}

fn match_name_to_packer(name: &str) -> error_stack::Result<crate::Packer, ErrorStack> {
    match name {
        "ubuntu-22-04-ace2" => Ok(crate::Packer::Ubuntu2204Ace2),
        "ubuntu-22-04-devbox" => Ok(crate::Packer::Ubuntu2204Devbox),
        "ubuntu-22-04-docker" => Ok(crate::Packer::Ubuntu2204Docker),
        "ubuntu-22-04-openvpn" => Ok(crate::Packer::Ubuntu2204Openvpn),
        "ubuntu-22-04-postgresql" => Ok(crate::Packer::Ubuntu2204Postgresql),
        "ubuntu-22-04-videoproc" => Ok(crate::Packer::Ubuntu2204Videoproc),

        //Technically this should NEVER happen, but...
        _ => Err(error_stack::report!(ErrorStack::UnknownPacker(
            name.to_string()
        ))),
    }
}

fn construct_etc_ami(ami: ace_db::etc::ami::Ami, account_key: &str, region: &str) -> Ami {
    let name = ami.name.clone();

    Ami {
        name: ami.name,
        graphkey: crate::Ami::Db(name),
        account_key: account_key.to_string(),
        region: region.to_string(),
        ami_id: ami.ami_id,
        build_timestamp: None,
    }
}

fn construct_from_latest_pkr_image(
    pkr: ace_db::data::packer_manifest::SerdeBuild,
    account_key: &str,
    region: &str,
) -> error_stack::Result<Ami, ErrorStack> {
    let Some(ami_id) = extract_ami_id(&pkr.artifact_id) else {
        return Err(error_stack::report!(
            ErrorStack::ExtractAmiIdFromPackerArtifactId(pkr.artifact_id.clone())
        ));
    };

    let packer_graphkey = match_name_to_packer(&pkr.name)?;

    Ok(Ami {
        name: pkr.name,
        graphkey: crate::Ami::Packer(packer_graphkey),
        account_key: account_key.to_string(),
        region: region.to_string(),
        ami_id,
        build_timestamp: Some(pkr.build_time),
    })
}

fn construct_from_specific_image(
    pkr: ace_db::data::packer_manifest::SerdeBuild,
    account_key: &str,
    region: &str,
) -> error_stack::Result<Ami, ErrorStack> {
    let Some(ami_id) = extract_ami_id(&pkr.artifact_id) else {
        return Err(error_stack::report!(
            ErrorStack::ExtractAmiIdFromPackerArtifactId(pkr.artifact_id.clone())
        ));
    };

    let packer_graphkey = match_name_to_packer(&pkr.name)?;

    Ok(Ami {
        name: pkr.name,
        graphkey: crate::Ami::PackerManifest(packer_graphkey, pkr.packer_run_uuid.clone()),
        account_key: account_key.to_string(),
        region: region.to_string(),
        ami_id,
        build_timestamp: Some(pkr.build_time),
    })
}
