use crate::{GraphKeyExt, GraphKeyName};
use error_stack::ResultExt;
use std::collections::HashMap;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    AccountRegionStructNotFound,
    GetConfig,
    GetCurrentAccount,
    GetDeveloper(crate::Developer),
    GetDeveloperApp(String, String),
    GetDeveloperFromDB(String),
    NotOneFound(crate::Developer),
    ParseAppGraphKey(crate::Developer, String),
    SelectDevelopersFromDB,
    DeserializeSubnet(String),
    SshPublicKeyFormatting(String),
}

#[derive(Debug, Clone)]
pub struct Developer {
    pub graphkey: crate::Developer,
    pub name: String,
    pub subnet: crate::Subnet,
    pub secret: String,
    pub instance_type: String,
    pub volume_size: u32,
    pub dns_prefix: String,
    pub dns_wildcard_prefix: String,
    pub private_hostname: String,
    pub public_hostname: String,
    pub public_ip: String,
    pub private_ip: String,
    pub resource_id: String,
    pub base_endpoint: String,
    pub applications: Vec<String>,
    pub connection: Option<String>,
    pub region: String,
    pub ssh_public_key: Option<String>,
    pub ace2_server_name: String,
    pub app_config_map: HashMap<AppName, ace_db::etc::developer::DeveloperAppConfig>,
    pub ace_legacy_auth: Option<String>,
    pub active: bool,
}

impl crate::GraphValueExt for Developer {}

impl GraphKeyName for crate::Developer {
    fn get_name(&self) -> String {
        match self {
            crate::Developer::Db(name) => name.clone(),
        }
    }
}

pub type DeveloperList = Vec<Developer>;
pub type AceEndpoint = String;
pub type AppName = String;
pub type Url = String;

impl Developer {
    #[must_use]
    /// Returns (`base_ace_endpoint`, list of: (`app name`, `app_url`))
    pub fn get_urls(&self) -> (AceEndpoint, Vec<(AppName, Url)>) {
        let mut urls: Vec<(AppName, Url)> = Vec::new();

        let ace_endpoint = format!(
            "https://{}:{}@{}",
            self.name, self.secret, self.ace2_server_name
        );

        for app_name in self.app_config_map.keys() {
            if app_name == "ctzen" {
                urls.push((
                    "ctzen".to_string(),
                    format!("{ace_endpoint}/cz/config?app=ctzen"),
                ));
            }
            if app_name == "ehungry" {
                urls.push((
                    "ehungry".to_string(),
                    format!("{ace_endpoint}/eh/config?app=ehungry"),
                ));
            }
        }

        (ace_endpoint, urls)
    }
}

pub async fn get(
    gk_developer: &crate::Developer,
    app: &ace_db::App,
) -> error_stack::Result<Developer, ErrorStack> {
    let filter = crate::DeveloperFilter::One(gk_developer.clone());
    let mut developers = select_result(&filter, app).await?;

    // Validate that one record was returned
    let developer: Result<Developer, error_stack::Report<ErrorStack>> = {
        // if length != 1, return error
        if developers.len() == 1 {
            developers.pop().unwrap()
        } else {
            error_stack::bail!(ErrorStack::NotOneFound(gk_developer.clone()))
        }
    };

    developer.change_context_lazy(|| ErrorStack::GetDeveloper(gk_developer.to_owned()))
}

pub async fn select(
    filter: &crate::DeveloperFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<Developer>, ErrorStack> {
    // Fails at ANY error found (but not necessarily at "hey didn't find a match"?).
    let mut rval = Vec::new();

    for dev_result in select_result(filter, app).await? {
        match dev_result {
            Ok(developer) => {
                rval.push(developer);
            }
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    filter: &crate::DeveloperFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<Developer, ErrorStack>>, ErrorStack> {
    let db_filter = match filter {
        crate::DeveloperFilter::All => ace_db::Filter::All,
        crate::DeveloperFilter::One(crate::Developer::Db(name)) => {
            ace_db::Filter::One(name.to_owned())
        }
        crate::DeveloperFilter::None => return Ok(Vec::new()), // exit early
    };

    let mut rval = Vec::new();

    let (current_account_key, current_region_key, ace2_server_name) = {
        let config = crate::config::get(&crate::Config::EtcConfig, app)
            .await
            .change_context(ErrorStack::GetConfig)?;

        (config.account_key, config.region, config.ace2_server_name)
    };

    let current_account = crate::account::get(&crate::Account::Db(current_account_key), app)
        .await
        .change_context(ErrorStack::GetCurrentAccount)?;

    let Some(current_region) = current_account.region.get(&current_region_key) else {
        error_stack::bail!(ErrorStack::AccountRegionStructNotFound);
    };

    let developers = ace_db::etc::developer::select(db_filter, &app.etc_path)
        .await
        .change_context_lazy(|| ErrorStack::SelectDevelopersFromDB)?;

    for developer in developers {
        match developer {
            Ok(d) => {
                let developer =
                    construct(d, &current_account, current_region, &ace2_server_name).await;
                rval.push(developer);
            }
            Err(e) => {
                rval.push(Err(e.change_context(ErrorStack::SelectDevelopersFromDB)));
            }
        }
    }

    Ok(rval)
}

async fn construct(
    db_developer: ace_db::etc::developer::Developer,
    current_account: &crate::account::Account,
    current_region: &crate::account::AccountRegion,
    ace2_server_name: &str,
) -> error_stack::Result<Developer, ErrorStack> {
    let dev_graphkey = crate::Developer::Db(db_developer.name.clone());

    let parsed_subnet = crate::Subnet::deserialize(&db_developer.subnet).map_err(|e| {
        ErrorStack::DeserializeSubnet(format!(
            "while loading developer {} {}",
            db_developer.name, e
        ))
    })?;

    let private_domain_name = current_account.private_domain.clone();
    let public_domain_name = current_account.public_domain.clone();

    let dns_prefix = format!("{}.dev", db_developer.name);
    let dns_wildcard_prefix = format!("*.{dns_prefix}");

    let private_subdomain_name = format!("{}.{}", current_region.name, private_domain_name);
    let public_subdomain_name = format!("{}.{}", current_region.name, public_domain_name);

    let private_hostname = format!("{dns_prefix}.{private_subdomain_name}");
    let public_hostname = format!("{dns_prefix}.{public_subdomain_name}");

    let public_ip = db_developer
        .public_ip
        .as_deref()
        .unwrap_or("N/A")
        .to_string();
    let private_ip = db_developer
        .private_ip
        .as_deref()
        .unwrap_or("N/A")
        .to_string();

    let resource_id = db_developer
        .resource_id
        .as_deref()
        .unwrap_or("N/A")
        .to_string();

    let base_endpoint = format!("https://{}", public_hostname);
    let region = current_region.name.clone();

    let mut applications: Vec<String> = db_developer.app.keys().cloned().collect();
    applications.sort();
    let connection = Some(format!("ssh app@{}", private_hostname));

    let ssh_public_key = match db_developer.ssh_public_key {
        Some(key) => {
            let key = key.trim().to_owned();
            Some(key)
        }
        None => None,
    };

    let developer = Developer {
        graphkey: dev_graphkey,
        name: db_developer.name,
        subnet: parsed_subnet,
        secret: db_developer.secret,
        instance_type: db_developer.instance_type,
        volume_size: db_developer.volume_size,
        dns_prefix,
        dns_wildcard_prefix,
        private_hostname,
        public_hostname,
        public_ip,
        private_ip,
        resource_id,
        base_endpoint,
        applications,
        connection,
        region,
        ssh_public_key,
        ace2_server_name: ace2_server_name.to_string(),
        app_config_map: db_developer.app,
        ace_legacy_auth: db_developer.ace_legacy_auth,
        active: db_developer.active,
    };

    Ok(developer)
}

pub async fn select_result_instance(
    filter: &crate::InstanceFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<crate::ins::Instance, ErrorStack>>, ErrorStack> {
    let mut rval = Vec::new();

    let developer_filter: crate::DeveloperFilter = filter.into();

    for developer in select_result(&developer_filter, app).await? {
        match developer {
            Ok(developer) => {
                let instance = crate::ins::Instance {
                    graphkey: crate::Instance::Developer(developer.graphkey.clone()),
                    ami_graphkey: crate::Ami::Packer(crate::Packer::Ubuntu2204Devbox),
                    name: format!("dev-{}", developer.name),
                    subnet: developer.subnet.clone().variant_to_name(),
                    instance_type: developer.instance_type,
                    volume_size: crate::ins::VolumeSize::Specific(developer.volume_size),
                    ingress: vec![],
                    use_elastic_ip: false,
                };
                rval.push(Ok(instance));
            }
            Err(e) => {
                rval.push(Err(e));
            }
        }
    }

    Ok(rval)
}

pub async fn select_result_keypair(
    filter: &crate::KeyPairFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<crate::keypair::KeyPair, ErrorStack>>, ErrorStack>
{
    let mut rval = Vec::new();

    //TODO: FIX123
    let developer_filter: crate::DeveloperFilter = filter.into();

    for developer in select_result(&developer_filter, app).await? {
        match developer {
            Ok(developer) => {
                if let Some(ssh_public_key) = developer.ssh_public_key {
                    let keypair = crate::keypair::KeyPair {
                        graphkey: crate::KeyPair::Developer(developer.graphkey.clone()),
                        name: format!("developer-{}-keypair", developer.name),
                        public_key: ssh_public_key,
                    };

                    rval.push(Ok(keypair));
                }
            }
            Err(e) => {
                rval.push(Err(e));
            }
        }
    }

    Ok(rval)
}

pub async fn select_result_securitygroup(
    filter: &crate::SecurityGroupFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<crate::sg::SecurityGroup, ErrorStack>>, ErrorStack>
{
    let mut rval = Vec::new();

    let developer_filter: crate::DeveloperFilter = filter.into();

    for developer in select_result(&developer_filter, app).await? {
        match developer {
            Ok(developer) => {
                let securitygroup = crate::sg::SecurityGroup {
                    graphkey: crate::SecurityGroup::Developer(developer.graphkey.clone()),
                    name: format!("developer-{}", developer.name),
                };

                rval.push(Ok(securitygroup));
            }
            Err(e) => {
                rval.push(Err(e));
            }
        }
    }

    Ok(rval)
}

// Lists tls certs for all filtered developers
pub async fn select_result_tlscert(
    filter: &crate::TlsCertFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<crate::tlscert::TlsCert, ErrorStack>>, ErrorStack>
{
    let mut rval = Vec::new();

    let developer_filter: crate::DeveloperFilter = filter.into();

    let config = crate::config::get(&crate::Config::EtcConfig, app)
        .await
        .change_context(ErrorStack::GetConfig)?;

    // Iterate over list of developers - filter happens in list()
    for dev_result in select_result(&developer_filter, app).await? {
        match dev_result {
            Ok(developer) => {
                let gk_priv =
                    crate::TlsCert::Developer(developer.graphkey.clone(), "wcpvt".to_string());
                let gk_pub =
                    crate::TlsCert::Developer(developer.graphkey.clone(), "wcpub".to_string());

                match crate::tlscert::read_expiry(&gk_priv, app).await {
                    Ok(expiration_time) => {
                        rval.push(Ok(crate::tlscert::TlsCert {
                            graphkey: gk_priv,
                            common_name: format!(
                                "{}.dev.{}",
                                developer.name, config.private_subdomain_name
                            ),
                            expiration_time,
                            subject_alternative_names: vec![format!(
                                "*.{}.dev.{}",
                                developer.name, config.private_subdomain_name
                            )],
                        }));
                    }
                    Err(e) => {
                        rval.push(Err(e.change_context(ErrorStack::GetDeveloper(
                            developer.graphkey.to_owned(),
                        ))));
                    }
                };

                match crate::tlscert::read_expiry(&gk_pub, app).await {
                    Ok(expiration_time) => {
                        rval.push(Ok(crate::tlscert::TlsCert {
                            graphkey: gk_pub,
                            common_name: format!(
                                "{}.dev.{}",
                                developer.name, config.public_subdomain_name
                            ),
                            expiration_time,
                            subject_alternative_names: vec![format!(
                                "*.{}.dev.{}",
                                developer.name, config.public_subdomain_name
                            )],
                        }));
                    }
                    Err(e) => {
                        rval.push(Err(e.change_context(ErrorStack::GetDeveloper(
                            developer.graphkey.to_owned(),
                        ))));
                    }
                }
            }
            Err(e) => {
                rval.push(Err(e));
            }
        }

        // only process devserver-private if it has been asked for
        // ...
    }

    Ok(rval)
}
