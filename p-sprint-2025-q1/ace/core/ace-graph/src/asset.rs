use std::path::{Path, PathBuf};

use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    DbError,
    GetAsset(String),
    NotOneFound(String),
    SelectFromDb,
    ObtainFilename(PathBuf),
    UndefinedAsset(String),
}

#[derive(Debug, Clone)]
pub struct Asset {
    pub graphkey: crate::Asset,
    pub name: String,

    pub version: String,
    pub target: String,
    pub hash: String,
}

impl crate::GraphValueExt for Asset {}

pub async fn select(asset_path: &Path) -> error_stack::Result<Vec<Asset>, ErrorStack> {
    // Fails at ANY error found
    let mut rval = Vec::new();

    for asset in select_result(asset_path).await? {
        match asset {
            Ok(asset) => {
                rval.push(asset);
            }
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

/// Selects ALL assets from the database
pub async fn select_result(
    asset_path: &Path,
) -> error_stack::Result<Vec<error_stack::Result<Asset, ErrorStack>>, ErrorStack> {
    let mut rval = Vec::new();

    let assets = ace_db::asset::select(asset_path)
        .await
        .change_context(ErrorStack::SelectFromDb)?;

    for asset in assets {
        match asset {
            Ok(asset) => {
                rval.push(construct(asset));
            }
            Err(e) => {
                rval.push(Err(e.change_context(ErrorStack::DbError)));
            }
        }
    }

    Ok(rval)
}

fn construct(asset: ace_db::asset::Asset) -> error_stack::Result<Asset, ErrorStack> {
    let target = asset.target.clone();
    let version = asset.version.clone();

    let graphkey = match asset.bin_name.as_str() {
        "ace-agent" => crate::Asset::AceAgent(target, version),
        "ace-agent-updater" => crate::Asset::AceAgentUpdater(target, version),
        "mp-process" => crate::Asset::MediaproctorProcess(target, version),
        "mp-stream" => crate::Asset::MediaproctorStream(target, version),
        _ => {
            error_stack::bail!(ErrorStack::UndefinedAsset(asset.bin_name.clone()))
        }
    };

    Ok(Asset {
        graphkey,
        name: asset.bin_name,
        version: asset.version,
        target: asset.target,
        hash: asset.hash,
    })
}
