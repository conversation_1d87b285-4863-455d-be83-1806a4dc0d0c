#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    NotOneSubnetFound(crate::Subnet, usize),
}

#[derive(Debug)]
pub struct Subnet {
    pub graphkey: crate::Subnet,
    pub name: String,
    // Other information...
}

impl crate::GraphValueExt for Subnet {}

pub async fn get(gk_subnet: crate::Subnet) -> error_stack::Result<Subnet, ErrorStack> {
    let filter = crate::SubnetFilter::One(gk_subnet.clone());

    let mut subnets = select(filter).await?;
    if subnets.len() == 1 {
        return Ok(subnets.pop().unwrap());
    }

    error_stack::bail!(ErrorStack::NotOneSubnetFound(gk_subnet, subnets.len()));
}

pub async fn select(filter: crate::SubnetFilter) -> error_stack::Result<Vec<Subnet>, ErrorStack> {
    let mut rval = Vec::new();

    for subnet in select_result(filter).await? {
        match subnet {
            Ok(subnet) => rval.push(subnet),
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    filter: crate::SubnetFilter,
) -> error_stack::Result<Vec<error_stack::Result<Subnet, ErrorStack>>, ErrorStack> {
    let built_in_filter = match filter {
        crate::SubnetFilter::One(gk_subnet) => Some(gk_subnet),
        crate::SubnetFilter::All => None,
        crate::SubnetFilter::None => return Ok(Vec::new()),
    };

    let mut rval = Vec::new();

    let subnets = vec![
        crate::Subnet::PublicA,
        crate::Subnet::PublicB,
        crate::Subnet::PublicC,
        crate::Subnet::PrivateA,
        crate::Subnet::PrivateB,
        crate::Subnet::PrivateC,
        crate::Subnet::Vpn,
        crate::Subnet::Ace,
        crate::Subnet::Temporal,
    ];

    for subnet in subnets {
        // Filter if a specific key is provided
        if let Some(filter_key) = &built_in_filter {
            if &subnet != filter_key {
                continue;
            }
        }

        rval.push(Ok(Subnet {
            graphkey: subnet.clone(),
            name: subnet.variant_to_name(),
        }));
    }

    Ok(rval)
}
