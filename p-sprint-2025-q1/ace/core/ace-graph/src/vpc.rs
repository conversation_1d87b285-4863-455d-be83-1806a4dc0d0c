use error_stack::ResultExt;

#[derive(Debug)]
pub struct Vpc {
    pub graphkey: crate::Vpc,
    pub name: String,
    pub cidr_block: ipnetwork::IpNetwork,
}

impl super::GraphValueExt for Vpc {}

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    GetConfig,
}

pub async fn get(graphkey: &crate::Vpc, app: &ace_db::App) -> error_stack::Result<Vpc, ErrorStack> {
    let config = crate::config::get_default(app)
        .await
        .change_context(ErrorStack::GetConfig)?;

    let vpc = match graphkey {
        crate::Vpc::Ace => {
            let name = "ace";

            let cidr_block = config.cidr_block;

            Vpc {
                graphkey: crate::Vpc::Ace,
                name: name.to_string(),
                cidr_block,
            }
        }
    };

    Ok(vpc)
}
