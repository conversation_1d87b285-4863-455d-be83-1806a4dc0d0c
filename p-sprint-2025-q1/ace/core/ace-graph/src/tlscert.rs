use crate::Graph<PERSON>eyExt;
use chrono::TimeZone;
use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    ConvertAsn1TimeToChrono,
    Get(crate::TlsCert),
    GetConfig,
    NotOneFound(crate::TlsCert, usize),
    ParseAceKeyToCert,
    ReadAceTlsCertFile,
    SelectFromApp,
    SelectFromDeveloper,
}

#[derive(Debug)]
pub struct TlsCert {
    pub graphkey: crate::TlsCert,
    pub common_name: String,
    pub subject_alternative_names: Vec<String>,
    pub expiration_time: Option<chrono::DateTime<chrono::Utc>>,
}

impl TlsCert {
    pub async fn read_expiry(
        &self,
        ace_db_app: &ace_db::App,
    ) -> error_stack::Result<Option<chrono::DateTime<chrono::Utc>>, ErrorStack> {
        let self_path = match &self.graphkey {
            crate::TlsCert::Ace => ace_db_app.data_path.join("tls.tlscert-ace.cert.pem"),
            crate::TlsCert::Developer(developer, _purpose) => ace_db_app.data_path.join(format!(
                "tls.tlscert-{}.cert.pem",
                developer.serialize_dashed()
            )),
            crate::TlsCert::App(app, _purpose) => ace_db_app
                .data_path
                .join(format!("tls.tlscert-{}.cert.pem", app)),
        };

        read_tlscert_and_parse_expire(&self_path).await
    }
}

impl crate::GraphValueExt for TlsCert {}

pub async fn get(
    gk_tlscert: &crate::TlsCert,
    app: &ace_db::App,
) -> error_stack::Result<TlsCert, ErrorStack> {
    let filter = crate::TlsCertFilter::One(gk_tlscert.to_owned());
    let mut tlscerts = select_result(&filter, app).await?;

    // Validate that one record was returned
    let tlscert: Result<TlsCert, error_stack::Report<ErrorStack>> = {
        // if length != 1, return error
        if tlscerts.len() == 1 {
            tlscerts.pop().unwrap()
        } else {
            error_stack::bail!(ErrorStack::NotOneFound(gk_tlscert.clone(), tlscerts.len()))
        }
    };

    tlscert.change_context_lazy(|| ErrorStack::Get(gk_tlscert.to_owned()))
}

pub async fn select(
    filter: &crate::TlsCertFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<TlsCert>, ErrorStack> {
    // Fails at ANY error found (but not necessarily at "hey didn't find a match"?).
    let mut rval = Vec::new();

    for row in select_result(filter, app).await? {
        match row {
            Ok(developer) => {
                rval.push(developer);
            }
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    filter: &crate::TlsCertFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<TlsCert, ErrorStack>>, ErrorStack> {
    let mut rval = Vec::new();

    let config = crate::config::get(&crate::Config::EtcConfig, app)
        .await
        .change_context(ErrorStack::GetConfig)?;

    let ace2_server_name = config.ace2_server_name;

    if matches!(
        filter,
        crate::TlsCertFilter::Ace
            | crate::TlsCertFilter::All
            | crate::TlsCertFilter::One(crate::TlsCert::Ace)
    ) {
        // Read the actual cert for the expiration time!!
        let ace_gk = crate::TlsCert::Ace;

        match read_expiry(&ace_gk, app).await {
            Ok(expiration_time) => {
                rval.push(Ok(TlsCert {
                    graphkey: ace_gk,
                    common_name: ace2_server_name,
                    subject_alternative_names: vec![],
                    expiration_time,
                }));
            }
            Err(e) => {
                rval.push(Err(e.change_context(ErrorStack::Get(ace_gk))));
            }
        }
    }

    // Call every place that tls certs can be found
    rval.extend(
        crate::developer::select_result_tlscert(filter, app)
            .await
            .change_context_lazy(|| ErrorStack::SelectFromDeveloper)?
            .into_iter()
            .map(|r| r.change_context_lazy(|| ErrorStack::SelectFromDeveloper)),
    );
    rval.extend(
        crate::app::select_result_tlscert(filter, app)
            .await
            .change_context_lazy(|| ErrorStack::SelectFromApp)?
            .into_iter()
            .map(|r| r.change_context_lazy(|| ErrorStack::SelectFromApp)),
    );

    Ok(rval)
}

/// Determines path to read based on the TlsCert type.  Returns the expiration time of the certificate as an Option. (It may not exist.)
pub(crate) async fn read_expiry(
    gk: &crate::TlsCert,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<Option<chrono::DateTime<chrono::Utc>>, ErrorStack> {
    let self_path = match gk {
        crate::TlsCert::Ace => ace_db_app.data_path.join("tls.tlscert-ace.cert.pem"),
        crate::TlsCert::Developer(developer, _purpose) => ace_db_app.data_path.join(format!(
            "tls.tlscert-{}.cert.pem",
            developer.serialize_dashed()
        )),
        crate::TlsCert::App(app, _purpose) => ace_db_app
            .data_path
            .join(format!("tls.tlscert-{}.cert.pem", app)),
    };

    read_tlscert_and_parse_expire(&self_path).await
}

/// Parses an x509 certificate and returns the expiration time converted from ASN1TIME to chrono::DateTime<Utc>.
async fn read_tlscert_and_parse_expire(
    cert_path: &std::path::Path,
) -> error_stack::Result<Option<chrono::DateTime<chrono::Utc>>, ErrorStack> {
    if !cert_path.exists() {
        return Ok(None);
    }

    let cert_contents = tokio::fs::read(&cert_path)
        .await
        .change_context(ErrorStack::ReadAceTlsCertFile)?;
    let cert = openssl::x509::X509::from_pem(&cert_contents)
        .change_context(ErrorStack::ParseAceKeyToCert)?;
    let expire_asn1time_str = cert.not_after().to_string();

    // Pop the "GMT" off the end of the string so chrono can parse it
    let expire_asn1time_trimmed = expire_asn1time_str.trim_end_matches(" GMT");

    let expire_naive_chrono =
        chrono::NaiveDateTime::parse_from_str(expire_asn1time_trimmed, "%b %d %H:%M:%S %Y")
            .change_context(ErrorStack::ConvertAsn1TimeToChrono)?;

    Ok(Some(chrono::Utc.from_utc_datetime(&expire_naive_chrono)))
}
