use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    NotOneBucketFound(crate::Bucket, usize),
    SelectFromApp,
    GetConfig,
}

#[derive(Debug)]
pub struct Bucket {
    pub graphkey: crate::Buck<PERSON>,
    pub name: String,
    pub developer_access: bool,
    pub public_read: Vec<String>,
    pub suffix: String,
}

impl crate::GraphValueExt for Bucket {}

pub async fn get(
    gk_bucket: crate::Bucket,
    app: &ace_db::App,
) -> error_stack::Result<Bucket, ErrorStack> {
    let filter = crate::BucketFilter::One(gk_bucket.clone());

    let mut buckets = select(filter, app).await?;
    if buckets.len() == 1 {
        return Ok(buckets.pop().unwrap());
    }

    error_stack::bail!(ErrorStack::NotOneBucketFound(gk_bucket, buckets.len()));
}

pub async fn select(
    filter: crate::BucketFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<Bucket>, ErrorStack> {
    let mut rval = Vec::new();

    for bucket in select_result(filter, app).await? {
        match bucket {
            Ok(bucket) => rval.push(bucket),
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    filter: crate::BucketFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<Bucket, ErrorStack>>, ErrorStack> {
    let mut rval = Vec::new();

    if matches!(
        filter,
        crate::BucketFilter::One(crate::Bucket::Ace) | crate::BucketFilter::All
    ) {
        let config = crate::config::get(&crate::Config::EtcConfig, app)
            .await
            .change_context_lazy(|| ErrorStack::GetConfig)?;

        rval.push(Ok(Bucket {
            graphkey: crate::Bucket::Ace,
            name: format!("{}-{}-ace2", config.account_key, config.region),
            developer_access: false,
            public_read: vec![],
            suffix: "ace".to_string(),
        }));
    }

    rval.extend(
        crate::app::select_result_bucket(&filter, app)
            .await
            .change_context_lazy(|| ErrorStack::SelectFromApp)?
            .into_iter()
            .map(|r| r.change_context_lazy(|| ErrorStack::SelectFromApp)),
    );

    Ok(rval)
}
