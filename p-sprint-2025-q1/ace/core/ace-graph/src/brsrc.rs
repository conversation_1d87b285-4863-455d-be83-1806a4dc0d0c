use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    DbError,
    GetAwsAccountFromDb(crate::Brsrc),
    GetBrsrc(crate::Brsrc),
    NotOneFound(crate::Brsrc),
    SelectFromDb,
}

#[derive(Debug)]
pub struct Brsrc {
    pub graphkey: crate::Brsrc,
    pub name: String,
    pub source: Source,
    pub target: Target,
}

impl crate::GraphValueExt for Brsrc {}

#[derive(Debug)]
pub struct Source {
    pub bucket: String,
    pub bucket_arn: String,
}

impl crate::GraphValueExt for Source {}

#[derive(Debug)]
pub struct Target {
    pub bucket: String,
    pub bucket_arn: String,
    pub account_key: String,
    pub region: String,
    pub aws_account_id: String,
}

impl crate::GraphValueExt for Target {}

pub async fn get(
    gk_brsrc: &crate::Brsrc,
    app: &ace_db::App,
) -> error_stack::Result<Brsrc, ErrorStack> {
    let filter = crate::BrsrcFilter::One(gk_brsrc.clone());
    let mut brsrcs = select_result(&filter, app).await?;

    // Validate that one record was returned
    let brsrc: Result<Brsrc, error_stack::Report<ErrorStack>> = {
        // if length != 1, return error
        if brsrcs.len() == 1 {
            brsrcs.pop().unwrap()
        } else {
            error_stack::bail!(ErrorStack::NotOneFound(gk_brsrc.clone()))
        }
    };

    brsrc.change_context_lazy(|| ErrorStack::GetBrsrc(gk_brsrc.to_owned()))
}

pub async fn select(
    filter: &crate::BrsrcFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<Brsrc>, ErrorStack> {
    // Fails at ANY error found (but not necessarily at "hey didn't find a match"?).
    let mut rval = Vec::new();

    for brsrc in select_result(filter, app).await? {
        match brsrc {
            Ok(brsrc) => {
                rval.push(brsrc);
            }
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    filter: &crate::BrsrcFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<Brsrc, ErrorStack>>, ErrorStack> {
    let db_filter = match filter {
        crate::BrsrcFilter::All => ace_db::Filter::All,
        crate::BrsrcFilter::One(crate::Brsrc::Db(name)) => ace_db::Filter::One(name.to_owned()),
        crate::BrsrcFilter::None => return Ok(Vec::new()),
    };

    let mut rval = Vec::new();

    let brsrcs = ace_db::etc::brsrc::select(db_filter, &app.etc_path)
        .await
        .change_context(ErrorStack::SelectFromDb)?;

    for brsrc in brsrcs {
        match brsrc {
            Ok(brsrc) => {
                let brsrc = construct(brsrc, app).await;
                rval.push(brsrc);
            }
            Err(e) => {
                rval.push(Err(e.change_context(ErrorStack::DbError)));
            }
        }
    }

    Ok(rval)
}

async fn construct(
    brsrc: ace_db::etc::brsrc::BrSrc,
    app: &ace_db::App,
) -> error_stack::Result<Brsrc, ErrorStack> {
    let graphkey = crate::Brsrc::Db(brsrc.name.clone());

    let desired_account_key = &brsrc.target.account_key;
    let aws_account = crate::account::get(&crate::Account::Db(desired_account_key.to_owned()), app)
        .await
        .change_context(ErrorStack::GetAwsAccountFromDb(graphkey.clone()))?;

    let source = Source {
        bucket: brsrc.source.bucket.clone(),
        bucket_arn: format!("arn:aws:s3:::{}", brsrc.source.bucket),
    };

    let target = Target {
        bucket: brsrc.target.bucket.clone(),
        bucket_arn: format!("arn:aws:s3:::{}", brsrc.target.bucket),
        account_key: aws_account.account_key.clone(),
        region: brsrc.target.region.clone(),
        aws_account_id: aws_account.aws_account_id,
    };

    Ok(Brsrc {
        graphkey,
        name: brsrc.name,
        source,
        target,
    })
}
