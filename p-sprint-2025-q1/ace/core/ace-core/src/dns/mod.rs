use error_stack::ResultExt;
use reqwest;
use serde::Deserialize;
use serde_json;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    Deserialize,
    GetResponse,
    GetResponseText,
}

pub type NsValues = Vec<String>;

#[derive(Deserialize, Debug)]
pub struct DNSResponse {
    #[serde(rename = "Status")]
    pub status: u8,
    #[serde(rename = "TC")]
    pub tc: bool,
    #[serde(rename = "RD")]
    pub rd: bool,
    #[serde(rename = "RA")]
    pub ra: bool,
    #[serde(rename = "AD")]
    pub ad: bool,
    #[serde(rename = "CD")]
    pub cd: bool,
    #[serde(rename = "Question")]
    pub question: Vec<Question>,
    #[serde(rename = "Answer")]
    pub answer: Option<Vec<Answer>>,
}

#[derive(Deserialize, Debug)]
pub struct Question {
    #[serde(rename = "name")]
    pub name: String,
    #[serde(rename = "type")]
    pub r#type: u8,
}

#[derive(Deserialize, Debug)]
pub struct Answer {
    #[serde(rename = "name")]
    pub name: String,
    #[serde(rename = "type")]
    pub r#type: u8,
    #[serde(rename = "TTL")]
    pub ttl: u32,
    #[serde(rename = "data")]
    pub data: String,
}

pub async fn get_dns_response(domain: &String) -> error_stack::Result<DNSResponse, ErrorStack> {
    let url = format!("https://dns.google/resolve?name={domain}&type=NS");
    let r = reqwest::get(&url)
        .await
        .change_context(ErrorStack::GetResponse)?;
    let response = r.text().await.change_context(ErrorStack::GetResponseText)?;
    let dns_response: DNSResponse =
        serde_json::from_str(&response).change_context(ErrorStack::Deserialize)?;

    Ok(dns_response)
}

pub async fn get_ns_values(domain: &str) -> error_stack::Result<NsValues, ErrorStack> {
    let dns_response = get_dns_response(&domain.to_string()).await?;

    Ok(match dns_response.answer {
        Some(answer) => {
            let mut ns_values = Vec::new();
            for record in answer {
                //Get rid of the silly extra period at the end of the ns record before returning
                ns_values.push(record.data.trim_end_matches('.').to_string());
            }
            ns_values
        }

        None => NsValues::new(),
    })
}
