use error_stack::ResultExt;
use garbage::CNSL;
use std::path::PathBuf;
use tokio::process::Command;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    GetConfig,
    GetCrl,
    GetInlineServerConfig,
    LoadConfigText,
    ValidateCRLCommandStatus(String),
    VpnConfigNotFound,
    WriteServerConfig,
}

pub async fn create(app: &crate::Application) -> error_stack::Result<PathBuf, ErrorStack> {
    let (vpn_client_subnet, subnet, vpn) = {
        let config = ace_graph::config::get(&ace_graph::Config::EtcConfig, &app.ace_db_app)
            .await
            .change_context(ErrorStack::GetConfig)?;
        (config.vpn_client_cidr_block, config.cidr_block, config.vpn)
    };

    let Some(vpn) = vpn else {
        error_stack::bail!(ErrorStack::VpnConfigNotFound)
    };

    // Add the route for this subnet
    let mut push_lines = vec![format!(
        "push \"route {} {}\"",
        &subnet.ip(),
        &subnet.mask()
    )];

    // Add the route for any additional subnets
    for additional_subnet in &vpn.additional_routed_subnets {
        push_lines.push(format!(
            "push \"route {} {}\"",
            additional_subnet.ip(),
            additional_subnet.mask()
        ));
    }

    // Regenerate the CRL
    let mut cmd = Command::new(crate::EASYRSA_BIN_PATH);
    cmd.current_dir(&app.ca_path);
    cmd.arg("gen-crl");
    cmd.env("EASYRSA_BATCH", "1");
    cmd.env("EASYRSA_SILENT", "1");
    cmd.env("EASYRSA_CRL_DAYS", "365");

    crate::ca::validate_status(cmd.status().await)
        .change_context(ErrorStack::ValidateCRLCommandStatus(format!("{:?}", &cmd)))?;

    // Read the new CRL (get it in string form so we can write it to the server config)
    let crl = crate::ca::get_crl(app)
        .await
        .change_context(ErrorStack::GetCrl)?;

    let inline_server_config = crate::ca::get_inline_server_config(app)
        .await
        .change_context(ErrorStack::GetInlineServerConfig)?;

    let server_line = format!(
        "server {} {}",
        vpn_client_subnet.ip(),
        vpn_client_subnet.mask()
    );

    #[rustfmt::skip]
    let rval = CNSL!(r"
        port 1194
        proto udp
        dev tun
        
        # Must be out of the VPN address space
        ", server_line, r"
        
        # Push the route to the clients
        ", push_lines.join("\n"), r"
        
        ifconfig-pool-persist ipp.txt
        keepalive 10 120
        persist-key
        persist-tun
        status server-status.log
        verb 3
        
        auth sha256
        
        <crl-verify>
        ", crl, r"
        </crl-verify>

        ", inline_server_config, r"                    
    ");

    let server_config_path = app.ace_db_app.vpn_path.join("server.conf");
    tokio::fs::write(&server_config_path, &rval)
        .await
        .change_context(ErrorStack::WriteServerConfig)?;

    Ok(server_config_path)
}

pub async fn load_config_text(app: &crate::Application) -> error_stack::Result<String, ErrorStack> {
    let config_path = app.ace_db_app.vpn_path.join("server.conf");
    let config_text = tokio::fs::read_to_string(&config_path)
        .await
        .change_context(ErrorStack::LoadConfigText)?;
    Ok(config_text)
}
