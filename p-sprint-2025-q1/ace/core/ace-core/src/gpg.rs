use error_stack::{Report, Result};
use std::process::Command;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    GpgNotInstalled,
    CreateDirectory,
    ExecuteCommand,
}
// Function to check if GPG is installed and the ./gpg directory exists
pub fn check_gpg_installation(app: &crate::Application) -> Result<(), ErrorStack> {
    let gpg_dir = app.path.join("./gpg");
    // Create the GPG directory if it does not exist
    if !gpg_dir.exists() {
        println!(
            "GPG directory not found, initializing GPG in {}",
            gpg_dir.display()
        );
        std::fs::create_dir_all(&gpg_dir).map_err(|_| Report::new(ErrorStack::CreateDirectory))?;
    }
    // Check if GPG is installed by running a simple GPG command
    let gpg_check = Command::new("gpg").arg("--version").output();
    match gpg_check {
        Ok(output) if output.status.success() => {
            println!("GPG is installed and ready to use.");
            Ok(())
        }
        Ok(_) => {
            println!(
                "GPG is installed but is not functioning properly. Please check your installation."
            );
            Err(Report::new(ErrorStack::GpgNotInstalled))
        }
        Err(_) => {
            println!("GPG is required but not installed. Install it by running:\n    sudo apt-get install gnupg\nThen, re-run `ace init`.");
            Err(Report::new(ErrorStack::GpgNotInstalled))
        }
    }
}
// Function to list GPG keys, providing more detailed error handling
pub fn gpg_info() -> Result<String, ErrorStack> {
    let output = Command::new("gpg")
        .arg("--list-keys")
        .output()
        .map_err(|_| {
            println!("Failed to execute GPG command. Ensure GPG is properly installed.");
            Report::new(ErrorStack::ExecuteCommand)
        })?;
    if output.status.success() {
        String::from_utf8(output.stdout).map_err(|_| Report::new(ErrorStack::ExecuteCommand))
    } else {
        println!("GPG command executed, but no keys found or GPG is not functioning correctly.");
        Err(Report::new(ErrorStack::GpgNotInstalled))
    }
}
