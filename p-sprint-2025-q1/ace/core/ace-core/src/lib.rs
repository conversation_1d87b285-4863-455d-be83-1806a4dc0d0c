pub mod ca;
pub mod dns;
pub mod download;
pub mod gen_install_keys;
pub mod git;
pub mod gocryptfs;
pub mod gpg;
pub mod mediaproctor;
pub mod packer;
pub mod terraform;
pub mod tls;
pub mod vpn;

use error_stack::ResultExt;
use sha2::{Digest, Sha256};
use std::fs;
use std::fs::File;
use std::io::{BufReader, Read};
use std::path::{Path, PathBuf};

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    CouldNotDeterminePathOfCurrentExecutable,
    CouldNotFindGitRepo,
    CouldNotLoadPathFromRepo,
    CreateTempDir,
    FormatTimestamp,
    GetHostname,
    CheckGpgInstallation,
    GpgInfo,
    GpgNotInstalled,
    InvalidDirectoryFormat(String),
    LockStatuses,
    ParseTimeFormat,
    ReadFile,
}

pub fn get_static_app() -> error_stack::Result<&'static Application, ErrorStack> {
    let app = load_env()?;
    let static_app = Box::leak(Box::new(app));

    Ok(static_app)
}

/// Maps an `error_stack` error to an anyhow error.
#[macro_export]
macro_rules! map_err_anyhow {
    // Plain version without custom error message preface
    ($expr:expr) => {
        $expr.map_err(|e| anyhow::anyhow!(format!("{:#?}", e)))
    };

    // Version with custom error message preface
    ($expr:expr, $msg:expr) => {
        $expr.map_err(|e| anyhow::anyhow!(format!("{}: {:#?}", $msg, e)))
    };
}

pub const EASYRSA_BIN_PATH: &str = "/usr/share/easy-rsa/easyrsa";
pub const NEBULA_TAR_URL: &str =
    "https://github.com/slackhq/nebula/releases/download/v1.9.4/nebula-linux-amd64.tar.gz";
pub const NEBULA_BIN_SHA256: &str =
    "f6cc723ce9a52992a8aeeef54ff395559f0982e1adecf801abb324389cfa6420";
pub const NEBULA_CERT_BIN_SHA256: &str =
    "367a9c170b9d87f9586ed0c1262f8deea9f3e221b7487a0e66fd9331f6955aed";

pub const PACKER_VERSION: &str = "1.11.2";
pub const PACKER_ZIP_URL: &str =
    "https://releases.hashicorp.com/packer/1.11.2/packer_1.11.2_linux_amd64.zip";
pub const PACKER_ZIP_SHA256: &str =
    "ced13efc257d0255932d14b8ae8f38863265133739a007c430cae106afcfc45a";
pub const PACKER_ZIP_BIN_SHA256: &str =
    "43f203af70cc7d8bc8eb4a84a435d3281197574f0040b778c85b26d81b1ed5f3";

pub const TERRAFORM_VERSION: &str = "1.9.8";
pub const TERRAFORM_ZIP_URL: &str =
    "https://releases.hashicorp.com/terraform/1.9.8/terraform_1.9.8_linux_amd64.zip";
pub const TERRAFORM_ZIP_SHA256: &str =
    "186e0145f5e5f2eb97cbd785bc78f21bae4ef15119349f6ad4fa535b83b10df8";
pub const TERRAFORM_ZIP_BIN_SHA256: &str =
    "7386e89a97d0f24024955acc79ccf693b75b97f7c6383cb9d966e7d59aa5b223";

pub const EASYTLS_VERSION: &str = "cbfc6f1c1dbff834d92b510f5d310599394f3d3b";
pub const EASYTLS_BIN_URL: &str = "https://raw.githubusercontent.com/TinCanTech/easy-tls/cbfc6f1c1dbff834d92b510f5d310599394f3d3b/easytls";
pub const EASYTLS_BIN_SHA256: &str =
    "5896e7a205e6305e8741ec26805f839a5164dbf19428b21826c9c2318db91b45";

pub const GOCRYPTFS_TAR_URL: &str = "https://github.com/rfjakob/gocryptfs/releases/download/v2.4.0/gocryptfs_v2.4.0_linux-static_amd64.tar.gz";

pub const REGION_SET: [&str; 20] = [
    "us-east-1",
    "us-east-2",
    "us-west-1",
    "us-west-2",
    "eu-west-1",
    "eu-west-2",
    "eu-west-3",
    "eu-central-1",
    "eu-north-1",
    "ap-northeast-1",
    "ap-northeast-2",
    "ap-northeast-3",
    "ap-southeast-1",
    "ap-southeast-2",
    "ap-south-1",
    "sa-east-1",
    "ca-central-1",
    "cn-north-1",
    "cn-northwest-1",
    "cn-northwest-1",
];

#[derive(Debug)]
pub struct PathAccountKeyRegion {
    pub path: PathBuf,
    pub account_key: String,
    pub region: String,
}

impl PathAccountKeyRegion {
    pub fn parse(path: &Path) -> error_stack::Result<Self, ErrorStack> {
        let basename = path.file_name().unwrap().to_string_lossy();

        // Bail out if dir does not have @
        if !basename.contains('@') {
            error_stack::bail!(ErrorStack::InvalidDirectoryFormat(basename.to_string()));
        }

        // split on first -
        let mut parts = basename.splitn(2, '@');
        let account_key = parts.next().unwrap_or("").to_string();
        let region = parts.next().unwrap_or("").to_string();

        Ok(PathAccountKeyRegion {
            path: path.to_path_buf(),
            account_key,
            region,
        })
    }
}

pub struct Repo {
    pub repo_mutex: tokio::sync::Mutex<git2::Repository>,
    pub repo_path: PathBuf,
}

impl std::fmt::Debug for Repo {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "Repo {{ path: {:?} }}", self.repo_path.display())
    }
}

#[derive(Debug)]
pub struct Application {
    pub is_ace_server: bool,
    pub executable_path: PathBuf,
    pub repo: Repo,
    pub hostname: String,

    pub ace_server_bin_path: PathBuf,
    pub bin_executable_path: PathBuf,
    pub bin_path: PathBuf,
    pub ca_path: PathBuf,
    pub data_path: PathBuf,
    pub data_pathcache_matches: bool,
    pub data_pathcache_path: PathBuf,
    pub data_terraform_output_path: PathBuf,
    pub data_version_file_path: PathBuf,
    pub docker_path: PathBuf,
    pub etc_path: PathBuf,
    pub etc_account_file_path: PathBuf,
    pub etc_config_file_path: PathBuf,
    pub init_needed: bool,
    pub local_file_path: PathBuf,
    pub nebula_path: PathBuf,
    pub nebula_cert_path: PathBuf,
    pub nebula_tar_url: String,
    pub packer_path: PathBuf,
    pub path_accountkey_region: PathAccountKeyRegion,
    pub path: PathBuf,
    pub ssh_bin_path: PathBuf,
    pub vpn_path: PathBuf,

    pub temp_path: PathBuf,
    pub terraform_path: PathBuf,
    pub terraform_state_path: PathBuf,

    pub ace_db_app: ace_db::App,

    pub packer_version: String,
    pub packer_zip_url: String,
    pub packer_zip_file: String,
    pub packer_bin_path: PathBuf,
    pub packer_config_path: PathBuf,

    pub terraform_version: String,
    pub terraform_zip_url: String,
    pub terraform_zip_file: String,
    pub terraform_bin_path: PathBuf,

    pub easytls_version: String,
    pub easytls_bin_url: String,
    pub easytls_bin_path: PathBuf,

    pub gocryptfs_tar_url: String,
    pub gocryptfs_bin_path: PathBuf,

    /// This is the CIPHERDIR
    pub secure_encrypted: PathBuf,
    pub secure_mountpoint: PathBuf,
}

pub fn load_env() -> error_stack::Result<Application, ErrorStack> {
    let executable_path = std::env::current_exe()
        .change_context(ErrorStack::CouldNotDeterminePathOfCurrentExecutable)?;

    let git2_repo =
        git2::Repository::discover(".").change_context(ErrorStack::CouldNotFindGitRepo)?;

    let repo_path = git2_repo.path().to_path_buf();

    let path = match git2_repo.workdir() {
        Some(path) => path.to_path_buf(),
        None => {
            error_stack::bail!(ErrorStack::CouldNotLoadPathFromRepo)
        }
    };

    let repo_wrapper = Repo {
        repo_mutex: tokio::sync::Mutex::new(git2_repo),
        repo_path,
    };

    let hostname = hostname::get()
        .change_context(ErrorStack::GetHostname)?
        .to_string_lossy()
        .into_owned();

    let path_accountkey_region = PathAccountKeyRegion::parse(&path)?;

    // Directory paths:
    let bin_path = path.join("bin");
    let ca_path = path.join("ca");
    let data_path = path.join("data");
    let docker_path = path.join("docker");
    let etc_path = path.join("etc");
    let packer_path = path.join("packer");
    let secure_encrypted = path.join(".secure");
    let secure_mountpoint = path.join("secure");
    let temp_path = path.join("tmp");
    let terraform_path = path.join("terraform");
    let vpn_path = path.join("vpn");

    // Specific binary paths:
    let ace_server_bin_path = bin_path.join("ace-server");
    let bin_executable_path = bin_path.join("ace");
    let easytls_bin_path = bin_path.join("easytls");
    let gocryptfs_bin_path = bin_path.join("gocryptfs");
    let nebula_cert_path = bin_path.join("nebula-cert");
    let nebula_path = bin_path.join("nebula");
    let packer_bin_path = bin_path.join("packer");
    let terraform_bin_path = bin_path.join("terraform");

    // Specific file paths:
    let ace2_private_key_path = data_path.join("ace2_key");
    let ace2_public_key_path = data_path.join("ace2_key.pub");
    let data_pathcache_path = data_path.join("pathcache");
    let data_terraform_output_path = data_path.join("terraform.output.json");
    let data_version_file_path = data_path.join("version.txt");
    let etc_account_file_path = etc_path.join("account.toml");
    let etc_config_file_path = etc_path.join("config.toml");
    let local_file_path = secure_mountpoint.join("LOCAL.env");
    let packer_config_path = packer_path.join("packer.pkr.hcl");
    let ssh_bin_path = PathBuf::from("/usr/bin/ssh");
    let terraform_state_path = terraform_path.join("terraform.tfstate");

    // Misc:
    let data_pathcache_matches = if data_pathcache_path.exists() {
        let contents =
            fs::read_to_string(&data_pathcache_path).change_context(ErrorStack::ReadFile)?;
        let contents = contents.trim().to_string();
        contents == path.display().to_string()
    } else {
        false
    };

    let is_ace_server = PathBuf::from("/etc/ace").exists();

    let init_needed = !(local_file_path.exists()
        && ace2_private_key_path.exists()
        && ace2_public_key_path.exists()
        && bin_path.exists()
        && data_path.exists()
        && docker_path.exists()
        && etc_account_file_path.exists()
        && etc_config_file_path.exists()
        && etc_path.exists()
        && packer_path.exists()
        && secure_mountpoint.exists()
        && secure_encrypted.exists()
        && temp_path.exists()
        && terraform_path.exists()
        && vpn_path.exists());

    let ace_db_app = ace_db::App::init(init_needed);

    Ok(Application {
        is_ace_server,
        executable_path,
        repo: repo_wrapper,
        hostname,

        ace_server_bin_path,
        bin_executable_path,
        bin_path,
        ca_path,
        data_path,
        data_pathcache_matches,
        data_pathcache_path,
        data_terraform_output_path,
        data_version_file_path,
        docker_path,
        etc_path,
        etc_account_file_path,
        etc_config_file_path,
        init_needed,
        local_file_path,
        nebula_path,
        nebula_cert_path,
        nebula_tar_url: NEBULA_TAR_URL.to_string(),
        packer_path,
        packer_config_path,
        path_accountkey_region,
        path,
        ssh_bin_path,
        secure_encrypted,
        secure_mountpoint,
        temp_path,
        terraform_path,
        terraform_state_path,
        vpn_path,

        ace_db_app,

        packer_version: PACKER_VERSION.to_string(),
        packer_zip_url: PACKER_ZIP_URL.to_string(),
        packer_zip_file: PACKER_ZIP_URL.split('/').last().unwrap().to_string(),
        packer_bin_path,

        terraform_version: TERRAFORM_VERSION.to_string(),
        terraform_zip_url: TERRAFORM_ZIP_URL.to_string(),
        terraform_zip_file: TERRAFORM_ZIP_URL.split('/').last().unwrap().to_string(),
        terraform_bin_path,

        easytls_version: EASYTLS_VERSION.to_string(),
        easytls_bin_url: EASYTLS_BIN_URL.to_string(),
        easytls_bin_path,

        gocryptfs_tar_url: GOCRYPTFS_TAR_URL.to_string(),
        gocryptfs_bin_path,
    })
}

impl Application {
    /// Will return true if any tracked or untracked files are uncommitted
    pub async fn is_dirty(&self, pathspec: &str) -> error_stack::Result<bool, ErrorStack> {
        let mut opts = git2::StatusOptions::new();
        opts.include_untracked(true);
        opts.exclude_submodules(true);
        opts.recurse_untracked_dirs(true);
        opts.pathspec(pathspec);

        let repo_lock = self.repo.repo_mutex.lock().await;
        let statuses = repo_lock
            .statuses(Some(&mut opts))
            .change_context(ErrorStack::LockStatuses)?;

        for status in statuses.iter() {
            if status.status() != git2::Status::CURRENT {
                return Ok(true);
            }
        }

        Ok(false)
    }

    pub fn create_temp_dir(&self, basename: &str) -> error_stack::Result<PathBuf, ErrorStack> {
        let now = chrono::Utc::now();
        let timestamp = now.format("%Y%m%d%H%M%S");

        let dir_name = format!("{}/{}-{}", self.temp_path.display(), timestamp, basename);
        let dir_path = PathBuf::from(dir_name);

        fs::create_dir_all(&dir_path).change_context(ErrorStack::CreateTempDir)?;

        Ok(dir_path)
    }

    pub fn is_secure_gocryptfs_initialized(&self) -> bool {
        let config_file = self.secure_encrypted.join("gocryptfs.conf");
        config_file.exists()
    }

    pub fn is_secure_mounted(&self) -> std::io::Result<bool> {
        let mount_point = self.secure_mountpoint.to_string_lossy();

        use std::io::BufRead;
        let file = File::open("/proc/mounts")?;
        let reader = BufReader::new(file);

        for line in reader.lines() {
            let line = line?;
            let fields: Vec<&str> = line.split_whitespace().collect();

            // Check if the mount point matches
            if fields.len() > 2 && fields[1] == mount_point {
                return Ok(true);
            }
        }

        Ok(false)
    }
}

pub fn compute_sha256_file_path(file_path: &PathBuf) -> Option<String> {
    if !file_path.exists() {
        return None;
    }

    let file = match File::open(file_path) {
        Ok(file) => file,
        Err(e) => {
            eprintln!("Error opening {}: {}", file_path.display(), e);
            return None;
        }
    };

    let mut buf_reader = BufReader::new(file);
    let mut hasher = Sha256::new();
    let mut buffer = [0; 1024];

    loop {
        let read = match buf_reader.read(&mut buffer) {
            Ok(r) => r,
            Err(e) => {
                eprintln!("Error reading {}: {}", file_path.display(), e);
                return None;
            }
        };

        if read == 0 {
            break;
        }

        hasher.update(&buffer[..read]);
    }

    let result = hasher.finalize();
    Some(format!("{result:x}"))
}
