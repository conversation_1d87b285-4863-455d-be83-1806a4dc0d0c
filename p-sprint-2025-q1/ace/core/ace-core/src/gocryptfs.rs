use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    CheckMountStatus,
    ExecuteInit,
    ExecuteInfoCommand,
    ExecuteSecureMount,
    ExecuteSecureUnmount,
    InitMount,
    SecureAlreadyMounted,
    SecureAlreadyUnmounted,
    SecureMountFailed,
    SecureUnmountFailed,
}

/// Initializes a new gocryptfs filesystem.  Requires a password.
/// WIP - not public yet
fn secure_init(app: &crate::Application) -> error_stack::Result<(), ErrorStack> {
    println!("--- Setting Up Encrypted Filesystem in ./secure-gocrpytfs ---");
    println!("--- You must create a secure password and save it in the organization's password manager ---");
    println!("--- It will be required any time this server is restarted to perform advanced operations ---");
    println!();

    let mut cmd = std::process::Command::new(&app.gocryptfs_bin_path);
    cmd.arg("-init");
    cmd.arg("-plaintextnames");
    cmd.arg(&app.secure_encrypted);

    match cmd.status() {
        Ok(output) => output,
        Err(e) => {
            eprintln!("Error: {:?}", e);
            return Err(ErrorStack::ExecuteInit.into());
        }
    };

    Ok(())
}

pub fn secure_mount(app: &crate::Application) -> error_stack::Result<(), ErrorStack> {
    if !app.is_secure_gocryptfs_initialized() {
        secure_init(app).change_context(ErrorStack::InitMount)?;
    }

    if app
        .is_secure_mounted()
        .change_context(ErrorStack::CheckMountStatus)?
    {
        error_stack::bail!(ErrorStack::SecureAlreadyMounted)
    }

    println!("--- Mounting Encrypted Filesystem in ./secure ---");
    println!("--- You will be prompted for the secure password that was created during initialization ---");
    println!();

    let status = std::process::Command::new(&app.gocryptfs_bin_path)
        .arg("-nonempty")
        .arg(&app.secure_encrypted)
        .arg(&app.secure_mountpoint)
        .status()
        .change_context(ErrorStack::ExecuteSecureMount)?;

    if !status.success() {
        return Err(ErrorStack::SecureMountFailed.into());
    }

    Ok(())
}

pub fn secure_unmount(app: &crate::Application) -> error_stack::Result<(), ErrorStack> {
    if !app
        .is_secure_mounted()
        .change_context(ErrorStack::CheckMountStatus)?
    {
        error_stack::bail!(ErrorStack::SecureAlreadyUnmounted)
    }

    let mount_point = &app.secure_mountpoint;

    let status = std::process::Command::new("fusermount")
        .arg("-u")
        .arg(mount_point)
        .status()
        .change_context(ErrorStack::ExecuteSecureUnmount)?;

    if !status.success() {
        error_stack::bail!(ErrorStack::SecureUnmountFailed)
    }

    Ok(())
}

pub fn secure_info(app: &crate::Application) -> error_stack::Result<(), ErrorStack> {
    let status = std::process::Command::new(&app.gocryptfs_bin_path)
        .arg("-info")
        .arg(&app.secure_encrypted)
        .status()
        .change_context(ErrorStack::ExecuteInfoCommand)?;

    if !status.success() {
        error_stack::bail!(ErrorStack::ExecuteInfoCommand)
    }

    println!();
    if app
        .is_secure_mounted()
        .change_context(ErrorStack::CheckMountStatus)?
    {
        println!("MOUNTED");
    } else {
        println!("NOT MOUNTED");
    }

    Ok(())
}
