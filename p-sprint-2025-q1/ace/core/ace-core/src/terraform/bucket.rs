use super::Output;
use error_stack::ResultExt;
use garbage::{CNSL, JE};
use hcl::Block;
use serde_json::json;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    ConvertToHcl,
    GetConfig,
    GetCurrentAccount,
    SelectBuckets,
}

pub async fn generate(
    output: &mut Output,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<super::Output, ErrorStack> {
    let buckets = ace_graph::bucket::select(ace_graph::BucketFilter::All, ace_db_app)
        .await
        .change_context(ErrorStack::SelectBuckets)?;
    let config = ace_graph::config::get(&ace_graph::Config::EtcConfig, ace_db_app)
        .await
        .change_context_lazy(|| ErrorStack::GetConfig)?;

    let mut blocks: Vec<Block> = vec![];
    let mut ace2_bucket_output = Output::new("ace2-bucket");

    for bucket in buckets {
        // Pull out app name from bucket graphkey for resource name
        let app_name = match &bucket.graphkey {
            ace_graph::Bucket::App(app_gk, _) => match app_gk {
                ace_graph::App::Db(name) => name,
            },
            ace_graph::Bucket::Ace => &"".to_string(), // We don't care about it in this case - Ace bucket is handled separately
        };

        let bucket_name = &bucket.name;
        let bucket_resource = format!("app-{}-{}", app_name, bucket.suffix);
        let mut bucket_policy_statements = vec![];
        let mut has_public_access = false;

        // Handle the Ace Bucket
        if matches!(bucket.graphkey, ace_graph::Bucket::Ace) {
            let current_account = ace_graph::account::get(
                &ace_graph::Account::Db(config.account_key.clone()),
                ace_db_app,
            )
            .await
            .change_context_lazy(|| ErrorStack::GetCurrentAccount)?;

            let ace_bucket_name = format!("{}-{}-ace2", current_account.account_key, config.region);

            // Create Ace Bucket config here
            #[rustfmt::skip]
            output.write(&None, &CNSL!(r#"
                resource "aws_s3_bucket" "ace2_bucket" {
                    bucket = "#, JE!(&ace_bucket_name), r#"
        
                    tags = {
                        Source = "Terraform"
                        AccountKey = "#, JE!(&current_account.account_key), r#"
                    }
                   
                }
        
                resource "aws_s3_bucket_versioning" "ace2_bucket" {
                    bucket = aws_s3_bucket.ace2_bucket.id
        
                    versioning_configuration {
                        status = "Enabled"
                    }
                }
        
            "#));

            // Add the Ace bucket to the list of outputs
            ace2_bucket_output.add("arn", "aws_s3_bucket.ace2_bucket.arn");
            ace2_bucket_output.add("name", "aws_s3_bucket.ace2_bucket.id");
            continue;
        }

        // Other, normal buckets:
        blocks.push(hcl::block!(
            resource "aws_s3_bucket" (&bucket_resource) {
                bucket = bucket_name
                force_destroy = false
                tags = {
                    Name = bucket_name
                    Source = "Terraform"
                    AccountKey = (&config.account_key)
                }
            }
        ));

        blocks.push(hcl::block!(
            resource "aws_s3_bucket_versioning" (&bucket_resource) {
                bucket = bucket_name
                versioning_configuration {
                    status = "Enabled"
                }
            }
        ));

        // if there are any public_read values, create a statement to allow reading them all
        if !bucket.public_read.is_empty() {
            // make sure we turn on public access
            has_public_access = true;

            // note that the public_read items always start with "/" as per ace_db validation
            let resources: Vec<String> = bucket
                .public_read
                .iter()
                .map(|public_read| format!("arn:aws:s3:::{}{}", bucket_name, public_read))
                .collect();

            bucket_policy_statements.push(json!(
                {
                    "Action": [
                        "s3:GetObject"
                    ],
                    "Principal": "*",
                    "Effect": "Allow",
                    "Resource": resources
                }
            ));

            blocks.push(hcl::block!(
                resource "aws_s3_bucket_policy" (&bucket_resource) {
                    bucket = bucket_name
                    policy = (json!({
                        "Version": "2012-10-17",
                        "Statement": bucket_policy_statements
                    }).to_string())
                }
            ));
        }

        // add public access by policy if there is any public access
        if has_public_access {
            blocks.push(hcl::block!(
                resource "aws_s3_bucket_public_access_block" (&bucket_resource) {
                    bucket = bucket_name
                    block_public_acls = true
                    block_public_policy = false
                    ignore_public_acls = true
                    restrict_public_buckets = false
                }
            ));
        }
    }

    let body = hcl::Body::from_iter(blocks);
    let body_string = hcl::to_string(&body)
        .change_context(ErrorStack::ConvertToHcl)?
        .replace("$$$", "$");
    output.write(&None, &body_string);

    Ok(ace2_bucket_output)
}
