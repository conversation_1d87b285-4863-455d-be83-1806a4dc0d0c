use std::vec;

use super::Output;
use error_stack::ResultExt;
use garbage::{CNSL, JE};
use serde_json::json;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    ConvertToHcl,
    GetApps,
    SelectBuckets,
}

pub async fn generate(
    config: &ace_graph::config::Config,
    output: &mut Output,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<(), ErrorStack> {
    for app in ace_graph::app::select(&ace_graph::AppFilter::All, ace_db_app)
        .await
        .change_context(ErrorStack::GetApps)?
    {
        generate_one(config, &app, output, ace_db_app).await?;
    }

    //output.add_output_pair("key_names", key_names.join(sep));

    Ok(())
}

async fn generate_one(
    config: &ace_graph::config::Config,
    app: &ace_graph::app::App,
    output: &mut Output,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<(), ErrorStack> {
    let mut blocks = vec![];
    let main_resource = format!("app-{}", app.name);
    let role_name = format!("{}-{}-app-{}", config.account_key, config.region, app.name);
    let user_name = format!("{}-{}-app-{}", config.account_key, config.region, app.name);

    let mut iam_policy_statements = vec![];

    // Get buckets for this app (to create policy statements)
    let filter =
        ace_graph::BucketFilter::One(ace_graph::Bucket::App(app.graphkey.clone(), "".to_string()));

    for bucket in ace_graph::bucket::select(filter, ace_db_app)
        .await
        .change_context(ErrorStack::SelectBuckets)?
    {
        let bucket_name = &bucket.name;

        iam_policy_statements.push(json!(
            {
                "Action": [
                    "s3:ListBucket",
                    "s3:ListBucketMultipartUploads",
                    "s3:GetBucketLocation"
                ],
                "Effect": "Allow",
                "Resource": [
                    (format!("arn:aws:s3:::{bucket_name}"))
                ]
            }
        ));

        iam_policy_statements.push(json!(
            {
                "Action": [
                    "s3:PutObject",
                    "s3:GetObject",
                    "s3:DeleteObject",
                    "s3:AbortMultipartUpload",
                    "s3:ListMultipartUploadParts"
                ],
                "Effect": "Allow",
                "Resource": [
                    (format!("arn:aws:s3:::{bucket_name}/*"))
                ]
            }
        ));
    }

    // setup policy if it's not empty
    if !iam_policy_statements.is_empty() {
        blocks.push(hcl::block!(
            resource "aws_iam_policy" (&main_resource) {
                name = (&main_resource)
                policy = (json!({
                    "Version": "2012-10-17",
                    "Statement": iam_policy_statements
                }).to_string())
            }
        ));
    }

    // Create/write the iam_access_key block raw...because the hcl crate doesn't let you insert unquoted values...
    #[rustfmt::skip]
    output.write(&Some(app.name.clone()), &CNSL!(r#"
        resource "aws_iam_access_key" "#, JE!(main_resource), r#" {
            user = "#, JE!(user_name) , r#"
            depends_on = [aws_iam_user."#, (main_resource), r#"]
        }

    "#));

    // setup iam role
    blocks.push(hcl::block!(
        resource "aws_iam_role" (&main_resource) {
            name = (role_name)
            assume_role_policy = (json!({
                "Version": "2012-10-17",
                "Statement": [
                    {
                        "Action": "sts:AssumeRole",
                        "Principal": {
                            "Service": "ec2.amazonaws.com"
                        },
                        "Effect": "Allow",
                        "Sid": ""
                    }
                ]
            }).to_string())
            tags = {
                Name = (&role_name)
                Source = "Terraform"
                AccountKey = (&config.account_key)
            }
        }
    ));

    // create an instance profile for this role
    blocks.push(hcl::block!(
        resource "aws_iam_instance_profile" (&main_resource) {
            name = (&main_resource)
            role = (&role_name)
        }
    ));

    // attach policy to role if it's not empty
    if !iam_policy_statements.is_empty() {
        blocks.push(hcl::block!(
            resource "aws_iam_role_policy_attachment" (&main_resource) {
                role = (&role_name)
                policy_arn = (format!("$${{aws_iam_policy.{main_resource}.arn}}"))
            }
        ));
    }

    // create an iam user
    blocks.push(hcl::block!(
        resource "aws_iam_user" (&main_resource) {
            name = (&user_name)
            tags = {
                Name = (&user_name)
                Source = "Terraform"
                AccountKey = (&config.account_key)
            }
        }
    ));

    // attach policy to user if there is a policy
    if !iam_policy_statements.is_empty() {
        blocks.push(hcl::block!(
            resource "aws_iam_user_policy_attachment" (&main_resource) {
                user = (&user_name)
                policy_arn = (format!("$${{aws_iam_policy.{main_resource}.arn}}"))
            }
        ));
    }

    // add outputs for this user's key and secret
    blocks.push(hcl::block!(
        output (format!("{}-user-access-key-id", main_resource)) {
            value = (format!("$${{aws_iam_access_key.{main_resource}.id}}"))
            sensitive = true
        }
    ));
    blocks.push(hcl::block!(
        output (format!("{}-user-secret-access-key", main_resource)) {
            value = (format!("$${{aws_iam_access_key.{main_resource}.secret}}"))
            sensitive = true
        }
    ));

    let body = hcl::Body::from_iter(blocks);
    let body_string = hcl::to_string(&body)
        .change_context(ErrorStack::ConvertToHcl)?
        .replace("$$$", "$");
    output.write(&Some(app.name.clone()), &body_string);
    Ok(())
}
