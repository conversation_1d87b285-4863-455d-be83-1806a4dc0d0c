use super::Output;
use error_stack::ResultExt;
use garbage::{CNSL, JE};
use serde_json::json;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    GenerateInstallSSHKeyScript,
    GenerateOne,
    GetInstanceProfile,
    GetUbuntu2204DevboxAmi,
    SelectDevelopers,
}

pub async fn generate(
    config: &ace_graph::config::Config,
    output: &mut Output,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<(), ErrorStack> {
    for developer in ace_graph::developer::select(&ace_graph::DeveloperFilter::All, ace_db_app)
        .await
        .change_context(ErrorStack::SelectDevelopers)?
    {
        generate_one(config, &developer, output, ace_db_app)
            .await
            .change_context(ErrorStack::GenerateOne)?;
    }

    //output.add_output_pair("key_names", key_names.join(sep));

    Ok(())
}

async fn generate_one(
    config: &ace_graph::config::Config,
    developer: &ace_graph::developer::Developer,
    output: &mut Output,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<(), ErrorStack> {
    // Create local references for convenience below
    let ami_id = ace_graph::ami::get(
        ace_graph::Ami::Packer(ace_graph::Packer::Ubuntu2204Devbox),
        ace_db_app,
    )
    .await
    .change_context(ErrorStack::GetUbuntu2204DevboxAmi)?
    .ami_id;

    let _instance_profile = ace_graph::ins_profile::get(
        ace_graph::InstanceProfile::Instance(ace_graph::Instance::Developer(
            developer.graphkey.clone(),
        )),
        ace_db_app,
    )
    .await
    .change_context(ErrorStack::GetInstanceProfile)?;

    let account_key = &config.account_key;
    let region = &config.region;
    let role_resource = format!("developer-{}", developer.name);
    let role_name = format!("{}-{}-developer-{}", account_key, region, developer.name);
    let instance_profile_resource = format!("developer-{}", developer.name);
    let instance_profile_name = format!("{}-{}-developer-{}", account_key, region, developer.name);

    // Is this what we're leading up to?
    // (shouldn't technically the developer instance itself be called by it's serialized-dashed graphkey?)
    // let instance_profile_resource = instance_profile.graphkey.serialize_dashed();
    // let instance_profile_name = instance_profile.name.clone();

    let policy_name = format!("{}-{}-developer-{}", account_key, region, developer.name);
    let security_group_resource = format!("developer-{}", developer.name);
    let instance_resource = format!("developer-{}", developer.name);
    let public_domain_resource = format!("developer-{}-public", developer.name);
    let public_domain_wildcard_resource = format!("developer-{}-public-wildcard", developer.name);
    let private_domain_resource = format!("developer-{}-private", developer.name);
    let private_domain_wildcard_resource = format!("developer-{}-private-wildcard", developer.name);

    let security_group_name = format!("{}-developer-{}-sg", account_key, developer.name);

    let install_keys_script =
        crate::gen_install_keys::generate_script(ace_db_app, account_key.to_owned())
            .await
            .change_context(ErrorStack::GenerateInstallSSHKeyScript)?;

    #[rustfmt::skip]
    let user_data = CNSL!(r#"
        #!/bin/bash
        echo "Terraform: START USER DATA SCRIPT from Terraform"


        # set the hostname
        echo "#, shell_quote::sh::quote(format!("Terraform: Setting hostname to {}", &developer.private_hostname)).to_string_lossy(), r#"
        hostnamectl set-hostname "#, shell_quote::sh::quote(&developer.private_hostname).to_string_lossy(), r#"

        # Create app user 
        echo "Terraform: Creating app user..."
        useradd -m -s /bin/bash app
        echo "app ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers.d/app
        
        # copy keys from ubuntu user to app user
        mkdir -p /home/<USER>/.ssh
        cp /home/<USER>/.ssh/authorized_keys /home/<USER>/.ssh/authorized_keys
        "#, install_keys_script, r#"
        chown -R app:app /home/<USER>/.ssh
        chmod 700 /home/<USER>/.ssh
        chmod 600 /home/<USER>/.ssh/authorized_keys

        # Delete ubuntu user
        echo "Terraform: Deleting ubuntu user..."
        userdel -r ubuntu
        
                    
        echo "Terraform: END USER DATA SCRIPT from Terraform"
        EOF123
        "#);

    let policy_statement_list: Vec<serde_json::Value> = {
        let mut policy_statement_list = vec![];

        // We are not using ecr at the moment, but this is very painful to figure out, so leaving it here for reference.
        if false {
            policy_statement_list.push(json!({
                "Sid":"ecr-getauth",
                "Effect":"Allow",
                "Action":[
                "ecr:GetAuthorizationToken"
                ],
                "Resource":"*"
            }));

            policy_statement_list.push(json!({
                "Sid":"ecr-download",
                "Effect":"Allow",
                "Action":[
                "ecr:GetDownloadUrlForLayer",
                "ecr:BatchGetImage",
                "ecr:BatchCheckLayerAvailability"
                ],
                "Resource":"*"
            }));
        }

        // Add read/write for buckets listed in the developer's app list
        // This is defined in the app[bucket.____].developer_access field
        for app_name in developer.app_config_map.keys() {
            let app_gk = ace_graph::App::Db(app_name.clone());
            let developer_app = ace_graph::developer_app::get(
                &ace_graph::DeveloperApp::Db(developer.graphkey.clone(), app_gk),
                ace_db_app,
            )
            .await
            .change_context(ErrorStack::SelectDevelopers)?;

            for app_bucket in developer_app.developer_access_buckets {
                // add statement for bucket-level permissions
                policy_statement_list.push(json!({
                    "Effect": "Allow",
                    "Action": [
                        "s3:ListBucket",
                        "s3:ListBucketMultipartUploads",
                        "s3:GetBucketLocation",
                    ],
                    "Resource": format!("arn:aws:s3:::{}", app_bucket.bucket_name)
                }));

                // add a statement for object-level permissions
                policy_statement_list.push(json!({
                    "Effect": "Allow",
                    "Action": [
                        "s3:GetObject",
                        "s3:PutObject",
                        "s3:DeleteObject",
                        "s3:AbortMultipartUpload",
                        "s3:ListMultipartUploadParts"
                    ],
                    "Resource": format!("arn:aws:s3:::{}/*", app_bucket.bucket_name)
                }));
            }
        }
        policy_statement_list
    };

    #[rustfmt::skip]
    output.write(&Some(developer.name.to_string()), &CNSL!(r#"
        resource "aws_iam_role" "#, JE!(role_resource), r#" {
            name = "#, JE!(role_name), r#"
            assume_role_policy = <<EOF
            {
                "Version": "2012-10-17",
                "Statement": [
                    {
                        "Action": "sts:AssumeRole",
                        "Principal": {
                            "Service": "ec2.amazonaws.com"
                        },
                        "Effect": "Allow",
                        "Sid": ""
                    }
                ]
            }
            EOF
            
            "#, if !policy_statement_list.is_empty() { CNSL!(r#"
            inline_policy {
                name = "#, JE!(policy_name), r#"
                policy = jsonencode({
                    "Version": "2012-10-17",
                    "Statement": "#, JE!(policy_statement_list), r#"
                })
            }
            "# ) } else { CNSL!("") }, r#"
        }
        
        resource "aws_iam_instance_profile" "#, JE!(instance_profile_resource), r#" {
            name = "#, JE!(instance_profile_name), r#"
            role = aws_iam_role."#, role_resource, r#".id
        }        
        
        resource "aws_security_group" "#, JE!(security_group_resource), r#" {
            name        = "#, JE!(security_group_name), r#"
            description = "Allow SSH inbound traffic"
            vpc_id      = aws_vpc.vpc_main.id

            // Allow inbound SSH traffic from vpc
            ingress {
                from_port   = 22
                to_port     = 22
                protocol    = "tcp"
                cidr_blocks = [aws_vpc.vpc_main.cidr_block]
            }

            // Allow inbound SSH
            ingress {
                from_port   = 22
                to_port     = 22
                protocol    = "tcp"
                cidr_blocks = ["0.0.0.0/0"]
            }

            // Allow inbound HTTP
            ingress {
                from_port   = 80
                to_port     = 80
                protocol    = "tcp"
                cidr_blocks = ["0.0.0.0/0"]
            }

            // Allow inbound HTTPS
            ingress {
                from_port   = 443
                to_port     = 443
                protocol    = "tcp"
                cidr_blocks = ["0.0.0.0/0"]
            }

            // Allow inbound HTTPS on high order ports
            ingress {
                from_port   = 8000
                to_port     = 8999
                protocol    = "tcp"
                cidr_blocks = ["0.0.0.0/0"]
            }

            // Allow all outbound traffic
            egress {
                from_port   = 0
                to_port     = 0
                protocol    = "-1"
                cidr_blocks = ["0.0.0.0/0"]
            }
        

            tags = {
                Name = "#, JE!(security_group_name), r#"
                Source = "Terraform"
                AccountKey = "#, JE!(&account_key), r#"
            }
        }

        
        resource "aws_instance" "#, JE!(instance_resource), r#" {
            ami = "#, JE!(&ami_id), r#"
            instance_type = "#, JE!(&developer.instance_type), r#"
            key_name = aws_key_pair.ace2_key.key_name
            subnet_id = "#, format!("aws_subnet.{}.id", &developer.subnet.variant_to_name()), r#"
            vpc_security_group_ids = ["#, format!("aws_security_group.{}.id", &security_group_resource), r#"]
            
            iam_instance_profile = "#, format!("aws_iam_instance_profile.{}.name", &instance_profile_resource), r#"

            root_block_device {
                volume_size = "#, JE!(&developer.volume_size), r#" // size in GB
                volume_type = "gp3"
            }

            lifecycle {
                prevent_destroy = true
                ignore_changes = [ami, instance_type, key_name, subnet_id, ebs_block_device, root_block_device, user_data, availability_zone]
            }

            user_data = "#, JE!(&user_data), r#"


            tags = {
                Name = "#, JE!(&developer.private_hostname), r#"
                Source = "Terraform"
                AccountKey = "#, JE!(&account_key), r#"
            }
        }

        
        resource "aws_route53_record" "#, JE!(private_domain_resource), r#" {
            zone_id = aws_route53_zone.private-domain.zone_id
            name    = "#, JE!(&developer.dns_prefix), r#"
            type    = "A"
            ttl     = "300"
            records = ["#, format!("aws_instance.{}.private_ip", &instance_resource), r#"]
        }

        resource "aws_route53_record" "#, JE!(private_domain_wildcard_resource), r#" {
            zone_id = aws_route53_zone.private-domain.zone_id
            name    = "#, JE!(&developer.dns_wildcard_prefix), r#"
            type    = "A"
            ttl     = "300"
            records = ["#, format!("aws_instance.{}.private_ip", &instance_resource), r#"]
        }
        
        resource "aws_route53_record" "#, JE!(public_domain_resource), r#" {
            zone_id = aws_route53_zone.public-domain.zone_id
            name    = "#, JE!(&developer.dns_prefix), r#"
            type    = "A"
            ttl     = "300"
            records = ["#, format!("aws_instance.{}.public_ip", &instance_resource), r#"]
        }

        resource "aws_route53_record" "#, JE!(public_domain_wildcard_resource), r#" {
            zone_id = aws_route53_zone.public-domain.zone_id
            name    = "#, JE!(&developer.dns_wildcard_prefix), r#"
            type    = "A"
            ttl     = "300"
            records = ["#, format!("aws_instance.{}.public_ip", &instance_resource), r#"]
        }


    "#));

    Ok(())
}
