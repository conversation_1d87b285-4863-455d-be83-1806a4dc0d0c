use super::Output;
use error_stack::ResultExt;
use garbage::{CNSL, JE};

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    GenerateInstallSSHKeyScript,
    GetLatestUbuntu2204Ace2Ami,
}

pub async fn generate(
    config: &ace_graph::config::Config,
    output: &mut Output,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<(), ErrorStack> {
    let ami_id = ace_graph::ami::get(
        ace_graph::Ami::Packer(ace_graph::Packer::Ubuntu2204Ace2),
        ace_db_app,
    )
    .await
    .change_context(ErrorStack::GetLatestUbuntu2204Ace2Ami)?
    .ami_id;

    // Create local references for convenience below
    let account_key = &config.account_key;
    let server_name = &config.ace2_server_name;
    let server_name_tag = format!("{account_key}-ace2-instance");

    let install_keys_script =
        crate::gen_install_keys::generate_script(ace_db_app, account_key.to_owned())
            .await
            .change_context(ErrorStack::GenerateInstallSSHKeyScript)?;

    #[rustfmt::skip]
    let user_data = CNSL!(
        r#"
        #!/bin/bash
        echo "Terraform: START USER DATA SCRIPT from Terraform"

        # set the hostname
        echo "#, shell_quote::sh::quote(format!("Terraform: Setting hostname to {}", &server_name)).to_string_lossy(), r#"
        hostnamectl set-hostname "#, shell_quote::sh::quote(server_name).to_string_lossy(), r#"

        # Create app user 
        echo "Terraform: Creating app user..."
        useradd -m -s /bin/bash app
        echo "app ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers.d/app

        # copy keys from ubuntu user to app user
        mkdir -p /home/<USER>/.ssh
        cp /home/<USER>/.ssh/authorized_keys /home/<USER>/.ssh/authorized_keys
        "#, install_keys_script, r#"
        chown -R app:app /home/<USER>/.ssh
        chmod 700 /home/<USER>/.ssh
        chmod 600 /home/<USER>/.ssh/authorized_keys
        chmod 600 /home/<USER>/.ssh/authorized_keys2

        # Delete ubuntu user
        echo "Terraform: Deleting ubuntu user..."
        userdel -r ubuntu
        
        echo "Terraform: Creating postgresql user and database for app..."
        sudo -u postgres createuser -s app
        sudo -u postgres createdb -O app app
        
        echo "Terraform: END USER DATA SCRIPT from Terraform"
        EOF123
        "#);

    #[rustfmt::skip]
    output.write(&None, &CNSL!(r#"
        resource "aws_security_group" "ace2_sg" {
            name        = "#, JE!(format!("{}-ace2-sg", &account_key)), r#"
            description = "Allow SSH inbound traffic"
            vpc_id      = aws_vpc.vpc_main.id

            // Allow inbound SSH traffic from private network
            ingress {
                from_port   = 22
                to_port     = 22
                protocol    = "tcp"
                cidr_blocks = ["10.0.0.0/8"]
            }

            // Allow inbound HTTPS traffic from private network
            ingress {
                from_port   = 443
                to_port     = 443
                protocol    = "tcp"
                cidr_blocks = ["10.0.0.0/8"]
            }

            // Allow inbound ICMP traffic from private network
            ingress {
                from_port   = -1
                to_port     = -1
                protocol    = "icmp"
                cidr_blocks = ["10.0.0.0/8"]
            }

            // Allow inbound SSH traffic from my ip
            ingress {
                from_port   = 22
                to_port     = 22
                protocol    = "tcp"
                cidr_blocks = ["#, JE!(&config.my_ipnetwork), r#"]
            }

            // allow inbound SSH traffic
            ingress {
                from_port   = 22
                to_port     = 22
                protocol    = "tcp"
                cidr_blocks = "#, JE!(&config.ace_instance_allow_ssh_from), r#"
            }

            // Allow all outbound traffic
            egress {
                from_port   = 0
                to_port     = 0
                protocol    = "-1"
                cidr_blocks = ["0.0.0.0/0"]
            }

            tags = {
                Name = "#, JE!(format!("{}-ace2-sg", &account_key)), r#"
                Source = "Terraform"
                AccountKey = "#, JE!(&account_key), r#"
            }
        }

        // Add elastic ip for ace2 instance
        resource "aws_eip" "ace2_eip" {
            domain = "vpc"
            tags = {
                Name = "#, JE!(format!("{}-ace2-eip", &account_key)), r#"
                Source = "Terraform"
                AccountKey = "#, JE!(&account_key), r#"
            }
        }
        
        resource "aws_instance" "ace2_instance" {
            ami = "#, JE!(&ami_id), r#"
            instance_type = "t3.small"
            key_name = aws_key_pair.ace2_key.key_name
            subnet_id = aws_subnet.sn-ace.id
            vpc_security_group_ids = [aws_security_group.ace2_sg.id]
            
            lifecycle {
                prevent_destroy = true
                ignore_changes = [ami, instance_type, key_name, subnet_id, ebs_block_device, root_block_device, user_data, availability_zone]
            }
            
            root_block_device {
                volume_size = "#, JE!(&config.ace_instance_volume_size), r#" // size in GB
                volume_type = "gp3"
            }
            
            user_data = "#, JE!(&user_data), r#"

            tags = {
                Name = "#, JE!(&server_name_tag), r#"
                Source = "Terraform"
                AccountKey = "#, JE!(&account_key), r#"
            }
        }

        // associate elastic ip with ace2 instance
        resource "aws_eip_association" "ace2_eip" {
            allocation_id = aws_eip.ace2_eip.id
            instance_id   = aws_instance.ace2_instance.id
        }

        // Create a route53 record for the ace2 instance public ip
        resource "aws_route53_record" "ace2_private_domain" {
            zone_id = aws_route53_zone.private-domain.zone_id
            name    = "#, JE!(&config.ace2_server_name_prefix), r#"
            type    = "A"
            ttl     = "300"
            records = [aws_instance.ace2_instance.private_ip]
        }

        // Create a route53 record for the ace2 instance public ip
        resource "aws_route53_record" "ace2_public_domain" {
            zone_id = aws_route53_zone.public-domain.zone_id
            name    = "#, JE!(&config.ace2_server_name_prefix), r#"
            type    = "A"
            ttl     = "300"
            records = [aws_eip.ace2_eip.public_ip]
        }
    "#));

    output.add("id", "aws_instance.ace2_instance.id");
    output.add("public_ip", "aws_eip.ace2_eip.public_ip");
    output.add("private_ip", "aws_instance.ace2_instance.private_ip");
    output.add(
        "public_domain",
        "aws_route53_record.ace2_public_domain.fqdn",
    );
    output.add(
        "private_domain",
        "aws_route53_record.ace2_private_domain.fqdn",
    );

    // Add the ami_id to the output
    output.add("ami_id", "aws_instance.ace2_instance.ami");

    Ok(())
}
