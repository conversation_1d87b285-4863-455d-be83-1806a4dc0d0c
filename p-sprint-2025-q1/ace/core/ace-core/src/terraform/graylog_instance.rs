use super::Output;
use error_stack::ResultExt;
use garbage::{CNSL, JE};

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    GenerateInstallSSHKeysScript,
    GetUbuntu22_04DockerAmi,
}

pub async fn generate(
    config: &ace_graph::config::Config,
    output: &mut Output,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<(), ErrorStack> {
    let Some(graylog) = &config.graylog else {
        #[rustfmt::skip]
        output.write(&None, &CNSL!(r#"
            # No graylog configuration found... not generating configuration.
        "#));
        return Ok(());
    };

    let ami_id = ace_graph::ami::get(
        ace_graph::Ami::Packer(ace_graph::Packer::Ubuntu2204Docker),
        ace_db_app,
    )
    .await
    .change_context(ErrorStack::GetUbuntu22_04DockerAmi)?
    .ami_id;

    // Create local references for convenience below
    let account_key = &config.account_key;
    let server_name = &graylog.server_name;
    let server_name_tag = format!("{account_key}-graylog-instance");

    let install_keys_script =
        crate::gen_install_keys::generate_script(ace_db_app, account_key.to_owned())
            .await
            .change_context(ErrorStack::GenerateInstallSSHKeysScript)?;

    #[rustfmt::skip]
    let user_data = CNSL!(r#"
        #!/bin/bash
        echo "Terraform: START USER DATA SCRIPT from Terraform"


        # set the hostname
        echo "#, shell_quote::sh::quote(format!("Terraform: Setting hostname to {}", &server_name)).to_string_lossy(), r#"
        hostnamectl set-hostname "#, shell_quote::sh::quote(server_name).to_string_lossy(), r#"

        # Create app user 
        echo "Terraform: Creating app user..."
        useradd -m -s /bin/bash app
        echo "app ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers.d/app
        
        # copy keys from ubuntu user to app user
        mkdir -p /home/<USER>/.ssh
        cp /home/<USER>/.ssh/authorized_keys /home/<USER>/.ssh/authorized_keys
        "#, install_keys_script, r#"
        chown -R app:app /home/<USER>/.ssh
        chmod 700 /home/<USER>/.ssh
        chmod 600 /home/<USER>/.ssh/authorized_keys

        # Delete ubuntu user
        echo "Terraform: Deleting ubuntu user..."
        userdel -r ubuntu
        
                    
        echo "Terraform: END USER DATA SCRIPT from Terraform"
        EOF123
        "#);

    #[rustfmt::skip]
    output.write(&None, &CNSL!(r#"
        resource "aws_security_group" "graylog_sg" {
            name        = "#, JE!(format!("{}-graylog-sg", &account_key)), r#"
            description = "Allow SSH inbound traffic"
            vpc_id      = aws_vpc.vpc_main.id

            // Allow inbound SSH traffic from vpc
            ingress {
                from_port   = 22
                to_port     = 22
                protocol    = "tcp"
                cidr_blocks = [aws_vpc.vpc_main.cidr_block]
            }
            
            // Allow inbound SSH traffic from my ip
            ingress {
                from_port   = 22
                to_port     = 22
                protocol    = "tcp"
                cidr_blocks = ["#, JE!(&config.my_ipnetwork), r#"]
            }

            ingress {
                from_port   = 80
                to_port     = 80
                protocol    = "tcp"
                cidr_blocks = ["0.0.0.0/0"]
            }

            ingress {
                from_port   = 443
                to_port     = 443
                protocol    = "tcp"
                cidr_blocks = ["0.0.0.0/0"]
            }

            // Allow all outbound traffic
            egress {
                from_port   = 0
                to_port     = 0
                protocol    = "-1"
                cidr_blocks = ["0.0.0.0/0"]
            }
        

            tags = {
                Name = "#, JE!(format!("{}-graylog-sg", &account_key)), r#"
                Source = "Terraform"
                AccountKey = "#, JE!(&account_key), r#"
            }
        }

        
        resource "aws_instance" "graylog_instance" {
            ami = "#, JE!(&ami_id), r#"
            instance_type = "t3.micro"
            key_name = aws_key_pair.ace2_key.key_name
            subnet_id = aws_subnet.sn-ace.id
            vpc_security_group_ids = [aws_security_group.graylog_sg.id]

            lifecycle {
                prevent_destroy = true
                ignore_changes = [ami]
            }

            user_data = "#, JE!(&user_data), r#"

            tags = {
                Name = "#, JE!(&server_name_tag), r#"
                Source = "Terraform"
                AccountKey = "#, JE!(&account_key), r#"
            }
        }

        // Create a route53 record for the graylog instance public ip
        resource "aws_route53_record" "graylog_private_domain" {
            zone_id = aws_route53_zone.private-domain.zone_id
            name    = "graylog"
            type    = "A"
            ttl     = "300"
            records = [aws_instance.graylog_instance.private_ip]
        }

        // Create a route53 record for the graylog instance public ip
        resource "aws_route53_record" "graylog_public_domain" {
            zone_id = aws_route53_zone.public-domain.zone_id
            name    = "graylog"
            type    = "A"
            ttl     = "300"
            records = [aws_instance.graylog_instance.public_ip]
        }

    "#));

    output.add("id", "aws_instance.graylog_instance.id");
    output.add("public_ip", "aws_instance.graylog_instance.public_ip");
    output.add("private_ip", "aws_instance.graylog_instance.private_ip");
    output.add(
        "public_domain",
        "aws_route53_record.graylog_public_domain.fqdn",
    );
    output.add(
        "private_domain",
        "aws_route53_record.graylog_private_domain.fqdn",
    );

    // Add the ami_id to the output
    output.add("ami_id", "aws_instance.graylog_instance.ami");

    Ok(())
}
