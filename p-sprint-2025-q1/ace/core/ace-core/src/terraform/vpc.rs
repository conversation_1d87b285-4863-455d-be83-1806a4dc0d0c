use super::Output;
use error_stack::ResultExt;
use garbage::{CNSL, JE, JN};

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    GetPeercons,
}

pub async fn generate(
    config: &ace_graph::config::Config,
    output: &mut Output,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<(), ErrorStack> {
    let peercon_list = ace_graph::peercon::select(&ace_graph::PeerconFilter::All, ace_db_app)
        .await
        .change_context(ErrorStack::GetPeercons)?;

    // Create local references for convenience below
    let account_key = &config.account_key;
    let region = &config.region;
    let subnet16 = &config.cidr_block.to_string();

    // Get the first 2 parts off of the subnet
    let subnet_parts: Vec<&str> = subnet16.split('.').collect();
    let subnet22 = |n: u8| format!("{}.{}.{}.0/22", subnet_parts[0], subnet_parts[1], n);

    #[rustfmt::skip]
    output.write(&Some("core".to_string()), &CNSL!(r#"
        resource "aws_vpc" "vpc_main" {
            cidr_block = "#, JE!(&subnet16), r"
            tags = {
                Name = ", JE!(format!("{}-vpc-main", &account_key)), r#"
                Source = "Terraform"
                AccountKey = "#, JE!(&account_key), r#"
            }
        }

        resource "aws_internet_gateway" "igw_main" {
            vpc_id = aws_vpc.vpc_main.id
            tags = {
                Name = "#, JE!(format!("{}-igw-main", &account_key)), r#"
                Source = "Terraform"
                AccountKey = "#, JE!(&account_key), r"
            }
        }


    "));

    for (name, availability_zone, cidr_block_public, cidr_block_private) in [
        (
            "a",
            region.to_string().push('a'),
            subnet22(0),
            subnet22(100),
        ),
        (
            "b",
            region.to_string().push('b'),
            subnet22(4),
            subnet22(104),
        ),
        (
            "c",
            region.to_string().push('c'),
            subnet22(8),
            subnet22(108),
        ),
    ] {
        let sn_public = format!("sn-{name}");
        let sna_public = format!("sna-{name}");
        let rtb_public = format!("rtb-{name}");
        let sn_private = format!("sn-{name}-private");
        let sna_private = format!("sna-{name}-private");
        let rtb_private = format!("rtb-{name}-private");
        let ngw = format!("ngw-{name}-private");
        let eip_ngw = format!("eip-{ngw}");
        // Here you would call your write function, which would take a String
        //write(....);

        #[rustfmt::skip]
        output.write(&Some("sn-abc".to_string()), &CNSL!(r#"
            // Create public subnet
            resource "aws_subnet" "#, JE!(&sn_public), r"{
                vpc_id = aws_vpc.vpc_main.id
                cidr_block = ", JE!(&cidr_block_public), r"
                availability_zone = ", JE!(&availability_zone), r"
                map_public_ip_on_launch = true
                tags = {
                    Name = ", JE!(format!("{}-{}", &account_key, &sn_public)), r#"
                    Source = "Terraform"
                    AccountKey = "#, JE!(&account_key), r#"
                }
            }

            // Create route tables for public subnet
            resource "aws_route_table" "#, JE!(&rtb_public), r#" {
                vpc_id = aws_vpc.vpc_main.id
                route {
                    cidr_block = "0.0.0.0/0"
                    gateway_id = aws_internet_gateway.igw_main.id
                }

                "#, JN!(&peercon_list, |peercon| CNSL!(r#"
                    route {
                        cidr_block = "#, JE!(&peercon.remote.cidr_block), r#"
                        vpc_peering_connection_id = "#, JE!(&peercon.peering_connection_id), r#"
                    }
                "#)), r"
                    
                tags = {
                    Name = ", JE!(format!("{}-{}", &account_key, &rtb_public)), r#"
                    Source = "Terraform"
                    AccountKey = "#, JE!(&account_key), r#"
                }
            }

            // Associate the Public Subnet with the Public Route Table
            resource "aws_route_table_association" "#, JE!(&sna_public), r" {
                subnet_id = aws_subnet.", &sn_public, r".id
                route_table_id = aws_route_table.", &rtb_public, r".id
            }

        "));

        if config.create_nat_gateways {
            output.write(
                &Some("nat-gateways".to_string()),
                &CNSL!(
                    r#"
                // Allocate an elastic IP for the NAT gateway
                resource "aws_eip" "#,
                    JE!(&eip_ngw),
                    r#" {
                    domain = "vpc"

                    tags = {
                        Name = "#,
                    JE!(format!("{}-{}", &account_key, &eip_ngw)),
                    r#"
                        Source = "Terraform"
                        AccountKey = "#,
                    JE!(&account_key),
                    r#"
                    }
                }
                
                // Create a NAT gateway in the public subnet
                resource "aws_nat_gateway" "#,
                    JE!(&ngw),
                    r" {
                    allocation_id = aws_eip.",
                    &eip_ngw,
                    r".id
                    subnet_id = aws_subnet.",
                    &sn_public,
                    r".id

                    tags = {
                        Name = ",
                    JE!(format!("{}-{}", &account_key, &ngw)),
                    r#"
                        Source = "Terraform"
                        AccountKey = "#,
                    JE!(&account_key),
                    r#"
                    }
                }
                
                // Create private subnet
                resource "aws_subnet" "#,
                    JE!(&sn_private),
                    r"{
                    vpc_id = aws_vpc.vpc_main.id
                    cidr_block = ",
                    JE!(&cidr_block_private),
                    r"
                    availability_zone = ",
                    JE!(&availability_zone),
                    r"
                    map_public_ip_on_launch = false
                    tags = {
                        Name = ",
                    JE!(format!("{}-{}", &account_key, &sn_private)),
                    r#"
                        Source = "Terraform"
                        AccountKey = "#,
                    JE!(&account_key),
                    r#"
                    }
                }

                // Create route tables for private subnet
                resource "aws_route_table" "#,
                    JE!(&rtb_private),
                    r#" {
                    vpc_id = aws_vpc.vpc_main.id
                    route {
                        cidr_block = "0.0.0.0/0"
                        nat_gateway_id = aws_nat_gateway."#,
                    &ngw,
                    r".id
                    }

                    tags = {
                        Name = ",
                    JE!(format!("{}-{}", &account_key, &rtb_private)),
                    r#"
                        Source = "Terraform"
                        AccountKey = "#,
                    JE!(&account_key),
                    r#"
                    }
                }

                // Associate the Private Subnet with the Private Route Table
                resource "aws_route_table_association" "#,
                    JE!(&sna_private),
                    r" {
                    subnet_id = aws_subnet.",
                    &sn_private,
                    r".id
                    route_table_id = aws_route_table.",
                    &rtb_private,
                    r".id
                }

            "
                ),
            );
        }
    }

    for (name, cidr_block, availability_zone, mappubip) in [
        ("ace", subnet22(12), region.to_string().push('b'), true),
        (
            "temporal",
            subnet22(248),
            region.to_string().push('b'),
            true,
        ),
        ("vpn", subnet22(252), region.to_string().push('b'), true),
    ] {
        let sn = "sn-".to_owned() + name;
        let rtb = "rtb-".to_owned() + name;
        let sna = "sna-".to_owned() + name;

        #[rustfmt::skip]
        output.write(&Some("sn-other".to_string()), &CNSL!(r#"
            resource "aws_subnet" "#, JE!(&sn), r"{
                vpc_id = aws_vpc.vpc_main.id
                cidr_block = ", JE!(&cidr_block), r"
                availability_zone = ", JE!(&availability_zone), r"
                map_public_ip_on_launch = ", JE!(&mappubip), r"
                tags = {
                    Name = ", JE!(format!("{}-{}", &account_key, &sn)), r#"
                    Source = "Terraform"
                    AccountKey = "#, JE!(&account_key), r#"
                }
            }
    
            // Create route tables for each private subnet
            resource "aws_route_table" "#, JE!(&rtb), r#" {
                vpc_id = aws_vpc.vpc_main.id
                route {
                    cidr_block = "0.0.0.0/0"
                    gateway_id = aws_internet_gateway.igw_main.id
                }

                "#, JN!(&peercon_list, |peercon| CNSL!(r"
                    route {
                        cidr_block = ", JE!(&peercon.remote.cidr_block), r"
                        vpc_peering_connection_id = ", JE!(&peercon.peering_connection_id), r"
                    }
                ")), r"                    
                
                tags = {
                    Name = ", JE!(format!("{}-{}", &account_key, &rtb)), r#"
                    Source = "Terraform"
                    AccountKey = "#, JE!(&account_key), r#"
                }
            }
    
            // Associate the private subnets with the private route tables
            resource "aws_route_table_association" "#, JE!(&sna), r" {
                subnet_id = aws_subnet.", &sn, r".id
                route_table_id = aws_route_table.", &rtb, r".id
            }
    
        "));
    }

    output.add("id", "aws_vpc.vpc_main.id");
    output.add("cidr_block", "aws_vpc.vpc_main.cidr_block");
    output.add("igw_id", "aws_internet_gateway.igw_main.id");
    output.add("sn_a_id", "aws_subnet.sn-a.id");
    output.add("sn_b_id", "aws_subnet.sn-b.id");
    output.add("sn_c_id", "aws_subnet.sn-c.id");
    Ok(())
}
