use super::Output;
use ace_graph::GraphKeyExt;
use error_stack::ResultExt;
use garbage::{CN, CNSL, JE, JN};
use granite::dash;

#[allow(dead_code)]
#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    GenerateInstallSSHKeyScript,
    GenerateOne,
    GetInstances,
    AmiNotFound(String),
    VolumeSizeNotSpecified,
}

pub async fn generate(
    config: &ace_graph::config::Config,
    output: &mut Output,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<(), ErrorStack> {
    for instance in ace_graph::ins::select(&ace_graph::InstanceFilter::All, ace_db_app)
        .await
        .change_context(ErrorStack::GetInstances)?
    {
        // For now, only generate files for Etc instances to avoid duplicate Ace/Vpn/Graylog or Developer instances.
        // To be updated in the future.
        if matches!(
            instance.graphkey,
            ace_graph::Instance::Db(_) | ace_graph::Instance::App(_)
        ) {
            generate_one(config, &instance, output, ace_db_app)
                .await
                .change_context(ErrorStack::GenerateOne)?;
        }
    }

    //output.add_output_pair("key_names", key_names.join(sep));

    Ok(())
}

async fn generate_one(
    config: &ace_graph::config::Config,
    instance: &ace_graph::ins::Instance,
    output: &mut Output,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<(), ErrorStack> {
    // Create local references for convenience below

    // Will automatically get the latest ami if it is from packer.manifest.json.
    let ami_id = ace_graph::ami::get(instance.ami_graphkey.clone(), ace_db_app)
        .await
        .change_context(ErrorStack::AmiNotFound(instance.ami_graphkey.serialize()))?
        .ami_id;

    let account_key = &config.account_key;
    let security_group_resource = format!("{}_sg", dash(&instance.name));
    let instance_resource = format!("{}_instance", dash(&instance.name));
    let public_domain_resource = format!("{}_public_domain", dash(&instance.name));
    let private_domain_resource = format!("{}_private_domain", dash(&instance.name));

    let security_group_name = format!("{}-{}-sg", account_key, dash(&instance.name));
    let server_name = format!(
        "{}.{}.{}",
        instance.name, config.region, config.private_domain_name
    );

    let eip_resource = format!("{}_eip", dash(&instance.name));
    let eip_tag = format!("{}-{}-eip", account_key, dash(&instance.name));
    let eip_association_resource = format!("{}_eip_association", dash(&instance.name));

    let install_keys_script =
        crate::gen_install_keys::generate_script(ace_db_app, account_key.to_owned())
            .await
            .change_context(ErrorStack::GenerateInstallSSHKeyScript)?;

    // Since we are (at this time) skipping any instances defined in etc, if the volume size is missing = problem!!
    let instance_volume_size = match &instance.volume_size {
        ace_graph::ins::VolumeSize::DeterminedByAws => {
            error_stack::bail!(ErrorStack::VolumeSizeNotSpecified)
        }
        ace_graph::ins::VolumeSize::Specific(size) => size,
    };

    #[rustfmt::skip]
    let user_data = CNSL!(r#"
        #!/bin/bash
        echo "Terraform: START USER DATA SCRIPT from Terraform"


        # set the hostname
        echo "#, shell_quote::sh::quote(format!("Terraform: Setting hostname to {}", &server_name)).to_string_lossy(), r#"
        hostnamectl set-hostname "#, shell_quote::sh::quote(&server_name).to_string_lossy(), r#"

        # Create app user 
        echo "Terraform: Creating app user..."
        useradd -m -s /bin/bash app
        echo "app ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers.d/app
        
        # copy keys from ubuntu user to app user
        mkdir -p /home/<USER>/.ssh
        cp /home/<USER>/.ssh/authorized_keys /home/<USER>/.ssh/authorized_keys
        "#, install_keys_script, r#"
        chown -R app:app /home/<USER>/.ssh
        chmod 700 /home/<USER>/.ssh
        chmod 600 /home/<USER>/.ssh/authorized_keys

        # Delete ubuntu user
        echo "Terraform: Deleting ubuntu user..."
        userdel -r ubuntu
        
                    
        echo "Terraform: END USER DATA SCRIPT from Terraform"
        EOF123
        "#);

    #[rustfmt::skip]
    output.write(&Some(dash(&instance.name)), &CNSL!(r#"
        resource "aws_security_group" "#, JE!(security_group_resource), r#" {
            name        = "#, JE!(security_group_name), r#"
            description = "Allow SSH inbound traffic"
            vpc_id      = aws_vpc.vpc_main.id

            // Allow inbound SSH traffic from vpc
            ingress {
                from_port   = 22
                to_port     = 22
                protocol    = "tcp"
                cidr_blocks = [aws_vpc.vpc_main.cidr_block]
            }

            // Allow inbound SSH traffic from my ip
            ingress {
                from_port   = 22
                to_port     = 22
                protocol    = "tcp"
                cidr_blocks = ["#, JE!(&config.my_ipnetwork), r#"]
            }

            "#, JN!(&instance.ingress, |ingress| CN!(r#"
            ingress {
                from_port   = "#, JE!(ingress.ports.to_aws_from_port()), r#"
                to_port     = "#, JE!(ingress.ports.to_aws_to_port()), r#"
                protocol    = "#, JE!(ingress.protocol), r#"
                cidr_blocks = "#, JE!(ingress.cidr_blocks), r#"
            }
            "#)), r#"

            // Allow all outbound traffic
            egress {
                from_port   = 0
                to_port     = 0
                protocol    = "-1"
                cidr_blocks = ["0.0.0.0/0"]
            }
        

            tags = {
                Name = "#, JE!(security_group_name), r#"
                Source = "Terraform"
                AccountKey = "#, JE!(&account_key), r#"
            }
        }

        
        resource "aws_instance" "#, JE!(instance_resource), r#" {
            ami = "#, JE!(&ami_id), r#"
            instance_type = "#, JE!(&instance.instance_type), r#"
            key_name = aws_key_pair.ace2_key.key_name
            subnet_id = "#, format!("aws_subnet.{}.id", &instance.subnet), r#"
            vpc_security_group_ids = ["#, format!("aws_security_group.{}.id", &security_group_resource), r#"]
            
            root_block_device {
                volume_size = "#, JE!(&instance_volume_size), r#" // size in GB
                volume_type = "gp3"
            }

            lifecycle {
                prevent_destroy = true
                ignore_changes = [ami, instance_type, key_name, subnet_id, ebs_block_device, root_block_device, user_data, availability_zone]
            }

            user_data = "#, JE!(&user_data), r#"


            tags = {
                Name = "#, JE!(&server_name), r#"
                Source = "Terraform"
                AccountKey = "#, JE!(&account_key), r#"
            }
        }

        // Create a route53 record for the openvpn instance public ip
        resource "aws_route53_record" "#, JE!(private_domain_resource), r#" {
            zone_id = aws_route53_zone.private-domain.zone_id
            name    = "#, JE!(&instance.name), r#"
            type    = "A"
            ttl     = "300"
            records = ["#, format!("aws_instance.{}.private_ip", &instance_resource), r#"]
        }

        // Create a route53 record for the openvpn instance public ip
        resource "aws_route53_record" "#, JE!(public_domain_resource), r#" {
            zone_id = aws_route53_zone.public-domain.zone_id
            name    = "#, JE!(&instance.name), r#"
            type    = "A"
            ttl     = "300"
            records = ["#, format!("aws_instance.{}.public_ip", &instance_resource), r#"]
        }

    "#));

    if instance.use_elastic_ip {
        #[rustfmt::skip]
        output.write(&Some(dash(&instance.name)), &CNSL!(r#"
            resource "aws_eip" "#, JE!(eip_resource), r#" {
                domain = "vpc"
                tags = {
                    Name = "#, JE!(eip_tag), r#"
                    Source = "Terraform"
                    AccountKey = "#, JE!(&account_key), r#"
                }
            }

            resource "aws_eip_association" "#, JE!(eip_association_resource), r#" {
                instance_id   = "#, format!("aws_instance.{}.id", &instance_resource), r#"
                allocation_id = "#, format!("aws_eip.{}.id", &eip_resource), r#"
            }
        "#));
    }

    Ok(())
}
