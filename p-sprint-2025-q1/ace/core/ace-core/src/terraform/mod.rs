use error_stack::ResultExt;
use garbage::{CNSL, JE, JN, STSL};
use std::collections::HashMap;
use std::{fs::File, io::Write};

mod ace2_instance;
mod ace2_key;
mod app;
mod brdst;
mod brsrc;
mod bucket;
mod developer;
mod dns;
mod ecr;
mod ecr_public;
mod graylog_instance;
mod instance;
mod keypair;
mod mediaproctor;
mod mysqldevimg;
mod openvpn_instance;
mod vpc;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    CreateFile,
    GenerateAce2Instance,
    GenerateApp,
    GenerateBrdst,
    GenerateBrsrc,
    GenerateBucket,
    GenerateDeveloper,
    GenerateEcrRepo,
    GeneratePublicEcrRepo,
    GenerateGraylogInstance,
    GenerateInstance,
    GenerateKeypair,
    GenerateMysqldevimg,
    GenerateOpenvpnInstance,
    GenerateVpc,
    ReadDirectory,
    RemoveFile,
    WriteFile,
}

pub struct Output {
    pub prefix: String,
    pub contentmap: HashMap<Option<String>, String>,
    pub outputs: Vec<(String, String)>,
}

// TODO: need support for vector or hashmap outputs.
impl Output {
    pub fn new(prefix: &str) -> Self {
        let prefix = prefix.to_string();
        let contentmap: HashMap<Option<String>, String> = HashMap::new();
        let outputs = Vec::new();
        Self {
            prefix,
            contentmap,
            outputs,
        }
    }

    pub fn write(&mut self, name: &Option<String>, content: &str) {
        // append content to hashmap value, or create

        // get mutable reference to hashmap value
        match self.contentmap.get_mut(name) {
            Some(value) => {
                value.push_str(content);
            }
            None => {
                self.contentmap.insert(name.clone(), content.to_string());
            }
        };
    }

    pub fn add(&mut self, key: &str, value: &str) {
        self.outputs.push((key.to_string(), value.to_string()));
    }

    pub fn save(
        &self,
        env: &crate::Application,
        names: &mut Vec<String>,
    ) -> error_stack::Result<(), ErrorStack> {
        for (name, content) in &self.contentmap {
            let name = match name {
                Some(name) => format!("{}.{}.tf", &self.prefix, &name),
                None => format!("{}.tf", &self.prefix),
            };

            let path = env.terraform_path.join(&name);

            names.push(name);

            println!("Writing to {}", path.display());

            let mut file = File::create(&path).change_context(ErrorStack::CreateFile)?;
            file.write_all(content.as_bytes())
                .change_context(ErrorStack::WriteFile)?;
        }

        Ok(())
    }
}

pub async fn build(
    app: &crate::Application,
    config: &ace_graph::config::Config,
) -> error_stack::Result<(), ErrorStack> {
    let mut terraform_file = Output::new("terraform");
    let mut app_output = Output::new("app");
    let mut ace2_instance_output = Output::new("ace2-instance");
    let mut ace2_key_output = Output::new("ace2-key");
    let mut brsrc_list_output = Output::new("brsrcs");
    let mut brdst_list_output = Output::new("brdsts");
    let mut bucket_list_output = Output::new("buckets");
    let mut developer_list_output = Output::new("developers");
    let mut dns_output = Output::new("dns");
    let mut ecr_output = Output::new("ecr");
    let mut ecr_public_output = Output::new("ecr-public");
    let mut graylog_instance_output = Output::new("graylog");
    let mut instance_output = Output::new("instance");
    let mut keypair_output = Output::new("keypair");
    let mut mediaproctor_output = Output::new("mediaproctor");
    let mut mysqldevimg_output = Output::new("mysqldevimg");
    let mut openvpn_instance_output = Output::new("openvpn");
    let mut vpc_output = Output::new("vpc");
    let mut data_output: Output = Output::new("data");

    #[rustfmt::skip]
    terraform_file.write(&None, &STSL!(r"
        terraform {
        }
    "));

    ace2_instance::generate(config, &mut ace2_instance_output, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GenerateAce2Instance)?;
    ace2_key::generate(config, &mut ace2_key_output);
    app::generate(config, &mut app_output, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GenerateApp)?;
    brsrc::generate(config, &mut brsrc_list_output, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GenerateBrsrc)?;
    brdst::generate(&mut brdst_list_output, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GenerateBrdst)?;
    let ace2_bucket_output = bucket::generate(&mut bucket_list_output, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GenerateBucket)?;
    developer::generate(config, &mut developer_list_output, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GenerateDeveloper)?;
    dns::generate(config, &mut dns_output);
    ecr::generate(&mut ecr_output, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GenerateEcrRepo)?;
    ecr_public::generate(&mut ecr_public_output, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GeneratePublicEcrRepo)?;
    graylog_instance::generate(config, &mut graylog_instance_output, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GenerateGraylogInstance)?;
    instance::generate(config, &mut instance_output, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GenerateInstance)?;
    keypair::generate(config, &mut keypair_output, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GenerateKeypair)?;
    mediaproctor::generate(config, &mut mediaproctor_output);
    mysqldevimg::generate(&mut mysqldevimg_output, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GenerateMysqldevimg)?;
    openvpn_instance::generate(config, &mut openvpn_instance_output, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GenerateOpenvpnInstance)?;
    vpc::generate(config, &mut vpc_output, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GenerateVpc)?;

    #[rustfmt::skip]
    data_output.write(&None, &CNSL!(r#"
        resource "local_file" "output" {
            content = jsonencode({
                "#, JN!(&ace2_instance_output.outputs, |(k,v)| format!("{}_{} = {},\n", "ace2_instance", k, v)), r#"
                "#, JN!(&ace2_key_output.outputs, |(k,v)| format!("{}_{} = {},\n", "ace2_key", k, v)), r#"
                "#, JN!(ace2_bucket_output.outputs, |(k,v)| format!("{}_{} = {},\n", "ace2_bucket", k, v)), r#"
                "#, JN!(&app_output.outputs, |(k,v)| format!("{}_{} = {},\n", "app", k, v)), r#"
                "#, JN!(&brsrc_list_output.outputs, |(k,v)| format!("{}_{} = {},\n", "brsrc_list", k, v)), r#"
                "#, JN!(&brdst_list_output.outputs, |(k,v)| format!("{}_{} = {},\n", "brdst_list", k, v)), r#"
                "#, JN!(&bucket_list_output.outputs, |(k,v)| format!("{}_{} = {},\n", "bucket_list", k, v)), r#"
                "#, JN!(&developer_list_output.outputs, |(k,v)| format!("{}_{} = {},\n", "developer_list", k, v)), r#"
                "#, JN!(&dns_output.outputs, |(k,v)| format!("{}_{} = {},\n", "dns", k, v)), r#"
                "#, JN!(&ecr_output.outputs, |(k,v)| format!("{}_{} = {},\n", "ecr_list", k, v)), r#"
                "#, JN!(&ecr_public_output.outputs, |(k,v)| format!("{}_{} = {},\n", "ecr_public_list", k, v)), r#"
                "#, JN!(&graylog_instance_output.outputs, |(k,v)| format!("{}_{} = {},\n", "graylog_instance", k, v)), r#"
                "#, JN!(&instance_output.outputs, |(k,v)| format!("{}_{} = {},\n", "instances", k, v)), r#"
                "#, JN!(&keypair_output.outputs, |(k,v)| format!("{}_{} = {},\n", "keypair", k, v)), r#"
                "#, JN!(&mediaproctor_output.outputs, |(k,v)| format!("{}_{} = {},\n", "mediaproctor", k, v)), r#"
                "#, JN!(&mysqldevimg_output.outputs, |(k,v)| format!("{}_{} = {},\n", "mysqldevimg", k, v)), r#"
                "#, JN!(&openvpn_instance_output.outputs, |(k,v)| format!("{}_{} = {},\n", "openvpn_instance", k, v)), r#"
                "#, JN!(&vpc_output.outputs, |(k,v)| format!("{}_{} = {},\n", "vpc", k, v)), r#"
            })
            filename = "#, JE!(&app.data_terraform_output_path), r#"
            file_permission = "0600"
        }
    "#));

    let mut names: Vec<String> = Vec::new();

    terraform_file.save(app, &mut names)?;
    ace2_instance_output.save(app, &mut names)?;
    ace2_key_output.save(app, &mut names)?;
    app_output.save(app, &mut names)?;
    brsrc_list_output.save(app, &mut names)?;
    brdst_list_output.save(app, &mut names)?;
    bucket_list_output.save(app, &mut names)?;
    developer_list_output.save(app, &mut names)?;
    dns_output.save(app, &mut names)?;
    ecr_output.save(app, &mut names)?;
    ecr_public_output.save(app, &mut names)?;
    graylog_instance_output.save(app, &mut names)?;
    instance_output.save(app, &mut names)?;
    keypair_output.save(app, &mut names)?;
    mediaproctor_output.save(app, &mut names)?;
    mysqldevimg_output.save(app, &mut names)?;
    openvpn_instance_output.save(app, &mut names)?;
    vpc_output.save(app, &mut names)?;
    data_output.save(app, &mut names)?;

    // Delete all of the *.tf files in the terraform directory that were not just written to.
    let tf_files =
        std::fs::read_dir(&app.terraform_path).change_context(ErrorStack::ReadDirectory)?;
    let tf_ext = std::ffi::OsStr::new("tf");
    for file in tf_files {
        let file = file.change_context(ErrorStack::CreateFile)?;
        if file.path().extension() != Some(tf_ext) {
            continue;
        }
        if names.contains(&file.file_name().to_string_lossy().to_string()) {
            continue;
        }
        println!("Deleting {}", file.path().display());
        std::fs::remove_file(file.path()).change_context(ErrorStack::RemoveFile)?;
    }

    Ok(())
}
