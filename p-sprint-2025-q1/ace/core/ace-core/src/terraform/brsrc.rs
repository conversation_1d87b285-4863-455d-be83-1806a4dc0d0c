use super::Output;
use error_stack::ResultExt;
use garbage::{CNSL, JE};
use serde_json::json;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    GetBrsrcs,
}

pub async fn generate(
    config: &ace_graph::config::Config,
    output: &mut Output,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<(), ErrorStack> {
    for source in ace_graph::brsrc::select(&ace_graph::BrsrcFilter::All, ace_db_app)
        .await
        .change_context(ErrorStack::GetBrsrcs)?
    {
        let resource_name = format!("{}-brsrc", source.name);

        // Very important that this matches the code that generates it the source side
        let source_iam_role_name = format!(
            "{}-{}-{}-brsrc",
            config.account_key, config.region, source.name
        );

        let iam_policy_name = format!(
            "{}-{}-{}-brsrc",
            config.account_key, config.region, source.name
        );

        #[rustfmt::skip]
        output.write(&Some(source.name.to_string()), &CNSL!(r#"

            # ---
            # brsrc for 
            # account_key: "#, (config.account_key), r#"
            # name: "#, JE!(source.name), r#"
            # bucket: "#, (source.source.bucket), r#"

             
            resource "aws_iam_role" "#, JE!(resource_name), r#" {
                name = "#, JE!(source_iam_role_name), r#"
                
                assume_role_policy = jsonencode("#, JE!(json!(
                    {
                        "Version": "2012-10-17",
                        "Statement": [
                            {
                                "Action": "sts:AssumeRole",
                                "Principal": {
                                    "Service": "s3.amazonaws.com"
                                },
                                "Effect": "Allow",
                                "Sid": ""
                            },
                            {
                                "Effect": "Allow",
                                "Principal": {
                                    "Service": "batchoperations.s3.amazonaws.com"
                                },
                                "Action": "sts:AssumeRole"
                            }
                        ]
                    }
                )), r#")
            }
              
            resource "aws_iam_policy" "#, JE!(resource_name), r#" {
                name = "#, JE!(iam_policy_name), r#"
                description = "policy to allow replication"
                
                policy = jsonencode("#, JE!(json!(
                    {
                        "Version":"2012-10-17",
                        "Statement": [
                            {
                                "Effect":"Allow",
                                "Action":[
                                    "s3:PutInventoryConfiguration"
                                ],
                                "Resource":[
                                    source.source.bucket_arn
                                ]
                            },
                            {
                                "Effect":"Allow",
                                "Action":[
                                    "s3:GetReplicationConfiguration",
                                    "s3:ListBucket"
                                ],
                                "Resource":[
                                    source.source.bucket_arn
                                ]
                            },
                            {
                                "Effect":"Allow",
                                "Action":[
                                    "s3:GetObjectVersionForReplication",
                                    "s3:GetObjectVersionAcl",
                                    "s3:GetObjectVersionTagging"
                                ],
                                "Resource":[
                                    format!("{}/*", source.source.bucket_arn)
                                ]
                            },
                            {
                                "Effect":"Allow",
                                "Action":[
                                    "s3:ReplicateObject",
                                    "s3:ReplicateDelete",
                                    "s3:ReplicateTags"
                                ],
                                "Resource": format!("{}/*", source.target.bucket_arn)
                            },
                            {
                                "Effect":"Allow",
                                "Action":[
                                    "s3:InitiateReplication",
                                    "s3:GetObject",
                                    "s3:GetObjectVersion",
                                    "s3:PutObject"
                                ],
                                "Resource": format!("{}/*", source.source.bucket_arn)
                            },
                            {
                                "Effect":"Allow",
                                "Action":[
                                    "s3:GetReplicationConfiguration",
                                    "s3:PutInventoryConfiguration"
                                ],
                                "Resource": source.source.bucket_arn
                            }
                        ]
                    }
                )), r#")
            }
              
            resource "aws_iam_role_policy_attachment" "#, JE!(resource_name), r#" {
                role = aws_iam_role."#, resource_name, r#".name
                policy_arn = aws_iam_policy."#, resource_name, r#".arn
            }

            resource "aws_s3_bucket_replication_configuration" "#, JE!(resource_name), r#" {
                role   = aws_iam_role."#, resource_name, r#".arn
                bucket = "#, JE!(source.source.bucket), r#"
              
                rule {
                  id = "bdr"
              
                  status = "Enabled"
              
                  destination {
                    bucket        = "#, JE!(source.target.bucket_arn), r#"
                    storage_class = "STANDARD"
                  }
                }
              }
        "#));
    }

    //output.add_output_pair("key_names", key_names.join(sep));

    Ok(())
}
