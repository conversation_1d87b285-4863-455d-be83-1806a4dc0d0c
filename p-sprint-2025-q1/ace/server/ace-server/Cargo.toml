[package]
name = "ace-server"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html
[package.metadata.acp]
app.port = 3007
extends = ["ace-server-agent", "ace-server-api", "ace-server-dashboard", "ace-server-ssh", "ace-server-zero", "bux"]

[dependencies]
ace-core = { path = "../../core/ace-core" }
ace-graph = { path = "../../core/ace-graph" }
ace-types = { path = "../../shared/ace-types" }
ace-server-agent = { path = "../ace-server-agent" }
ace-server-api = { path = "../ace-server-api" }
ace-server-dashboard = { path = "../ace-server-dashboard" }
ace-server-ssh = { path = "../ace-server-ssh" }
ace-server-zero = { path = "../ace-server-zero" }
garbage = { path = "../../shared/garbage"}

approck = { workspace = true }
clap = { workspace = true, features = ["derive"]}
bux = { workspace = true }
granite = { workspace = true }

dotenv = { workspace = true }
maud = { workspace = true }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
tokio = { workspace = true, features = ["full"] }
toml = { workspace = true }
