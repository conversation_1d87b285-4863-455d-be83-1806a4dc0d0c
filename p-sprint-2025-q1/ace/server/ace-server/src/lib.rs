#[path = "libλ.rs"]
mod libλ;

pub mod web;

pub trait AppSystem: ace_server_zero::AppSystem {}

pub trait AppIdentity {}

pub trait Document: bux::document::Base {}

pub mod types {
    use ace_graph::GraphKeyExt;
    use approck::server::tls::{CertificateRequestState, TlsCertificateKeyPemPair};
    use granite::ResultExt;

    #[derive(Debug, serde::Deserialize)]
    pub struct AppConfig {
        pub webserver: approck::server::WebServerConfig,
    }
    impl AppConfig {
        pub async fn into_system(self) -> granite::Result<AppSystem> {
            Ok(AppSystem {
                webserver: self.webserver.into_system().await?,
                zero_system: ace_server_zero::ZeroSystem::init(),
            })
        }
    }

    #[derive(Debug)]
    pub enum AppIdentity {
        Anonymous,
    }

    pub struct AppSystem {
        pub webserver: approck::server::WebServerSystem,
        pub zero_system: ace_server_zero::ZeroSystem,
    }

    impl crate::AppIdentity for AppIdentity {}
    impl ace_server_agent::AppIdentity for AppIdentity {}
    impl ace_server_api::AppIdentity for AppIdentity {}
    impl ace_server_dashboard::AppIdentity for AppIdentity {}
    impl ace_server_ssh::AppIdentity for AppIdentity {}
    impl ace_server_zero::AppIdentity for AppIdentity {}

    impl crate::AppSystem for AppSystem {}
    impl ace_server_agent::AppSystem for AppSystem {}
    impl ace_server_api::AppSystem for AppSystem {}
    impl ace_server_dashboard::AppSystem for AppSystem {}
    impl ace_server_ssh::AppSystem for AppSystem {}
    impl ace_server_zero::AppSystem for AppSystem {
        fn zero_system(&self) -> &ace_server_zero::ZeroSystem {
            &self.zero_system
        }
    }

    impl approck::server::WebServerModule for AppSystem {
        fn webserver_system(&self) -> &approck::server::WebServerSystem {
            &self.webserver
        }

        async fn webserver_route(
            &'static self,
            req: approck::server::Request,
        ) -> granite::Result<approck::server::response::Response> {
            crate::libλ::router(self, req).await
        }

        async fn request_certificate(
            &self,
            server_name: &str,
        ) -> granite::Result<CertificateRequestState> {
            let app = self.zero_system.ace_core_app;

            let ace2_server_name = match ace_graph::config::get(
                &ace_graph::Config::EtcConfig,
                &app.ace_db_app,
            )
            .await
            {
                Ok(config) => config.ace2_server_name,
                Err(e) => {
                    return Err(granite::Error::new(granite::ErrorType::Unexpected)
                        .add_context(e.to_string()))
                }
            };

            if server_name == ace2_server_name {
                // load the certificate from the filesystem
                let tlscert_path = app.ace_db_app.data_path.join(format!(
                    "tls.{}.cert.pem",
                    ace_graph::TlsCert::Ace.serialize_dashed()
                ));
                let tlscert_key_path = app.ace_db_app.data_path.join(format!(
                    "tls.{}.key.pem",
                    ace_graph::TlsCert::Ace.serialize_dashed()
                ));

                if !tlscert_path.exists() {
                    return Err(granite::Error::new(granite::ErrorType::Unexpected)
                        .add_context(format!("TLS Certificate file not found: {tlscert_path:?}")));
                }

                let cert_contents = std::fs::read_to_string(&tlscert_path).amend(|e| {
                    e.add_context(format!("Error reading {}", &tlscert_path.display()))
                })?;
                let key_contents = std::fs::read_to_string(&tlscert_key_path).amend(|e| {
                    e.add_context(format!("Error reading {}", &tlscert_key_path.display()))
                })?;

                let pair = TlsCertificateKeyPemPair {
                    cert_pem: cert_contents,
                    key_pem: key_contents,
                }
                .try_into_key_pair()
                .amend(|e| e.add_context("Error parsing TLS certificate".to_string()))?;

                return Ok(CertificateRequestState::Ready(pair.into()));
            }

            // default is None available
            Ok(approck::server::tls::CertificateRequestState::None)
        }
    }

    bux::document! {
        pub struct Document {}

        impl crate::Document for Document {}
        impl ace_server_dashboard::Document for Document {}
    }
}

pub async fn init(app: &'static crate::types::AppSystem) {
    println!("init ace-server!");

    let ace_core_app = app.zero_system.ace_core_app;

    // If the local file exists, read it with dotenv
    if ace_core_app.local_file_path.exists() {
        dotenv::from_path(&ace_core_app.local_file_path).expect("Failed to apply LOCAL.env file");
    }

    ace_server_agent::AppSystem::init(app);
}

async fn authenticate_request(
    _app: &'static crate::types::AppSystem,
    _req: &approck::server::Request,
) -> granite::Result<types::AppIdentity> {
    Ok(types::AppIdentity::Anonymous)
}
