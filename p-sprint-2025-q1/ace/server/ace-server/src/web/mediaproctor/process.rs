#[approck::http(POST /mediaproctor/process; AUTH None; return JSON;)]
pub mod page {
    #[granite::gtype(rust: j2t e2j;)]
    pub struct PostJson {
        name: String,
        secret: String,
        job: JsonValue,
        tags: HashMap<String, String>,
    }

    pub async fn request(app: App, form: <PERSON><PERSON><PERSON>) -> Result<Response> {
        let mediaproctor = super::super::auth(&form.name, &form.secret, app).await?;

        let run_response = match ace_core::mediaproctor::run_mp_process(
            &mediaproctor,
            &form.job,
            &form.tags,
            &app.zero_system().ace_core_app.ace_db_app,
        )
        .await
        {
            Ok(r) => r,
            Err(e) => return Err(granite::from_error_stack!(e)),
        };

        let rval = match serde_json::to_string(&run_response) {
            Ok(v) => v,
            Err(e) => {
                return Err(
                    approck::Error::new(approck::ErrorType::Unexpected).add_context(e.to_string())
                )
            }
        };

        Ok(Response::JSON(rval.into()))
    }
}
