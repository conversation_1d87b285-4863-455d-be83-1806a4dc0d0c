#[approck::http(POST /mediaproctor/terminate-instance; AUTH None; return JSON;)]
pub mod terminate_instance {

    #[granite::gtype(rust: j2t e2j;)]
    pub struct PostJson {
        name: String,
        secret: String,
        instance_id: String,
    }

    pub async fn request(app: App, form: PostJ<PERSON>) -> Result<Response> {
        let mediaproctor = super::super::auth(&form.name, &form.secret, app).await?;

        let terminate_result = match ace_core::mediaproctor::terminate_instance(
            &mediaproctor,
            &form.instance_id,
        )
        .await
        {
            Ok(rval) => rval,
            Err(e) => return Err(granite::from_error_stack!(e)),
        };

        let rval = match serde_json::to_string(&terminate_result) {
            Ok(v) => v,
            Err(e) => {
                return Err(approck::Error::new(approck::ErrorType::ProcessError)
                    .add_context(e.to_string()));
            }
        };

        Ok(Response::JSON(rval.into()))
    }
}
