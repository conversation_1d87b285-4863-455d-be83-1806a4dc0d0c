#[approck::http(POST /mediaproctor/instance-status; AUTH None; return JSON;)]
pub mod instance_status {
    #[granite::gtype(rust: j2t e2j;)]
    pub struct PostJson {
        name: String,
        secret: String,
        instance_ids: Vec<String>,
    }

    pub async fn request(app: App, form: PostJson) -> Result<Response> {
        let mediaproctor = super::super::auth(&form.name, &form.secret, app).await?;

        let run_response =
            match ace_core::mediaproctor::check_instance_status(&mediaproctor, &form.instance_ids)
                .await
            {
                Ok(run_response) => run_response,
                Err(e) => return Err(granite::from_error_stack!(e)),
            };

        let rval = match serde_json::to_string(&run_response) {
            Ok(v) => v,
            Err(e) => {
                return Err(approck::Error::new(approck::ErrorType::ProcessError)
                    .add_context(e.to_string()))
            }
        };

        Ok(Response::JSON(rval.into()))
    }
}
