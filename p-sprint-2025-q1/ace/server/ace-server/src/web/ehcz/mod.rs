pub mod config;
pub mod foo;
pub mod mysqldevimg;

pub(crate) async fn authenticate_developer(
    username: String,
    password: String,
    app: &impl crate::AppSystem,
) -> granite::Result<ace_graph::developer::Developer> {
    // Get current developer (based off login information)
    let developer = match ace_graph::developer::get(
        &ace_graph::Developer::Db(username),
        &app.zero_system().ace_core_app.ace_db_app,
    )
    .await
    {
        Ok(developer) => developer,
        Err(e) => {
            return Err(approck::Error::new(approck::ErrorType::Authorization)
                .add_context(format!("{e:#?}")));
        }
    };

    // check the password
    if developer.secret != password {
        return Err(approck::Error::new(approck::ErrorType::Authorization)
            .add_context("User not authorized!"));
    }

    Ok(developer)
}
