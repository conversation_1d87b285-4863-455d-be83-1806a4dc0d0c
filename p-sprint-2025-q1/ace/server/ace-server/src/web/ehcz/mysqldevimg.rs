#[approck::http(GET /ehcz/mysqldevimg/{pc:String}; AUTH None; return Bytes|Empty;)]
pub mod page_with_capture {
    pub async fn request(app: App, path: Path, auth_basic: AuthBasic) -> Result<Response> {
        let (username, password) = auth_basic;

        match crate::web::ehcz::authenticate_developer(username, password, app).await {
            Ok(_developer) => match super::mysqldevimg::request(app, Some(path.pc)).await {
                Ok(super::mysqldevimg::Response::Bytes(bytes)) => Ok(Response::Bytes(bytes)),
                Err(e) => Err(approck::Error::new(approck::ErrorType::Unexpected).add_context(e)),
            },
            // TODO: handle this error better?
            Err(_e) => {
                let response = Empty {
                    status: approck::StatusCode::UNAUTHORIZED,
                    ..Default::default()
                };
                return Ok(Response::Empty(response));
            }
        }
    }
}

#[approck::http(GET /ehcz/mysqldevimg; AUTH None; return Bytes;)]
pub mod mysqldevimg {

    pub async fn request(app: App, app_id: Option<String>) -> Result<Response> {
        let app_name = match app_id {
            Some(app_id) => app_id,
            None => {
                return Err(approck::Error::new(approck::ErrorType::Unexpected)
                    .add_context("app_id is None"))
            }
        };

        let mysqldevimg_list = match ace_graph::mysqldevimg::select(
            &ace_graph::MysqlDevImgFilter::All,
            &app.zero_system().ace_core_app.ace_db_app,
        )
        .await
        {
            Ok(mysqldevimg_list) => mysqldevimg_list,
            Err(e) => return Err(granite::from_error_stack!(e)),
        };

        // find the mysqldevimg for this developer
        let mysqldevimg = match mysqldevimg_list.iter().find(|a| a.name == app_name) {
            Some(mysqldevimg) => mysqldevimg,
            None => {
                return Err(granite::from_error_stack!());
            }
        };

        let file_path = &mysqldevimg.sql_path;

        let contents = tokio::fs::read(file_path).await?;
        let rval = Bytes::new(contents);

        Ok(Response::Bytes(rval))
    }
}
