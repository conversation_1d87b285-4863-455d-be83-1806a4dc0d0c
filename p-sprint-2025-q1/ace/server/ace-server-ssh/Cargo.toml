[package]
name = "ace-server-ssh"
version = "0.1.0"
edition = "2021"


[package.metadata.acp]
module = {}
extends = []

[dependencies]
ace-core = { path = "../../core/ace-core" }
ace-graph = { path = "../../core/ace-graph" }
ace-server-dashboard = { path = "../../server/ace-server-dashboard" }
ace-server-zero = { path = "../../server/ace-server-zero" }
ace-types = { path = "../../shared/ace-types" }
garbage = { path = "../../shared/garbage"}

approck = { workspace = true }
bux = { workspace = true }
glob.workspace = true
granite = { workspace = true }
error-stack = { workspace = true }

chrono = { workspace = true }
indexmap = { workspace = true }
maud = { workspace = true }
tokio = { workspace = true, features = ["full"] }
http = { workspace = true }
sha2.workspace = true
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
toml = { workspace = true }
uuid = { workspace = true, features = ["v4"] }
