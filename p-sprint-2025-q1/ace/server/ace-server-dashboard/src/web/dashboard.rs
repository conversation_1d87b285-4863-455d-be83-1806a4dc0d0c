#[approck::http(GET /dashboard; AUTH None; return HTML|WebSocketUpgrade;)]
pub mod page {
    use ace_graph::GraphKeyExt;
    use ace_server_zero::HostState;

    pub async fn request(app: App, req: Request, ui: Document) -> Result<Response> {
        let ace_db_app = &app.zero_system().ace_core_app.ace_db_app;

        if req.is_upgrade() {
            let upgrade = req
                .upgrade_to_websocket(
                    move |socket| async move { self::websocket(app, socket).await },
                )
                .await;

            match upgrade {
                Ok(Some(response)) => return Ok(Response::WebSocketUpgrade(response)),
                Err(error) => return Err(error),
                _ => {}
            }
        }

        let canonical_hosts_results =
            match ace_graph::host::select_result(&ace_graph::HostFilter::All, ace_db_app).await {
                Ok(hosts) => hosts,
                Err(e) => return Err(granite::from_error_stack!(e)),
            };

        let mut errs = vec![];
        let mut sorted_canonical_hosts = vec![];

        for canonical_host in canonical_hosts_results {
            match canonical_host {
                Ok(ch) => sorted_canonical_hosts.push(ch),
                Err(e) => errs.push(e),
            }
        }

        sorted_canonical_hosts.sort_by(|a, b| {
            a.graphkey
                .serialize_dashed()
                .cmp(&b.graphkey.serialize_dashed())
        });

        let now = chrono::Utc::now().timestamp();
        {
            let host_state_map = app.zero_system().host_states.read().await;

            #[rustfmt::skip]
            ui.add_body(maud::html! {
                table class="host-table" {
                    thead {
                        tr {
                            th { "Host GraphKey" }
                            th { "Root Disk Total Space (bytes)" }
                            th { "Root Disk Free Space (bytes)" }
                            th { "Total Memory (bytes)" }
                            th { "Used Memory (bytes)" }
                            th { "Time Since Last Message" }
                            th { "Last Message ID" }
                            th { "Status" }
                        }
                    }
                    tbody {
                        @for host in sorted_canonical_hosts {

                            // Check if the host is in the host_state_map (This shouldn't be such a big deal)
                            @match host_state_map.get(&host.graphkey){
                                Some(host) => {
                                    @let elapsed_time_string = super::format_duration(now - host.last_msg_timestamp);

                                    tr {
                                        td { (host.host_gk) }
                                        @match &host.last_known_status {
                                            HostState::Ok ( sysinfo ) => {
                                                @match &sysinfo.root_disk {
                                                    ace_types::DiskSpaceCheck_a2s::Ok{ total_space, free_space } => {
                                                        td { (total_space) }
                                                        td { (free_space) }
                                                    }
                                                    ace_types::DiskSpaceCheck_a2s::Error(err) => {
                                                        td colspan="2" { (err)}
                                                    }
                                                }
                                                td { (sysinfo.total_memory) }
                                                td { (sysinfo.used_memory) }
                                                td { (elapsed_time_string) }
                                                td { (host.last_msg_id) }
                                                td { "OK" }
                                            }
                                            HostState::Error(error_msg) => {
                                                td colspan="2" { (error_msg) }
                                                td { "N/A" }
                                                td { "N/A" }
                                                td { (elapsed_time_string) }
                                                td { (host.last_msg_id) }
                                                td { "Error: " (error_msg) }    // This may need tweaked.
                                            }
                                            HostState::Unknown => {
                                                td { "N/A" }
                                                td { "N/A" }
                                                td { "N/A" }
                                                td { "N/A" }
                                                td { "N/A" }
                                                td { "N/A" }
                                                td { "Unknown" }
                                            }
                                        }
                                    }
                                },
                                None => {
                                    // HostState::Unknown  // Not necessarily an error, it maybe hasn't sent a message yet.
                                    tr {
                                        td { (host.graphkey) }
                                        td { "N/A" }
                                        td { "N/A" }
                                        td { "N/A" }
                                        td { "N/A" }
                                        td { "N/A" }
                                        td { "N/A" }
                                        td { "Unknown" }
                                    }
                                }
                            }
                        }
                    }
                    style {
                        "table.host-table { width: 100%; border-collapse: collapse; margin: 20px 0; }"
                        "th, td { padding: 12px 15px; text-align: left; }"
                        "th { background-color: #f2f2f2; }"
                        "tr:nth-child(even) { background-color: #f9f9f9; }"
                        "td { border-bottom: 1px solid #dddddd; }"
                    }
                    // Header to separate Bad connection table:
                    thead {
                        tr {
                            th colspan="6" { "Bad Connections" }
                        }
                    }

                    table id="bad-conn-table" {
                        // Table for Bad connections:
                        thead {
                            tr {
                                th { "Graphkey String" }
                                th { "IP Address" }
                                th { "Timestamp" }
                                th { "Reason" }
                            }
                        }
                        tbody id="bad-conn-tbody" {
                        }
                    }
                    script {
                        (maud::PreEscaped(super::dashboard_js()))
                    }

                    // ace-graph error table:
                    thead {
                        tr {
                            th colspan="6" { "Other Errors" }
                        }
                    }
                    tbody {
                        @for err in errs {
                            tr {
                                td colspan="6" { (format!("{:#?}", err)) }
                            }
                        }
                    }
                }
            });
        }

        Ok(Response::HTML(ui.into()))
    }

    async fn websocket(
        app: &'static impl crate::AppSystem,
        mut websocket: WebSocket,
    ) -> granite::Result<()> {
        // get broadcast listener for data updates from the server:

        // Old: let mut dashboard_listener = app.zero_system().dashboard_broadcast_channel.subscribe();
        let mut dashboard_listener = {
            let mut dashboard_broadcast_channel =
                app.zero_system().dashboard_broadcast_state.write().await;

            // If the broadcast channel is initialized, subscribe to it.
            // If not, initialize it and subscribe to it.
            match &mut *dashboard_broadcast_channel {
                Some((broadcast_channel, num_listening_clients)) => {
                    *num_listening_clients += 1;

                    broadcast_channel.subscribe()
                }
                None => {
                    let (sender, _receiver) = tokio::sync::broadcast::channel(100);
                    *dashboard_broadcast_channel = Some((sender, 1));

                    if let Some((broadcast_channel, _)) = dashboard_broadcast_channel.as_mut() {
                        broadcast_channel.subscribe()
                    } else {
                        return Err(granite::Error::new(granite::ErrorType::Unexpected)
                            .add_context(
                                "Failed to initialize and subscribe to broadcast channel",
                            ));
                    }
                }
            }
        };

        loop {
            tokio::select! {
                // Messages on the server->this-client channel:
                msg = dashboard_listener.recv() => {
                    match msg {
                        Ok(msg) => {
                            websocket.send(msg.into()).await?;
                        }
                        Err(e) => {
                            println!("Error receiving message: {e}");
                        }
                    }
                }

                // Websocket hears items from the channel to send to the client
                msg = websocket.recv() => {
                    match msg {
                        Some(Ok(message)) => {
                            match message.into_data() {
                                WebSocketMessageData::Text(text) => {
                                    println!("Sending to Client: {}", text);
                                    websocket.send(text.into()).await?;
                                }
                                WebSocketMessageData::Close => {
                                    println!("Client closed connection - decrementing number of listening clients");

                                    // Update the number of listening clients for the dashboard broadcast channel
                                    let mut dashboard_broadcast_channel = app.zero_system().dashboard_broadcast_state.write().await;

                                    // Match on the broadcast channel to decrement the number of listening clients
                                    // If it is None (which shouldn't be possible) print an error message but otherwise do nothing.
                                    match &mut *dashboard_broadcast_channel {
                                        Some((_, num_listening_clients)) => {

                                            // If the number of listening clients is 1, set the broadcast channel to None.
                                            // (It would be decremented to zero anyway)
                                            if *num_listening_clients == 1 {
                                                *dashboard_broadcast_channel = None;
                                            } else {
                                                *num_listening_clients -= 1;
                                            }

                                        },
                                        None => {
                                            println!("Error: dashboard_broadcast_channel is None when it shouldn't be.");
                                        }
                                    }

                                    break Ok(());
                                }
                                _ => {
                                    println!("Something else is happening...");
                                }
                            }
                        }
                        Some(Err(e)) => {
                            println!("Error receiving message: {e}");
                        }
                        _ => {}
                    }
                }
            }
        }
    }
}

fn format_duration(elapsed: i64) -> String {
    // Convert elapsed time to u64
    let secs = elapsed as u64;

    let duration = std::time::Duration::from_secs(secs);
    format!("{:#?}", duration)
}

fn dashboard_js() -> String {
    use garbage::CNSL;

    let js = CNSL!(
        r#"
    let socket = new WebSocket(globalThis.location.href);

    socket.onopen = function(event) {
        console.log('WebSocket connection initialized');
    };

    socket.onmessage = function(event) {
        console.log('Received WebSocket message: ', event.data);
        const clientMessage = JSON.parse(event.data);

        if (clientMessage.update.BadConnection) {
            console.log('Processing BadConnection: ', clientMessage.update.BadConnection);
            updateBadConnectionsTable(clientMessage.update.BadConnection);
        }
    };

    function updateBadConnectionsTable(badConnection) {
        const tbody = document.getElementById('bad-conn-tbody');  // Targeting the specific tbody by id

        if (!tbody) {
            console.error('tbody element not found');
            return;
        }

        const newRow = document.createElement('tr');

        const gkStringCell = document.createElement('td');
        gkStringCell.textContent = badConnection.gk_string;
        newRow.appendChild(gkStringCell);

        const ipAddrCell = document.createElement('td');
        ipAddrCell.textContent = badConnection.ip_addr;
        newRow.appendChild(ipAddrCell);

        const timestampCell = document.createElement('td');
        const date = new Date(badConnection.timestamp * 1000);
        timestampCell.textContent = date.toLocaleString();
        newRow.appendChild(timestampCell);

        const reasonCell = document.createElement('td');
        reasonCell.textContent = formatBadConnectionReason(badConnection.reason);
        newRow.appendChild(reasonCell);

        tbody.insertBefore(newRow, tbody.firstChild);
    }

    function formatBadConnectionReason(reason) {
        if (typeof reason === 'string') return reason;

        if (reason.DuplicateHost) {
            return 'Duplicate Host';
        } else if (reason.InvalidGraphKey) {
            return `Invalid GraphKey: ${reason.InvalidGraphKey}`;
        } else if (reason.UnknownHost) {
            return `Unknown Host: ${reason.UnknownHost}`;
        } else if (reason.LockingIssue) {
            return `Locking Issue: ${reason.LockingIssue}`;
        }
        return 'Unknown Reason';
    }
    "#
    );

    js
}
