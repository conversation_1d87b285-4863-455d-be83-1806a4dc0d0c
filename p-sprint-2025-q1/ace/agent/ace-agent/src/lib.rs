pub mod actions;
pub mod aws;
pub mod config;
pub mod error;
pub mod install;
pub mod jobs;
pub mod uninstall;
pub mod upgrade;

use ace_types::Message_a2s;
use futures_util::sink::SinkExt;
use futures_util::StreamExt;
use std::path::PathBuf;
use tokio::net::TcpStream;
use tokio::sync::mpsc;
use tokio::time::{interval, Duration};
use tokio_tungstenite::MaybeTlsStream;

pub static AWS_REGIONS: &[&str] = &[
    "us-east-2",
    "us-east-1",
    "us-west-1",
    "us-west-2",
    "af-south-1",
    "ap-east-1",
    "ap-south-2",
    "ap-southeast-3",
    "ap-southeast-4",
    "ap-south-1",
    "ap-northeast-3",
    "ap-northeast-2",
    "ap-southeast-1",
    "ap-southeast-2",
    "ap-northeast-1",
    "ca-central-1",
    "eu-central-1",
    "eu-west-1",
    "eu-west-2",
    "eu-south-1",
    "eu-west-3",
    "eu-south-2",
    "eu-north-1",
    "eu-central-2",
    "il-central-1",
    "me-south-1",
    "me-central-1",
    "sa-east-1",
];

#[derive(Debug)]
pub enum WebSocketError {
    NoMessageReceived,
    ProblemReformattingUrl(String),
    ProblemTurningMessageIntoText(tungstenite::error::Error),
    ReceivedMessageIsNotText(tungstenite::protocol::Message),
    SerializingMessage(serde_json::Error),
    TokioTungsteniteError(tokio_tungstenite::tungstenite::Error),
}

#[derive(Clone, Debug)]
pub struct App {
    pub config_path: PathBuf,
    pub data_dir: PathBuf,
    pub executable_path: PathBuf,
    pub systemd_unit_path: PathBuf,
    pub hostname: String,

    /// https:// url to the ace server - must not end in `/`
    pub ace_url: String,

    /// Websocket endpoint (calculated from `ace_url`)
    pub wss_url: String,
    pub graph_key: String,
    pub ed25519_public_key: String,
}

#[derive(Debug)]
pub struct Websocket {
    pub ws: tokio_tungstenite::WebSocketStream<MaybeTlsStream<TcpStream>>,
}

impl Websocket {
    async fn connect(app: &'static App, sim_gk: &Option<String>) -> Option<Websocket> {
        // Determine url based off of whether we are using a simulated graphkey (pretending to be another agent)
        let wss_url = match sim_gk {
            Some(sim_gk) => {
                // Strip https off ace_url HERE
                let stripped_ace_url = match app.ace_url.strip_prefix("https://") {
                    None => {
                        eprintln!(
                            "Config option `ace_url` must start with `https://`: `{}`",
                            app.ace_url
                        );
                        std::process::exit(1);
                    }
                    Some(url) if url.ends_with('/') => {
                        eprintln!(
                            "Config option `ace_url` must not end with `/`: `{}`",
                            app.ace_url
                        );
                        std::process::exit(1);
                    }
                    Some(url) => url,
                };

                &format!("wss://{stripped_ace_url}/ace-agent/websocket?gk={sim_gk}")
            }
            None => &app.wss_url,
        };

        println!("Connecting to websocket: {wss_url}");

        match tokio_tungstenite::connect_async_with_config(wss_url, None, false).await {
            Ok((s, _r)) => {
                println!("Connected to websocket");
                Some(Websocket { ws: s })
            }
            Err(e) => {
                println!("Error connecting to websocket: {e:?}");
                None
            }
        }
    }

    async fn send(&mut self, msg: &ace_types::Message_a2s) -> Result<(), WebSocketError> {
        let serialized_msg = match serde_json::to_string(&msg) {
            Ok(msg) => msg,
            Err(e) => return Err(WebSocketError::SerializingMessage(e)),
        };

        match self.ws.send(serialized_msg.into()).await {
            Ok(_) => Ok(()),
            Err(e) => Err(WebSocketError::TokioTungsteniteError(e)),
        }
    }

    // Returns (a future) the instruction received from ace-server
    async fn next(&mut self) -> Result<ace_types::Message_s2a, WebSocketError> {
        match self.ws.next().await {
            Some(Ok(msg)) => {
                if msg.is_text() {
                    let text = match msg.into_text() {
                        Ok(text) => text,
                        Err(e) => return Err(WebSocketError::ProblemTurningMessageIntoText(e)),
                    };

                    let instruction = match serde_json::from_str::<ace_types::Message_s2a>(&text) {
                        Ok(instruction) => instruction,
                        Err(e) => return Err(WebSocketError::SerializingMessage(e)),
                    };

                    return Ok(instruction);
                }
                Err(WebSocketError::ReceivedMessageIsNotText(msg))
            }
            Some(Err(e)) => Err(WebSocketError::TokioTungsteniteError(e)),
            None => Err(WebSocketError::NoMessageReceived),
        }
    }

    async fn close(&mut self) {
        match self.ws.close(None).await {
            Ok(_) => (),
            Err(e) => println!("Error closing websocket: {e:?}"),
        };
    }
}

pub struct JobConnector<S2A, A2S> {
    /// Send messages TO the job
    pub tx_s2a: mpsc::UnboundedSender<S2A>,
    /// Receive messages FROM the job
    pub rx_a2s: mpsc::UnboundedReceiver<A2S>,
    /// Backup queue for messages if there is a problem with `rx_a2s`
    pub tx_a2s: mpsc::UnboundedSender<A2S>,
}

pub async fn run(app: &'static App, simulated_gk: Option<String>) {
    let mut ping_server_interval = interval(Duration::from_secs(12));
    let (main_sender, mut main_receiver) = mpsc::unbounded_channel();

    let mut ws0: Option<Websocket> = None;
    let mut delay_interval = 0;

    // this spawns the system info job and returns a JobConnector to talk to it
    let mut system_info = crate::jobs::system_info::run(app).await;

    loop {
        if ws0.is_none() {
            if delay_interval > 0 {
                println!("Delaying connection to websocket");
                tokio::time::sleep(Duration::from_secs(delay_interval)).await;
            }
            ws0 = Websocket::connect(app, &simulated_gk).await;
        }

        // connected to websocket
        #[allow(clippy::collapsible_else_if)]
        if let Some(ws1) = ws0.as_mut() {
            delay_interval = 0;
            tokio::select! {
                _ = ping_server_interval.tick() => {
                    let ts = chrono::Utc::now().timestamp();
                    let msg_id = uuid::Uuid::new_v4().to_string();

                    let a2s_ping = ace_types::Message_a2s {
                        msg_id,
                        job: ace_types::Job_a2s::Ping,
                        timestamp: ts,
                        msg_type: ace_types::Message_Type::Prompt,
                    };

                    match main_sender.send(a2s_ping) {
                        Ok(_) => (),
                        Err(e) => println!("Error sending task message to central agent: {e:?}"),
                    };
                }

                // Where messages are received from the server TO the agent...
                msg = ws1.next() => {
                    match msg {
                        Ok(s2a_message) => {
                            println!("Received from ace-server: {s2a_message:?}");

                            // Handle instructions + spawn tasks
                            match s2a_message.job {
                                ace_types::Job_s2a::Ping => {
                                    let task_channel = main_sender.clone();
                                    tokio::spawn(async move {
                                        respond_to_ping(task_channel, s2a_message).await;
                                    });
                                }
                                ace_types::Job_s2a::Pong => (), // Nothing to do here
                                ace_types::Job_s2a::NotAuthorized => {
                                    println!("Not authorized to perform this action");
                                    // Just break - the server has already closed the connection.
                                    break;
                                },
                                ace_types::Job_s2a::SystemInfo(info) => {
                                    system_info.tx_s2a.send((info, s2a_message.msg_id, s2a_message.timestamp)).expect("boo");
                                }
                            }
                        },
                        Err(e) => {
                            println!("Error receiving message: {e:?}");
                            println!("Closing connection");
                            ws1.close().await;
                            ws0 = None;
                        },
                    }
                }
                msg = system_info.rx_a2s.recv() => {
                    if let Some(msg) = msg {
                        match ws1.send(&msg).await {
                            Ok(_) => println!("Sent message to server: {msg:?}"),
                            Err(e) => {
                                println!("Error sending message: {e:?}");
                                println!("Re-queueing message");
                                system_info.tx_a2s.send(msg).expect("boo");
                            }
                        }

                    }
                }

                result = main_receiver.recv() => {
                    if let Some(result) = result {
                        println!("Sent instruction response: {result:?}");
                        if let Err(e) = ws1.send(&result).await {
                            println!("Error sending message: {e:?}");
                        };
                    }
                }
            }
        }
        // could not connect to websocket
        else {
            if delay_interval < 15 {
                delay_interval += 1;
            }
        }
    }
}

async fn respond_to_ping(
    ping_respond_channel: mpsc::UnboundedSender<Message_a2s>,
    original_msg: ace_types::Message_s2a,
) {
    let a2s_pong = ace_types::Message_a2s {
        msg_id: original_msg.msg_id,
        job: ace_types::Job_a2s::Pong,
        timestamp: original_msg.timestamp,
        msg_type: ace_types::Message_Type::Reply,
    };

    match ping_respond_channel.send(a2s_pong) {
        Ok(_) => (),
        Err(e) => println!("Error sending task message to central agent: {e:?}"),
    };
}
