[package]
name = "candoc-app"
version = "0.1.0"
edition = "2021"
rust-version = "1.79"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[package.metadata.acp]
app.port = 8000

extends = ["bux", "candoc", "candoc-user", "candoc-public", "candoc-bux", "candoc-approck", "approck", "granite", "auth-fence"]

[dependencies]
approck = { workspace = true }
auth-fence = { workspace = true }
bux = { workspace = true }
granite = { workspace = true }
granite-redis = { workspace = true }
granite-postgres = { workspace = true }
candoc = { path = "../candoc" }
candoc-public = { path = "../candoc-public" }
candoc-user = { path = "../candoc-user" }
candoc-bux = { path = "../candoc-bux" }
candoc-approck = { path = "../candoc-approck" }


clap = { workspace = true, features = ["derive"] }
tokio = { workspace = true, features = ["full"] }
maud = { workspace = true }
serde = { workspace = true, features = ["derive"] }
toml = { workspace = true }
