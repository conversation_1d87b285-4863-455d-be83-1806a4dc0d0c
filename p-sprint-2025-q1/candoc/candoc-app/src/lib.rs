#[path = "libλ.rs"]
mod libλ;

mod module;
mod web;

// It makes sense to define documents in the web dir, but they should be referred to from here.
pub use crate::web::Document::Document;

// hack workaround until we move the framework to look at crate::Document*
pub mod types {
    pub use crate::AppConfig;
    pub use crate::AppIdentity;
    pub use crate::AppSystem;
    pub use crate::Document;
}

#[approck::appconfig]
pub struct AppConfig {
    pub redis: granite_redis::RedisConfig,
    pub postgres: granite_postgres::PostgresConfig,
    pub webserver: approck::server::WebServerConfig,
    pub auth_fence: auth_fence::types::ModConfig,
}

pub struct AppSystem {
    pub webserver: approck::server::WebServerSystem,
    pub postgres: granite_postgres::PostgresSystem,
    pub redis: granite_redis::RedisSystem,
    pub auth_fence: auth_fence::types::ModSystem,
}

#[derive(Debug)]
pub enum AppIdentity {
    Anonymous,
    User(auth_fence::identity::Identity),
}

pub async fn init(_app: &'static AppSystem) {
    // get the crate name using the env! macro
    let crate_name = env!("CARGO_PKG_NAME");
    println!("init: {}", crate_name);
}

pub async fn authenticate_request(
    app: &'static AppSystem,
    req: &approck::server::Request,
) -> granite::Result<AppIdentity> {
    use auth_fence::AppSystem;
    let auth_fence = app.auth_fence_system();

    let mut redis = match app.redis.get_dbcx().await {
        Ok(redis) => redis,
        Err(e) => {
            return Err(granite::process_error!(
                "Error getting Redis connection: {}",
                e
            ));
        }
    };

    match auth_fence
        .get_user_identity(&req.session_token(), &mut redis)
        .await
    {
        Ok(user_info) => match user_info {
            Some(user_info) => Ok(AppIdentity::User(user_info)),
            None => Ok(AppIdentity::Anonymous),
        },
        Err(_e) => Ok(AppIdentity::Anonymous),
    }
}
