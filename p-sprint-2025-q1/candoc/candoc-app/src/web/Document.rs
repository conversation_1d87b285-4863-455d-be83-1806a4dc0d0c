use auth_fence::AppSystem;
use bux::html;

bux::document! {

    pub struct Document {}

    impl Document {
        pub fn new(
            app: &'static crate::AppSystem,
            identity: &crate::AppIdentity,
            req: &approck::server::Request,
        ) -> Self {
            // trait Nav2 must be in scope for set_identity() and nav2_menu_add()
            use bux::document::{Nav1, Nav2, Nav2Dropdown, Nav2Logo};

            println!("Request {:?}", req.path());

            let mut this = Self {
                ..Default::default()
            };

            // Base setup



            // Add Nav1 menu links
            this.add_nav1_menu_link("App Directory", "/", "");
            this.add_nav1_menu_link("Bux Documentation", "/doc", "");
            this.add_nav1_menu_link("Approck Documentation", "/approck", "");

            // Nav2 setup
            this.set_identity(identity);

            match candoc_user::AppIdentity::is_logged_in(identity) {
                true => {
                    if let Some(name) = bux::AppIdentity::name(identity) {
                        let user_menu: &mut Nav2Dropdown;
                        if let Some(avatar_uri) = bux::AppIdentity::avatar_uri(identity) {
                            let user_logo = Nav2Logo { url: avatar_uri, alt: None };
                            user_menu = this.add_nav2_menu_dropdown(&name, "user-menu", Some(user_logo), None);
                        } else {
                            user_menu = this.add_nav2_menu_dropdown(&name, "user-menu", None, None);
                        }
                        user_menu.add_link("Dashboard", "/dashboard/", None);
                        user_menu.add_span(format!("Logged in as {}", name).as_str());
                        user_menu.add_link("Logout", auth_fence::AppSystem::logout_url(app), None);
                    };
                }
                false => {
                    this.add_nav2_menu_link("Login", app.login_url(), "");
                }
            }

            this
        }
    }


    impl bux::document::Base for Document {
        fn render_body(&self) -> maud::Markup {
            use bux::document::{Base, Nav1, Nav2};
            use maud::{html, PreEscaped};
            html!(
                layout-wrapper-outer {
                    layout-wrapper-inner {
                        header-bar.disabled id="header-bar" {}
                        nav-wrapper {
                            content-container {
                                nav-header id="horizontal-nav-header" {
                                    a class="home-link" href="/" { i class="fa fa-home" {} }
                                }
                                (Nav1::render_nav1(self))
                                (Nav2::render(self))
                            }
                        }
                        content-container {
                            (Base::render_body_inner(self))
                        }
                    }
                    footer {
                        p id="footer-copyright" {
                            small {
                                (PreEscaped("&copy;"))
                                "AppCove 2024"
                            }
                        }
                    }
                }

            )
        }
    }
    impl bux::document::Nav1 for Document {}
    impl bux::document::Nav2 for Document {}
    impl bux::document::FooterSocial for Document {}
    impl bux::document::FooterLinks for Document {}
    impl auth_fence::Document for Document {}
    impl bux::document::PageNav for Document {}

}
