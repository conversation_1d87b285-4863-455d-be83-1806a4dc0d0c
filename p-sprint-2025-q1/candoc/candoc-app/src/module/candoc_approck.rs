use crate::{types::Document, AppIdentity, AppSystem};

impl candoc_approck::AppSystem for AppSystem {}

impl candoc_approck::AppIdentity for AppIdentity {
    fn usage(&self) -> bool {
        false
    }
    fn is_logged_in(&self) -> bool {
        match self {
            AppIdentity::Anonymous => false,
            AppIdentity::User(_) => true,
        }
    }
}

impl candoc_approck::Document for Document {}
