use crate::{AppIdentity, AppSystem};

impl auth_fence::AppIdentity for AppIdentity {}

impl auth_fence::AppSystem for AppSystem {
    fn after_login_next_url<'a>(&self, next_uri: Option<&'a str>) -> &'a str {
        match next_uri {
            Some(next_uri) => next_uri,
            None => "/dashboard/",
        }
    }
    fn auth_fence_system(&self) -> &auth_fence::types::ModSystem {
        &self.auth_fence
    }
}
