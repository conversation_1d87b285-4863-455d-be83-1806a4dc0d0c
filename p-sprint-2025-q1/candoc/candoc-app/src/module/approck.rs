use crate::{AppIdentity, AppSystem};

impl approck::server::WebServerModule for AppSystem {
    fn webserver_system(&self) -> &approck::server::WebServerSystem {
        &self.webserver
    }

    async fn webserver_route(
        &'static self,
        req: approck::server::Request,
    ) -> granite::Result<approck::server::response::Response> {
        crate::libλ::router(self, req).await
    }
}

impl approck::AppIdentity for AppIdentity {}
