use crate::{types::Document, AppIdentity, AppSystem};

impl candoc_user::AppSystem for AppSystem {}

impl candoc_user::AppIdentity for AppIdentity {
    fn usage(&self) -> bool {
        match self {
            AppIdentity::Anonymous => false,
            AppIdentity::User(_) => true,
        }
    }
    fn is_logged_in(&self) -> bool {
        match self {
            AppIdentity::Anonymous => false,
            AppIdentity::User(_) => true,
        }
    }
}

impl candoc_user::Document for Document {}
