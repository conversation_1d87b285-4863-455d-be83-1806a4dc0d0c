use crate::AppIdentity;

impl bux::AppIdentity for AppIdentity {
    fn role(&self) -> String {
        match self {
            AppIdentity::User(_) => "user".to_string(),
            AppIdentity::Anonymous => "anonymous".to_string(),
        }
    }
    fn name(&self) -> Option<String> {
        match self {
            AppIdentity::User(user_info) => Some(user_info.name.to_string()),
            AppIdentity::Anonymous => None,
        }
    }
    fn email(&self) -> Option<String> {
        match self {
            AppIdentity::User(user_info) => user_info.email.clone(),
            AppIdentity::Anonymous => None,
        }
    }
    fn avatar_uri(&self) -> Option<String> {
        match self {
            AppIdentity::User(user_info) => user_info.avatar_uri.clone(),
            AppIdentity::Anonymous => None,
        }
    }
}
