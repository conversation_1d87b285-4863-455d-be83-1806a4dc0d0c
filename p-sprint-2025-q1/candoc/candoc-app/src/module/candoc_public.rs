use crate::{types::Document, AppIdentity, AppSystem};

impl candoc_public::AppSystem for AppSystem {}

impl candoc_public::AppIdentity for AppIdentity {
    fn usage(&self) -> bool {
        false
    }
    fn is_logged_in(&self) -> bool {
        match self {
            AppIdentity::Anonymous => false,
            AppIdentity::User(_) => true,
        }
    }
}

impl candoc_public::Document for Document {}
