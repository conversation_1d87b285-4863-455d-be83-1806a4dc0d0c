#[approck::http(GET /approck; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Approck Documentation");

        doc.add_body(html!(

            panel {
                div {
                    h1 {"Approck Documentation"}
                    ul {
                        li { a href="/" { "Home"} }
                        li { a href="/approck/designation" { "Path Designation"} }
                        li { a href="/approck/querystring1" { "Query String 1"} }
                        li { a href="/approck/querystring2" { "Query String 2"} }
                        li { a href="/approck/querystring3" { "Query String 3"} }
                        li { a href="/approck/querystring4" { "Query String 4"} }
                        li { a href="/approck/post1" { "Post 1" } }
                    }
                }
            }

            cell-9 {
                panel {
                    content {"Official Approck Documentation Site"}
                }
            }
        ));

        Response::HTML(doc.into())
    }
}
