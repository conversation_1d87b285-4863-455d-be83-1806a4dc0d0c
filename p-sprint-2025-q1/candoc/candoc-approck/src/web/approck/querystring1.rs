#[approck::http(GET /approck/querystring1?a=Option<i32>&b=Option<u32>&c=Option<String>; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(req: Request, ui: Document, qs: QueryString) -> Response {
        #[rustfmt::skip]
        ui.add_body(maud::html! {
            panel {
                a href="/approck" { "◀ Back to approck List" }

                h1 { code {  (req.path()) } }

                ul {
                    li { "Query String a: " (format!("{:?}", qs.a)) }
                    li { "Query String b: " (format!("{:?}", qs.b)) }
                    li { "Query String c: " (format!("{:?}", qs.c)) }
                }

                hr;
                ul{
                    li {a href="/approck/querystring1" { "/approck/querystring1" } }
                }
                hr;
                ul {
                    li {a href="/approck/querystring1?a=1" { "/approck/querystring1?a=1" } }
                    li {a href="/approck/querystring1?a=1&b=2" { "/approck/querystring1?a=1&b=2" } }
                    li {a href="/approck/querystring1?a=1&b=2&c=3" { "/approck/querystring1?a=1&b=2&c=3" } }
                    li {a href="/approck/querystring1?a=1&b=2&c=256" { "/approck/querystring1?a=1&b=2&c=256" } }
                }
            }
        });

        Response::HTML(ui.into())
    }
}
