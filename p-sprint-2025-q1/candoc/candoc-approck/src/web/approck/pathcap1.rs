#[approck::http(GET /approck/pathcap1/{pc:String}; AUTH None; return HTML;)]
pub mod page_with_capture {
    pub async fn request(req: Request, ui: Document, path: Path) -> Response {
        match super::page::request(req, ui, Some(path.pc)).await {
            super::page::Response::HTML(html) => Response::HTML(html),
        }
    }
}

#[approck::http(GET /approck/pathcap1; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(req: Request, ui: Document, pc: Option<String>) -> Response {
        #[rustfmt::skip]
        ui.add_body(maud::html! {
            panel {
                a href="/approck" { "◂ Back to approck List" }
                h1 { code { (req.path()) } }

                @match &pc {
                    Some(pc) => {
                        p { "Captured Path: " (pc) }
                    }
                    None => {
                        p { "Captured Path: None" }
                    }
                }

                hr;
                ul{
                    li {a href="/approck/pathcap1" { "/approck/pathcap1" } }
                }
                hr;
                ul {
                    li {a href="/approck/pathcap1/a" { "/approck/pathcap1/a" } }
                    li {a href="/approck/pathcap1/b" { "/approck/pathcap1/b" } }
                    li {a href="/approck/pathcap1/c" { "/approck/pathcap1/c" } }
                    li {a href="/approck/querystring1" { "/approck/querystring1" } }
                }


            }
        });

        Response::HTML(ui.into())
    }
}
