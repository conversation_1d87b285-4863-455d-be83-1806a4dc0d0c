#[approck::http(GET /approck/designation; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Designation");

        doc.add_body(html!(
            div {
                grid-12 {
                    cell-3 {

                    }
                    cell-9 {
                        panel {
                            content {
                                "This is the documentation page for path designation"
                            }
                        }
                    }
                }
            }
        ));

        Response::HTML(doc.into())
    }
}
